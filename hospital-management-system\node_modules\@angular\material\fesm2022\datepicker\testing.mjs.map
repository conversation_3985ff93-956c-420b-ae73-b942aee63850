{"version": 3, "file": "testing.mjs", "sources": ["../../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/material/datepicker/testing/datepicker-toggle-harness.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {HarnessPredicate} from '@angular/cdk/testing';\nimport {coerceBooleanProperty} from '@angular/cdk/coercion';\nimport {DatepickerToggleHarnessFilters} from './datepicker-harness-filters';\nimport {DatepickerTriggerHarnessBase} from './datepicker-trigger-harness-base';\n\n/** Harness for interacting with a standard Material datepicker toggle in tests. */\nexport class MatDatepickerToggleHarness extends DatepickerTriggerHarnessBase {\n  static hostSelector = '.mat-datepicker-toggle';\n\n  /** The clickable button inside the toggle. */\n  private _button = this.locatorFor('button');\n\n  /**\n   * Gets a `HarnessPredicate` that can be used to search for a `MatDatepickerToggleHarness` that\n   * meets certain criteria.\n   * @param options Options for filtering which datepicker toggle instances are considered a match.\n   * @return a `HarnessPredicate` configured with the given options.\n   */\n  static with(\n    options: DatepickerToggleHarnessFilters = {},\n  ): HarnessPredicate<MatDatepickerToggleHarness> {\n    return new HarnessPredicate(MatDatepickerToggleHarness, options);\n  }\n\n  /** Gets whether the calendar associated with the toggle is open. */\n  async isCalendarOpen(): Promise<boolean> {\n    return (await this.host()).hasClass('mat-datepicker-toggle-active');\n  }\n\n  /** Whether the toggle is disabled. */\n  async isDisabled(): Promise<boolean> {\n    const button = await this._button();\n    return coerceBooleanProperty(await button.getAttribute('disabled'));\n  }\n\n  protected async _openCalendar(): Promise<void> {\n    return (await this._button()).click();\n  }\n}\n"], "names": [], "mappings": ";;;;;;AAaA;AACM,MAAO,0BAA2B,SAAQ,4BAA4B,CAAA;AAC1E,IAAA,OAAO,YAAY,GAAG,wBAAwB;;AAGtC,IAAA,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC;AAE3C;;;;;AAKG;AACH,IAAA,OAAO,IAAI,CACT,OAAA,GAA0C,EAAE,EAAA;AAE5C,QAAA,OAAO,IAAI,gBAAgB,CAAC,0BAA0B,EAAE,OAAO,CAAC;;;AAIlE,IAAA,MAAM,cAAc,GAAA;AAClB,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,QAAQ,CAAC,8BAA8B,CAAC;;;AAIrE,IAAA,MAAM,UAAU,GAAA;AACd,QAAA,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,OAAO,EAAE;QACnC,OAAO,qBAAqB,CAAC,MAAM,MAAM,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;;AAG3D,IAAA,MAAM,aAAa,GAAA;QAC3B,OAAO,CAAC,MAAM,IAAI,CAAC,OAAO,EAAE,EAAE,KAAK,EAAE;;;;;;"}