import { Observable } from 'rxjs';
import { NgControl, AbstractControlDirective } from '@angular/forms';
import * as i0 from '@angular/core';

/** An interface which allows a control to work inside of a `MatFormField`. */
declare abstract class MatFormFieldControl<T> {
    /** The value of the control. */
    value: T | null;
    /**
     * Stream that emits whenever the state of the control changes such that the parent `MatForm<PERSON>ield`
     * needs to run change detection.
     */
    readonly stateChanges: Observable<void>;
    /** The element ID for this control. */
    readonly id: string;
    /** The placeholder for this control. */
    readonly placeholder: string;
    /** Gets the AbstractControlDirective for this control. */
    readonly ngControl: NgControl | AbstractControlDirective | null;
    /** Whether the control is focused. */
    readonly focused: boolean;
    /** Whether the control is empty. */
    readonly empty: boolean;
    /** Whether the `MatFormField` label should try to float. */
    readonly shouldLabelFloat: boolean;
    /** Whether the control is required. */
    readonly required: boolean;
    /** Whether the control is disabled. */
    readonly disabled: boolean;
    /** Whether the control is in an error state. */
    readonly errorState: boolean;
    /**
     * An optional name for the control type that can be used to distinguish `mat-form-field` elements
     * based on their control type. The form field will add a class,
     * `mat-form-field-type-{{controlType}}` to its root element.
     */
    readonly controlType?: string;
    /**
     * Whether the input is currently in an autofilled state. If property is not present on the
     * control it is assumed to be false.
     */
    readonly autofilled?: boolean;
    /**
     * Value of `aria-describedby` that should be merged with the described-by ids
     * which are set by the form-field.
     */
    readonly userAriaDescribedBy?: string;
    /**
     * Whether to automatically assign the ID of the form field as the `for` attribute
     * on the `<label>` inside the form field. Set this to true to prevent the form
     * field from associating the label with non-native elements.
     */
    readonly disableAutomaticLabeling?: boolean;
    /** Gets the list of element IDs that currently describe this control. */
    readonly describedByIds?: string[];
    /** Sets the list of element IDs that currently describe this control. */
    abstract setDescribedByIds(ids: string[]): void;
    /** Handles a click on the control's container. */
    abstract onContainerClick(event: MouseEvent): void;
    static ɵfac: i0.ɵɵFactoryDeclaration<MatFormFieldControl<any>, never>;
    static ɵdir: i0.ɵɵDirectiveDeclaration<MatFormFieldControl<any>, never, never, {}, {}, never, never, true, never>;
}

export { MatFormFieldControl as M };
