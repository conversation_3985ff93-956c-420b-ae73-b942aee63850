{"version": 3, "file": "testing.mjs", "sources": ["../../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/material/progress-spinner/testing/progress-spinner-harness.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {coerceNumberProperty} from '@angular/cdk/coercion';\nimport {\n  ComponentHarness,\n  ComponentHarnessConstructor,\n  HarnessPredicate,\n} from '@angular/cdk/testing';\nimport {ProgressSpinnerMode} from '../progress-spinner';\nimport {ProgressSpinnerHarnessFilters} from './progress-spinner-harness-filters';\n\n/** Harness for interacting with a MDC based mat-progress-spinner in tests. */\nexport class MatProgressSpinnerHarness extends ComponentHarness {\n  /** The selector for the host element of a `MatProgressSpinner` instance. */\n  static hostSelector = '.mat-mdc-progress-spinner';\n\n  /**\n   * Gets a `HarnessPredicate` that can be used to search for a progress spinnner with specific\n   * attributes.\n   * @param options Options for filtering which progress spinner instances are considered a match.\n   * @return a `HarnessPredicate` configured with the given options.\n   */\n  static with<T extends MatProgressSpinnerHarness>(\n    this: ComponentHarnessConstructor<T>,\n    options: ProgressSpinnerHarnessFilters = {},\n  ): HarnessPredicate<T> {\n    return new HarnessPredicate(this, options);\n  }\n\n  /** Gets the progress spinner's value. */\n  async getValue(): Promise<number | null> {\n    const host = await this.host();\n    const ariaValue = await host.getAttribute('aria-valuenow');\n    return ariaValue ? coerceNumberProperty(ariaValue) : null;\n  }\n\n  /** Gets the progress spinner's mode. */\n  async getMode(): Promise<ProgressSpinnerMode> {\n    const modeAttr = (await this.host()).getAttribute('mode');\n    return (await modeAttr) as ProgressSpinnerMode;\n  }\n}\n"], "names": [], "mappings": ";;;AAiBA;AACM,MAAO,yBAA0B,SAAQ,gBAAgB,CAAA;;AAE7D,IAAA,OAAO,YAAY,GAAG,2BAA2B;AAEjD;;;;;AAKG;AACH,IAAA,OAAO,IAAI,CAET,OAAA,GAAyC,EAAE,EAAA;AAE3C,QAAA,OAAO,IAAI,gBAAgB,CAAC,IAAI,EAAE,OAAO,CAAC;;;AAI5C,IAAA,MAAM,QAAQ,GAAA;AACZ,QAAA,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE;QAC9B,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC;AAC1D,QAAA,OAAO,SAAS,GAAG,oBAAoB,CAAC,SAAS,CAAC,GAAG,IAAI;;;AAI3D,IAAA,MAAM,OAAO,GAAA;AACX,QAAA,MAAM,QAAQ,GAAG,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,YAAY,CAAC,MAAM,CAAC;AACzD,QAAA,QAAQ,MAAM,QAAQ;;;;;;"}