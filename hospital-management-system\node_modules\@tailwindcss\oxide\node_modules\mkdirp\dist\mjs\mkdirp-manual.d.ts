import { MkdirpOptions } from './opts-arg.js';
export declare const mkdirpManualSync: (path: string, options?: MkdirpOptions, made?: string | undefined | void) => string | undefined | void;
export declare const mkdirpManual: ((path: string, options?: MkdirpOptions, made?: string | undefined | void) => Promise<string | undefined | void>) & {
    sync: (path: string, options?: MkdirpOptions, made?: string | undefined | void) => string | undefined | void;
};
//# sourceMappingURL=mkdirp-manual.d.ts.map