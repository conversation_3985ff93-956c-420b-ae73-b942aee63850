import * as i0 from '@angular/core';
import { MatButtonModule } from '../button/index.js';
import { M as MatSelectModule } from '../module.d-bebo7gS5.js';
import { M as MatTooltipModule } from '../module.d-m-qXd3m8.js';
import { M as MatPaginator } from '../paginator.d-DuJ-oYgT.js';
export { c as MAT_PAGINATOR_DEFAULT_OPTIONS, f as MAT_PAGINATOR_INTL_PROVIDER, e as MAT_PAGINATOR_INTL_PROVIDER_FACTORY, b as MatPaginatorDefaultOptions, d as MatPaginatorIntl, a as MatPaginatorSelectConfig, P as PageEvent } from '../paginator.d-DuJ-oYgT.js';
import '@angular/cdk/a11y';
import '../palette.d-BSSFKjO6.js';
import '../ripple-loader.d-9me-KFSi.js';
import '../common-module.d-C8xzHJDr.js';
import '@angular/cdk/bidi';
import '../index.d-C5neTPvr.js';
import '../ripple.d-BT30YVLB.js';
import '@angular/cdk/platform';
import '@angular/cdk/overlay';
import '../index.d-DAhBYbjm.js';
import '../pseudo-checkbox-module.d-BHmTZ10P.js';
import '../option.d-BcvS44bt.js';
import 'rxjs';
import '@angular/cdk/collections';
import '@angular/cdk/scrolling';
import '@angular/forms';
import '../error-options.d-CGdTZUYk.js';
import '../form-field.d-C6p5uYjG.js';
import '@angular/cdk/coercion';
import '../form-field-control.d-DvB4ZVlf.js';
import '../module.d-D1Ym5Wf2.js';
import '@angular/cdk/observers';

declare class MatPaginatorModule {
    static ɵfac: i0.ɵɵFactoryDeclaration<MatPaginatorModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<MatPaginatorModule, never, [typeof MatButtonModule, typeof MatSelectModule, typeof MatTooltipModule, typeof MatPaginator], [typeof MatPaginator]>;
    static ɵinj: i0.ɵɵInjectorDeclaration<MatPaginatorModule>;
}

export { MatPaginator, MatPaginatorModule };
