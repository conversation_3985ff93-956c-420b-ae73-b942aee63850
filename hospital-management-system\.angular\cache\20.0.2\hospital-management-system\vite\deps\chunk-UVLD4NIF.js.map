{"version": 3, "sources": ["../../../../../../node_modules/@angular/cdk/fesm2022/layout.mjs", "../../../../../../node_modules/@angular/material/fesm2022/animation-DfMFjxHu.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { NgModule } from '@angular/core';\nexport { B as BreakpointObserver, M as MediaMatcher } from './breakpoints-observer-QutrMj4x.mjs';\nimport 'rxjs';\nimport 'rxjs/operators';\nimport './platform-DNDzkVcI.mjs';\nimport '@angular/common';\nimport './array-I1yfCXUO.mjs';\nclass LayoutModule {\n  static ɵfac = function LayoutModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || LayoutModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: LayoutModule\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(LayoutModule, [{\n    type: NgModule,\n    args: [{}]\n  }], null, null);\n})();\n\n// PascalCase is being used as Breakpoints is used like an enum.\n// tslint:disable-next-line:variable-name\nconst Breakpoints = {\n  XSmall: '(max-width: 599.98px)',\n  Small: '(min-width: 600px) and (max-width: 959.98px)',\n  Medium: '(min-width: 960px) and (max-width: 1279.98px)',\n  Large: '(min-width: 1280px) and (max-width: 1919.98px)',\n  XLarge: '(min-width: 1920px)',\n  Handset: '(max-width: 599.98px) and (orientation: portrait), ' + '(max-width: 959.98px) and (orientation: landscape)',\n  Tablet: '(min-width: 600px) and (max-width: 839.98px) and (orientation: portrait), ' + '(min-width: 960px) and (max-width: 1279.98px) and (orientation: landscape)',\n  Web: '(min-width: 840px) and (orientation: portrait), ' + '(min-width: 1280px) and (orientation: landscape)',\n  HandsetPortrait: '(max-width: 599.98px) and (orientation: portrait)',\n  TabletPortrait: '(min-width: 600px) and (max-width: 839.98px) and (orientation: portrait)',\n  WebPortrait: '(min-width: 840px) and (orientation: portrait)',\n  HandsetLandscape: '(max-width: 959.98px) and (orientation: landscape)',\n  TabletLandscape: '(min-width: 960px) and (max-width: 1279.98px) and (orientation: landscape)',\n  WebLandscape: '(min-width: 1280px) and (orientation: landscape)'\n};\nexport { Breakpoints, LayoutModule };\n", "import { MediaMatcher } from '@angular/cdk/layout';\nimport { InjectionToken, inject, ANIMATION_MODULE_TYPE } from '@angular/core';\n\n/** Injection token used to configure the animations in Angular Material. */\nconst MATERIAL_ANIMATIONS = new InjectionToken('MATERIAL_ANIMATIONS');\n/**\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n * @docs-private\n */\nclass AnimationCurves {\n    static STANDARD_CURVE = 'cubic-bezier(0.4,0.0,0.2,1)';\n    static DECELERATION_CURVE = 'cubic-bezier(0.0,0.0,0.2,1)';\n    static ACCELERATION_CURVE = 'cubic-bezier(0.4,0.0,1,1)';\n    static SHARP_CURVE = 'cubic-bezier(0.4,0.0,0.6,1)';\n}\n/**\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n * @docs-private\n */\nclass AnimationDurations {\n    static COMPLEX = '375ms';\n    static ENTERING = '225ms';\n    static EXITING = '195ms';\n}\n/**\n * Returns whether animations have been disabled by DI. Must be called in a DI context.\n * @docs-private\n */\nfunction _animationsDisabled() {\n    if (inject(MATERIAL_ANIMATIONS, { optional: true })?.animationsDisabled ||\n        inject(ANIMATION_MODULE_TYPE, { optional: true }) === 'NoopAnimations') {\n        return true;\n    }\n    const mediaMatcher = inject(MediaMatcher);\n    return mediaMatcher.matchMedia('(prefers-reduced-motion)').matches;\n}\n\nexport { AnimationCurves as A, MATERIAL_ANIMATIONS as M, _animationsDisabled as _, AnimationDurations as a };\n\n"], "mappings": ";;;;;;;;;;;;;;AAQA,IAAM,eAAN,MAAM,cAAa;AAAA,EACjB,OAAO,OAAO,SAAS,qBAAqB,mBAAmB;AAC7D,WAAO,KAAK,qBAAqB,eAAc;AAAA,EACjD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,EACR,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB,CAAC,CAAC;AACrD;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,IACN,MAAM,CAAC,CAAC,CAAC;AAAA,EACX,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AAIH,IAAM,cAAc;AAAA,EAClB,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,KAAK;AAAA,EACL,iBAAiB;AAAA,EACjB,gBAAgB;AAAA,EAChB,aAAa;AAAA,EACb,kBAAkB;AAAA,EAClB,iBAAiB;AAAA,EACjB,cAAc;AAChB;;;ACrCA,IAAM,sBAAsB,IAAI,eAAe,qBAAqB;AAMpE,IAAM,kBAAN,MAAsB;AAAA,EAClB,OAAO,iBAAiB;AAAA,EACxB,OAAO,qBAAqB;AAAA,EAC5B,OAAO,qBAAqB;AAAA,EAC5B,OAAO,cAAc;AACzB;AAMA,IAAM,qBAAN,MAAyB;AAAA,EACrB,OAAO,UAAU;AAAA,EACjB,OAAO,WAAW;AAAA,EAClB,OAAO,UAAU;AACrB;AAKA,SAAS,sBAAsB;AAC3B,MAAI,OAAO,qBAAqB,EAAE,UAAU,KAAK,CAAC,GAAG,sBACjD,OAAO,uBAAuB,EAAE,UAAU,KAAK,CAAC,MAAM,kBAAkB;AACxE,WAAO;AAAA,EACX;AACA,QAAM,eAAe,OAAO,YAAY;AACxC,SAAO,aAAa,WAAW,0BAA0B,EAAE;AAC/D;", "names": []}