import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';

const routes: Routes = [
  { path: '', redirectTo: '/dashboard', pathMatch: 'full' },
  {
    path: 'auth',
    loadChildren: () => import('./auth/auth-module').then(m => m.AuthModule)
  },
  {
    path: 'dashboard',
    loadChildren: () => import('./dashboard/dashboard-module').then(m => m.DashboardModule)
  },
  {
    path: 'patients',
    loadChildren: () => import('./patients/patients-module').then(m => m.PatientsModule)
  },
  {
    path: 'doctors',
    loadChildren: () => import('./doctors/doctors-module').then(m => m.DoctorsModule)
  },
  {
    path: 'appointments',
    loadChildren: () => import('./appointments/appointments-module').then(m => m.AppointmentsModule)
  },
  {
    path: 'billing',
    loadChildren: () => import('./billing/billing-module').then(m => m.BillingModule)
  },
  { path: '**', redirectTo: '/dashboard' }
];

@NgModule({
  imports: [RouterModule.forRoot(routes)],
  exports: [RouterModule]
})
export class AppRoutingModule { }
