@use 'sass:map';
@use '../core/tokens/m2-utils';
@use '../core/theming/theming';
@use '../core/theming/inspection';
@use '../core/style/sass-utils';

// Tokens that can't be configured through Angular Material's current theming API,
// but may be in a future version of the theming API.
@function get-unthemable-tokens() {
  @return (
    stepper-header-focus-state-layer-shape: 0,
    stepper-header-hover-state-layer-shape: 0,
  );
}

// Tokens that can be configured through Angular Material's color theming API.
@function get-color-tokens($theme) {
  @return map.merge(private-get-color-palette-color-tokens($theme, primary), (
    stepper-container-color: inspection.get-theme-color($theme, background, card),
    stepper-line-color: inspection.get-theme-color($theme, foreground, divider),
    stepper-header-hover-state-layer-color: inspection.get-theme-color($theme, background, hover),
    stepper-header-focus-state-layer-color: inspection.get-theme-color($theme, background, hover),
    stepper-header-label-text-color: inspection.get-theme-color($theme, foreground, secondary-text),
    stepper-header-optional-label-text-color: inspection.get-theme-color(
        $theme, foreground, secondary-text),
    stepper-header-selected-state-label-text-color:
        inspection.get-theme-color($theme, foreground, text),
    stepper-header-error-state-label-text-color: inspection.get-theme-color($theme, warn, text),
    stepper-header-icon-background-color:
        inspection.get-theme-color($theme, foreground, secondary-text),
    stepper-header-error-state-icon-foreground-color:
        inspection.get-theme-color($theme, warn, text),
    stepper-header-error-state-icon-background-color: transparent,
  ));
}

// Tokens that can be configured through Angular Material's typography theming API.
@function get-typography-tokens($theme) {
  @return (
    stepper-container-text-font: inspection.get-theme-typography($theme, body-2, font-family),
    stepper-header-label-text-font: inspection.get-theme-typography($theme, body-2, font-family),
    stepper-header-label-text-size: inspection.get-theme-typography($theme, body-2, font-size),
    stepper-header-label-text-weight: inspection.get-theme-typography($theme, body-2, font-weight),
    stepper-header-error-state-label-text-size:
        inspection.get-theme-typography($theme, body-1, font-size),
    stepper-header-selected-state-label-text-size: inspection.get-theme-typography(
        $theme, body-1, font-size),
    stepper-header-selected-state-label-text-weight: inspection.get-theme-typography(
        $theme, body-1, font-weight),
  );
}

// Tokens that can be configured through Angular Material's density theming API.
@function get-density-tokens($theme) {
  $scale: theming.clamp-density(inspection.get-theme-density($theme), -4);
  $height-config: (
    0: 72px,
    -1: 68px,
    -2: 64px,
    -3: 60px,
    -4: 42px,
  );

  @return (
    stepper-header-height: map.get($height-config, $scale),
  );
}

// Generates the tokens used to theme the stepper based on a palette.
@function private-get-color-palette-color-tokens($theme, $palette-name) {
  $active-state-foreground: inspection.get-theme-color($theme, $palette-name, default-contrast);
  $active-state-background: inspection.get-theme-color($theme, $palette-name);

  @return (
    stepper-header-icon-foreground-color: $active-state-foreground,
    stepper-header-selected-state-icon-background-color: $active-state-background,
    stepper-header-selected-state-icon-foreground-color: $active-state-foreground,
    stepper-header-done-state-icon-background-color: $active-state-background,
    stepper-header-done-state-icon-foreground-color: $active-state-foreground,
    stepper-header-edit-state-icon-background-color: $active-state-background,
    stepper-header-edit-state-icon-foreground-color: $active-state-foreground,
  );
}

// Combines the tokens generated by the above functions into a single map with placeholder values.
// This is used to create token slots.
@function get-token-slots() {
  @return sass-utils.deep-merge-all(
      get-unthemable-tokens(),
      get-color-tokens(m2-utils.$placeholder-color-config),
      get-typography-tokens(m2-utils.$placeholder-typography-config),
      get-density-tokens(m2-utils.$placeholder-density-config)
  );
}
