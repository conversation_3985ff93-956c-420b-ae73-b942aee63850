/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
import { VersionChanges } from '../../update-tool/version-changes';
export interface CssTokenUpgradeData {
    /** The CSS selector to replace. */
    replace: string;
    /** The new CSS selector. */
    replaceWith: string;
    /**
     * Controls which file types in which this replacement is made. If omitted, it is made in all
     * files.
     */
    replaceIn?: {
        /** Replace this name in stylesheet files. */
        stylesheet?: boolean;
        /** Replace this name in HTML files. */
        html?: boolean;
        /** Replace this name in TypeScript strings. */
        tsStringLiterals?: boolean;
    };
}
export declare const cssTokens: VersionChanges<CssTokenUpgradeData>;
