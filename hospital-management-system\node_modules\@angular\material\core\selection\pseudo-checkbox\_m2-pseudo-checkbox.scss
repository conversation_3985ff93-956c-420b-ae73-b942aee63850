@use '../../tokens/m2-utils';
@use '../../theming/inspection';
@use '../../style/sass-utils';

// Tokens that can't be configured through Angular Material's current theming API,
// but may be in a future version of the theming API.
@function get-unthemable-tokens() {
  @return ();
}

// Tokens that can be configured through Angular Material's color theming API.
@function get-color-tokens($theme, $palette-name: accent) {
  $is-dark: inspection.get-theme-type($theme) == dark;
  $disabled-color: if($is-dark, #686868, #b0b0b0);
  $checkmark-color: inspection.get-theme-color($theme, background, background);

  @return (
    pseudo-checkbox-full-selected-icon-color: inspection.get-theme-color($theme, $palette-name),
    pseudo-checkbox-full-selected-checkmark-color: $checkmark-color,
    pseudo-checkbox-full-unselected-icon-color:
        inspection.get-theme-color($theme, foreground, secondary-text),
    pseudo-checkbox-full-disabled-selected-checkmark-color: $checkmark-color,
    pseudo-checkbox-full-disabled-unselected-icon-color: $disabled-color,
    pseudo-checkbox-full-disabled-selected-icon-color: $disabled-color,
    pseudo-checkbox-minimal-selected-checkmark-color:
        inspection.get-theme-color($theme, $palette-name),
    pseudo-checkbox-minimal-disabled-selected-checkmark-color: if($is-dark, #686868, #b0b0b0),
  );
}

// Tokens that can be configured through Angular Material's typography theming API.
@function get-typography-tokens($theme) {
  @return ();
}

// Tokens that can be configured through Angular Material's density theming API.
@function get-density-tokens($theme) {
  @return ();
}

// Combines the tokens generated by the above functions into a single map with placeholder values.
// This is used to create token slots.
@function get-token-slots() {
  @return sass-utils.deep-merge-all(
      get-unthemable-tokens(),
      get-color-tokens(m2-utils.$placeholder-color-config),
      get-typography-tokens(m2-utils.$placeholder-typography-config),
      get-density-tokens(m2-utils.$placeholder-density-config)
  );
}
