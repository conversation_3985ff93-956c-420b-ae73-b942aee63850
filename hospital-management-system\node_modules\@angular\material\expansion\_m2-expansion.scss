@use 'sass:map';
@use '../core/tokens/m2-utils';
@use '../core/theming/theming';
@use '../core/theming/inspection';
@use '../core/style/sass-utils';

// Tokens that can't be configured through Angular Material's current theming API,
// but may be in a future version of the theming API.
@function get-unthemable-tokens() {
  @return (
    expansion-container-shape: 4px,
    expansion-legacy-header-indicator-display: inline-block,
    expansion-header-indicator-display: none,
  );
}

// Tokens that can be configured through Angular Material's color theming API.
@function get-color-tokens($theme) {
  @return (
    expansion-container-background-color: inspection.get-theme-color($theme, background, card),
    expansion-container-text-color: inspection.get-theme-color($theme, foreground, text),
    expansion-actions-divider-color: inspection.get-theme-color($theme, foreground, divider),
    expansion-header-hover-state-layer-color: inspection.get-theme-color($theme, background, hover),
    expansion-header-focus-state-layer-color: inspection.get-theme-color($theme, background, hover),
    expansion-header-disabled-state-text-color: inspection.get-theme-color(
        $theme, foreground, disabled-button),
    expansion-header-text-color: inspection.get-theme-color($theme, foreground, text),
    expansion-header-description-color:
        inspection.get-theme-color($theme, foreground, secondary-text),
    expansion-header-indicator-color:
        inspection.get-theme-color($theme, foreground, secondary-text),
  );
}

// Tokens that can be configured through Angular Material's typography theming API.
@function get-typography-tokens($theme) {
  @return (
    expansion-header-text-font: inspection.get-theme-typography($theme, subtitle-2, font-family),
    expansion-header-text-size: inspection.get-theme-typography($theme, subtitle-2, font-size),
    expansion-header-text-weight: inspection.get-theme-typography($theme, subtitle-2, font-weight),

    // TODO(crisbeto): these two properties weren't set at all before the introduction of tokens,
    // but it's inconsistent not to provide them since the container sets all of them. Eventually
    // we should change the values to use come from `subtitle-2`.
    expansion-header-text-line-height: inherit,
    expansion-header-text-tracking: inherit,

    expansion-container-text-font: inspection.get-theme-typography($theme, body-2, font-family),
    expansion-container-text-line-height:
        inspection.get-theme-typography($theme, body-2, line-height),
    expansion-container-text-size: inspection.get-theme-typography($theme, body-2, font-size),
    expansion-container-text-tracking:
        inspection.get-theme-typography($theme, body-2, letter-spacing),
    expansion-container-text-weight: inspection.get-theme-typography($theme, body-2, font-weight),
  );
}

// Tokens that can be configured through Angular Material's density theming API.
@function get-density-tokens($theme) {
  $scale: theming.clamp-density(inspection.get-theme-density($theme), -3);
  $collapsed-scale: (
    0: 48px,
    -1: 44px,
    -2: 40px,
    -3: 36px,
  );
  $expanded-scale: (
    0: 64px,
    -1: 60px,
    -2: 56px,
    -3: 48px,
  );

  @return (
    expansion-header-collapsed-state-height: map.get($collapsed-scale, $scale),
    expansion-header-expanded-state-height: map.get($expanded-scale, $scale),
  );
}

// Combines the tokens generated by the above functions into a single map with placeholder values.
// This is used to create token slots.
@function get-token-slots() {
  @return sass-utils.deep-merge-all(
      get-unthemable-tokens(),
      get-color-tokens(m2-utils.$placeholder-color-config),
      get-typography-tokens(m2-utils.$placeholder-typography-config),
      get-density-tokens(m2-utils.$placeholder-density-config)
  );
}
