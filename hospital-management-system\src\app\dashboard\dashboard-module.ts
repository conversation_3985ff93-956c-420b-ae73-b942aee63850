import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { SharedModule } from '../shared/shared-module';
import { Dashboard } from './dashboard/dashboard';

const routes: Routes = [
  { path: '', component: Dashboard }
];

@NgModule({
  declarations: [
    Dashboard
  ],
  imports: [
    SharedModule,
    RouterModule.forChild(routes)
  ]
})
export class DashboardModule { }
