{"version": 3, "file": "testing.mjs", "sources": ["../../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/material/input/testing/native-option-harness.ts", "../../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/material/input/testing/native-select-harness.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {ComponentHarness, HarnessPredicate} from '@angular/cdk/testing';\nimport {NativeOptionHarnessFilters} from './native-select-harness-filters';\n\n/** Harness for interacting with a native `option` in tests. */\nexport class MatNativeOptionHarness extends ComponentHarness {\n  /** Selector used to locate option instances. */\n  static hostSelector = 'select[matNativeControl] option';\n\n  /**\n   * Gets a `HarnessPredicate` that can be used to search for a `MatNativeOptionHarness` that meets\n   * certain criteria.\n   * @param options Options for filtering which option instances are considered a match.\n   * @return a `HarnessPredicate` configured with the given options.\n   */\n  static with(options: NativeOptionHarnessFilters = {}) {\n    return new HarnessPredicate(MatNativeOptionHarness, options)\n      .addOption('text', options.text, async (harness, title) =>\n        HarnessPredicate.stringMatches(await harness.getText(), title),\n      )\n      .addOption(\n        'index',\n        options.index,\n        async (harness, index) => (await harness.getIndex()) === index,\n      )\n      .addOption(\n        'isSelected',\n        options.isSelected,\n        async (harness, isSelected) => (await harness.isSelected()) === isSelected,\n      );\n  }\n\n  /** Gets the option's label text. */\n  async getText(): Promise<string> {\n    return (await this.host()).getProperty<string>('label');\n  }\n\n  /** Index of the option within the native `select` element. */\n  async getIndex(): Promise<number> {\n    return (await this.host()).getProperty<number>('index');\n  }\n\n  /** Gets whether the option is disabled. */\n  async isDisabled(): Promise<boolean> {\n    return (await this.host()).getProperty<boolean>('disabled');\n  }\n\n  /** Gets whether the option is selected. */\n  async isSelected(): Promise<boolean> {\n    return (await this.host()).getProperty<boolean>('selected');\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {HarnessPredicate, parallel} from '@angular/cdk/testing';\nimport {MatFormFieldControlHarness} from '../../form-field/testing/control';\nimport {MatNativeOptionHarness} from './native-option-harness';\nimport {\n  NativeOptionHarnessFilters,\n  NativeSelectHarnessFilters,\n} from './native-select-harness-filters';\n\n/** Harness for interacting with a native `select` in tests. */\nexport class MatNativeSelectHarness extends MatFormFieldControlHarness {\n  static hostSelector = 'select[matNativeControl]';\n\n  /**\n   * Gets a `HarnessPredicate` that can be used to search for a `MatNativeSelectHarness` that meets\n   * certain criteria.\n   * @param options Options for filtering which select instances are considered a match.\n   * @return a `HarnessPredicate` configured with the given options.\n   */\n  static with(options: NativeSelectHarnessFilters = {}): HarnessPredicate<MatNativeSelectHarness> {\n    return new HarnessPredicate(MatNativeSelectHarness, options);\n  }\n\n  /** Gets a boolean promise indicating if the select is disabled. */\n  async isDisabled(): Promise<boolean> {\n    return (await this.host()).getProperty<boolean>('disabled');\n  }\n\n  /** Gets a boolean promise indicating if the select is required. */\n  async isRequired(): Promise<boolean> {\n    return (await this.host()).getProperty<boolean>('required');\n  }\n\n  /** Gets a boolean promise indicating if the select is in multi-selection mode. */\n  async isMultiple(): Promise<boolean> {\n    return (await this.host()).getProperty<boolean>('multiple');\n  }\n\n  /** Gets the name of the select. */\n  async getName(): Promise<string> {\n    // The \"name\" property of the native select is never undefined.\n    return await (await this.host()).getProperty<string>('name');\n  }\n\n  /** Gets the id of the select. */\n  async getId(): Promise<string> {\n    // We're guaranteed to have an id, because the `matNativeControl` always assigns one.\n    return await (await this.host()).getProperty<string>('id');\n  }\n\n  /** Focuses the select and returns a void promise that indicates when the action is complete. */\n  async focus(): Promise<void> {\n    return (await this.host()).focus();\n  }\n\n  /** Blurs the select and returns a void promise that indicates when the action is complete. */\n  async blur(): Promise<void> {\n    return (await this.host()).blur();\n  }\n\n  /** Whether the select is focused. */\n  async isFocused(): Promise<boolean> {\n    return (await this.host()).isFocused();\n  }\n\n  /** Gets the options inside the select panel. */\n  async getOptions(filter: NativeOptionHarnessFilters = {}): Promise<MatNativeOptionHarness[]> {\n    return this.locatorForAll(MatNativeOptionHarness.with(filter))();\n  }\n\n  /**\n   * Selects the options that match the passed-in filter. If the select is in multi-selection\n   * mode all options will be clicked, otherwise the harness will pick the first matching option.\n   */\n  async selectOptions(filter: NativeOptionHarnessFilters = {}): Promise<void> {\n    const [isMultiple, options] = await parallel(() => {\n      return [this.isMultiple(), this.getOptions(filter)];\n    });\n\n    if (options.length === 0) {\n      throw Error('Select does not have options matching the specified filter');\n    }\n\n    const [host, optionIndexes] = await parallel(() => [\n      this.host(),\n      parallel(() => options.slice(0, isMultiple ? undefined : 1).map(option => option.getIndex())),\n    ]);\n\n    await host.selectOptions(...optionIndexes);\n  }\n}\n"], "names": [], "mappings": ";;;;;AAWA;AACM,MAAO,sBAAuB,SAAQ,gBAAgB,CAAA;;AAE1D,IAAA,OAAO,YAAY,GAAG,iCAAiC;AAEvD;;;;;AAKG;AACH,IAAA,OAAO,IAAI,CAAC,OAAA,GAAsC,EAAE,EAAA;AAClD,QAAA,OAAO,IAAI,gBAAgB,CAAC,sBAAsB,EAAE,OAAO;aACxD,SAAS,CAAC,MAAM,EAAE,OAAO,CAAC,IAAI,EAAE,OAAO,OAAO,EAAE,KAAK,KACpD,gBAAgB,CAAC,aAAa,CAAC,MAAM,OAAO,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC;aAE/D,SAAS,CACR,OAAO,EACP,OAAO,CAAC,KAAK,EACb,OAAO,OAAO,EAAE,KAAK,KAAK,CAAC,MAAM,OAAO,CAAC,QAAQ,EAAE,MAAM,KAAK;aAE/D,SAAS,CACR,YAAY,EACZ,OAAO,CAAC,UAAU,EAClB,OAAO,OAAO,EAAE,UAAU,KAAK,CAAC,MAAM,OAAO,CAAC,UAAU,EAAE,MAAM,UAAU,CAC3E;;;AAIL,IAAA,MAAM,OAAO,GAAA;AACX,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,WAAW,CAAS,OAAO,CAAC;;;AAIzD,IAAA,MAAM,QAAQ,GAAA;AACZ,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,WAAW,CAAS,OAAO,CAAC;;;AAIzD,IAAA,MAAM,UAAU,GAAA;AACd,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,WAAW,CAAU,UAAU,CAAC;;;AAI7D,IAAA,MAAM,UAAU,GAAA;AACd,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,WAAW,CAAU,UAAU,CAAC;;;;ACxC/D;AACM,MAAO,sBAAuB,SAAQ,0BAA0B,CAAA;AACpE,IAAA,OAAO,YAAY,GAAG,0BAA0B;AAEhD;;;;;AAKG;AACH,IAAA,OAAO,IAAI,CAAC,OAAA,GAAsC,EAAE,EAAA;AAClD,QAAA,OAAO,IAAI,gBAAgB,CAAC,sBAAsB,EAAE,OAAO,CAAC;;;AAI9D,IAAA,MAAM,UAAU,GAAA;AACd,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,WAAW,CAAU,UAAU,CAAC;;;AAI7D,IAAA,MAAM,UAAU,GAAA;AACd,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,WAAW,CAAU,UAAU,CAAC;;;AAI7D,IAAA,MAAM,UAAU,GAAA;AACd,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,WAAW,CAAU,UAAU,CAAC;;;AAI7D,IAAA,MAAM,OAAO,GAAA;;AAEX,QAAA,OAAO,MAAM,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,WAAW,CAAS,MAAM,CAAC;;;AAI9D,IAAA,MAAM,KAAK,GAAA;;AAET,QAAA,OAAO,MAAM,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,WAAW,CAAS,IAAI,CAAC;;;AAI5D,IAAA,MAAM,KAAK,GAAA;QACT,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,KAAK,EAAE;;;AAIpC,IAAA,MAAM,IAAI,GAAA;QACR,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,IAAI,EAAE;;;AAInC,IAAA,MAAM,SAAS,GAAA;QACb,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,SAAS,EAAE;;;AAIxC,IAAA,MAAM,UAAU,CAAC,MAAA,GAAqC,EAAE,EAAA;AACtD,QAAA,OAAO,IAAI,CAAC,aAAa,CAAC,sBAAsB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE;;AAGlE;;;AAGG;AACH,IAAA,MAAM,aAAa,CAAC,MAAA,GAAqC,EAAE,EAAA;QACzD,MAAM,CAAC,UAAU,EAAE,OAAO,CAAC,GAAG,MAAM,QAAQ,CAAC,MAAK;AAChD,YAAA,OAAO,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;AACrD,SAAC,CAAC;AAEF,QAAA,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE;AACxB,YAAA,MAAM,KAAK,CAAC,4DAA4D,CAAC;;QAG3E,MAAM,CAAC,IAAI,EAAE,aAAa,CAAC,GAAG,MAAM,QAAQ,CAAC,MAAM;YACjD,IAAI,CAAC,IAAI,EAAE;AACX,YAAA,QAAQ,CAAC,MAAM,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,UAAU,GAAG,SAAS,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC;AAC9F,SAAA,CAAC;AAEF,QAAA,MAAM,IAAI,CAAC,aAAa,CAAC,GAAG,aAAa,CAAC;;;;;;"}