{"version": 3, "file": "input-harness-oQzj5EsQ.mjs", "sources": ["../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/material/input/testing/input-harness.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {HarnessPredicate, parallel} from '@angular/cdk/testing';\nimport {MatFormFieldControlHarness} from '../../form-field/testing/control';\nimport {coerceBooleanProperty} from '@angular/cdk/coercion';\nimport {InputHarnessFilters} from './input-harness-filters';\n\n/** Harness for interacting with a standard Material inputs in tests. */\nexport class MatInputHarness extends MatFormFieldControlHarness {\n  // TODO: We do not want to handle `select` elements with `matNativeControl` because\n  // not all methods of this harness work reasonably for native select elements.\n  // For more details. See: https://github.com/angular/components/pull/18221.\n  static hostSelector = '[matInput], input[matNativeControl], textarea[matNativeControl]';\n\n  /**\n   * Gets a `HarnessPredicate` that can be used to search for a `MatInputHarness` that meets\n   * certain criteria.\n   * @param options Options for filtering which input instances are considered a match.\n   * @return a `HarnessPredicate` configured with the given options.\n   */\n  static with(options: InputHarnessFilters = {}): HarnessPredicate<MatInputHarness> {\n    return new HarnessPredicate(MatInputHarness, options)\n      .addOption('value', options.value, (harness, value) => {\n        return HarnessPredicate.stringMatches(harness.getValue(), value);\n      })\n      .addOption('placeholder', options.placeholder, (harness, placeholder) => {\n        return HarnessPredicate.stringMatches(harness.getPlaceholder(), placeholder);\n      });\n  }\n\n  /** Whether the input is disabled. */\n  async isDisabled(): Promise<boolean> {\n    const host = await this.host();\n    const disabled = await host.getAttribute('disabled');\n\n    if (disabled !== null) {\n      return coerceBooleanProperty(disabled);\n    }\n\n    return (await host.getAttribute('aria-disabled')) === 'true';\n  }\n\n  /** Whether the input is required. */\n  async isRequired(): Promise<boolean> {\n    return (await this.host()).getProperty<boolean>('required');\n  }\n\n  /** Whether the input is readonly. */\n  async isReadonly(): Promise<boolean> {\n    return (await this.host()).getProperty<boolean>('readOnly');\n  }\n\n  /** Gets the value of the input. */\n  async getValue(): Promise<string> {\n    // The \"value\" property of the native input is never undefined.\n    return await (await this.host()).getProperty<string>('value');\n  }\n\n  /** Gets the name of the input. */\n  async getName(): Promise<string> {\n    // The \"name\" property of the native input is never undefined.\n    return await (await this.host()).getProperty<string>('name');\n  }\n\n  /**\n   * Gets the type of the input. Returns \"textarea\" if the input is\n   * a textarea.\n   */\n  async getType(): Promise<string> {\n    // The \"type\" property of the native input is never undefined.\n    return await (await this.host()).getProperty<string>('type');\n  }\n\n  /** Gets the placeholder of the input. */\n  async getPlaceholder(): Promise<string> {\n    const host = await this.host();\n    const [nativePlaceholder, fallback] = await parallel(() => [\n      host.getProperty('placeholder'),\n      host.getAttribute('data-placeholder'),\n    ]);\n    return nativePlaceholder || fallback || '';\n  }\n\n  /** Gets the id of the input. */\n  async getId(): Promise<string> {\n    // The input directive always assigns a unique id to the input in\n    // case no id has been explicitly specified.\n    return await (await this.host()).getProperty<string>('id');\n  }\n\n  /**\n   * Focuses the input and returns a promise that indicates when the\n   * action is complete.\n   */\n  async focus(): Promise<void> {\n    return (await this.host()).focus();\n  }\n\n  /**\n   * Blurs the input and returns a promise that indicates when the\n   * action is complete.\n   */\n  async blur(): Promise<void> {\n    return (await this.host()).blur();\n  }\n\n  /** Whether the input is focused. */\n  async isFocused(): Promise<boolean> {\n    return (await this.host()).isFocused();\n  }\n\n  /**\n   * Sets the value of the input. The value will be set by simulating\n   * keypresses that correspond to the given value.\n   */\n  async setValue(newValue: string): Promise<void> {\n    const inputEl = await this.host();\n    await inputEl.clear();\n    // We don't want to send keys for the value if the value is an empty\n    // string in order to clear the value. Sending keys with an empty string\n    // still results in unnecessary focus events.\n    if (newValue) {\n      await inputEl.sendKeys(newValue);\n    }\n\n    // Some input types won't respond to key presses (e.g. `color`) so to be sure that the\n    // value is set, we also set the property after the keyboard sequence. Note that we don't\n    // want to do it before, because it can cause the value to be entered twice.\n    await inputEl.setInputValue(newValue);\n  }\n}\n"], "names": [], "mappings": ";;;;AAaA;AACM,MAAO,eAAgB,SAAQ,0BAA0B,CAAA;;;;AAI7D,IAAA,OAAO,YAAY,GAAG,iEAAiE;AAEvF;;;;;AAKG;AACH,IAAA,OAAO,IAAI,CAAC,OAAA,GAA+B,EAAE,EAAA;AAC3C,QAAA,OAAO,IAAI,gBAAgB,CAAC,eAAe,EAAE,OAAO;AACjD,aAAA,SAAS,CAAC,OAAO,EAAE,OAAO,CAAC,KAAK,EAAE,CAAC,OAAO,EAAE,KAAK,KAAI;YACpD,OAAO,gBAAgB,CAAC,aAAa,CAAC,OAAO,CAAC,QAAQ,EAAE,EAAE,KAAK,CAAC;AAClE,SAAC;AACA,aAAA,SAAS,CAAC,aAAa,EAAE,OAAO,CAAC,WAAW,EAAE,CAAC,OAAO,EAAE,WAAW,KAAI;YACtE,OAAO,gBAAgB,CAAC,aAAa,CAAC,OAAO,CAAC,cAAc,EAAE,EAAE,WAAW,CAAC;AAC9E,SAAC,CAAC;;;AAIN,IAAA,MAAM,UAAU,GAAA;AACd,QAAA,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE;QAC9B,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC;AAEpD,QAAA,IAAI,QAAQ,KAAK,IAAI,EAAE;AACrB,YAAA,OAAO,qBAAqB,CAAC,QAAQ,CAAC;;QAGxC,OAAO,CAAC,MAAM,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,MAAM,MAAM;;;AAI9D,IAAA,MAAM,UAAU,GAAA;AACd,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,WAAW,CAAU,UAAU,CAAC;;;AAI7D,IAAA,MAAM,UAAU,GAAA;AACd,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,WAAW,CAAU,UAAU,CAAC;;;AAI7D,IAAA,MAAM,QAAQ,GAAA;;AAEZ,QAAA,OAAO,MAAM,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,WAAW,CAAS,OAAO,CAAC;;;AAI/D,IAAA,MAAM,OAAO,GAAA;;AAEX,QAAA,OAAO,MAAM,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,WAAW,CAAS,MAAM,CAAC;;AAG9D;;;AAGG;AACH,IAAA,MAAM,OAAO,GAAA;;AAEX,QAAA,OAAO,MAAM,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,WAAW,CAAS,MAAM,CAAC;;;AAI9D,IAAA,MAAM,cAAc,GAAA;AAClB,QAAA,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE;QAC9B,MAAM,CAAC,iBAAiB,EAAE,QAAQ,CAAC,GAAG,MAAM,QAAQ,CAAC,MAAM;AACzD,YAAA,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC;AAC/B,YAAA,IAAI,CAAC,YAAY,CAAC,kBAAkB,CAAC;AACtC,SAAA,CAAC;AACF,QAAA,OAAO,iBAAiB,IAAI,QAAQ,IAAI,EAAE;;;AAI5C,IAAA,MAAM,KAAK,GAAA;;;AAGT,QAAA,OAAO,MAAM,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,WAAW,CAAS,IAAI,CAAC;;AAG5D;;;AAGG;AACH,IAAA,MAAM,KAAK,GAAA;QACT,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,KAAK,EAAE;;AAGpC;;;AAGG;AACH,IAAA,MAAM,IAAI,GAAA;QACR,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,IAAI,EAAE;;;AAInC,IAAA,MAAM,SAAS,GAAA;QACb,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,SAAS,EAAE;;AAGxC;;;AAGG;IACH,MAAM,QAAQ,CAAC,QAAgB,EAAA;AAC7B,QAAA,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE;AACjC,QAAA,MAAM,OAAO,CAAC,KAAK,EAAE;;;;QAIrB,IAAI,QAAQ,EAAE;AACZ,YAAA,MAAM,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC;;;;;AAMlC,QAAA,MAAM,OAAO,CAAC,aAAa,CAAC,QAAQ,CAAC;;;;;;"}