import {
  MAT_SELECT_CONFIG,
  MAT_SELECT_SCROLL_STRATEGY,
  MAT_SELECT_SCROLL_STRATEGY_PROVIDER,
  MAT_SELECT_SCROLL_STRATEGY_PROVIDER_FACTORY,
  MAT_SELECT_TRIGGER,
  MatSelect,
  MatSelectChange,
  MatSelectModule,
  MatSelectTrigger
} from "./chunk-LAF7SV47.js";
import "./chunk-ALMOMGYH.js";
import "./chunk-DJPOMJN6.js";
import "./chunk-JKRSZR5T.js";
import "./chunk-QBGAJJNV.js";
import {
  MatOptgroup,
  MatOption
} from "./chunk-VR74EIIL.js";
import "./chunk-A323VGTR.js";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>orm<PERSON>ield,
  <PERSON><PERSON>int,
  <PERSON><PERSON><PERSON><PERSON>,
  MatPrefix,
  MatSuffix
} from "./chunk-64EXLORB.js";
import "./chunk-MUEA6QJ4.js";
import "./chunk-CBGQNHVP.js";
import "./chunk-E3RI57BW.js";
import "./chunk-L2RY5H6K.js";
import "./chunk-ENLWLTCM.js";
import "./chunk-7QYU4HFA.js";
import "./chunk-6GOA3XGK.js";
import "./chunk-QCETVJKM.js";
import "./chunk-DQ7OVFPD.js";
import "./chunk-BQVFHSAC.js";
import "./chunk-P3VRHFR2.js";
import "./chunk-WBZGCOHS.js";
import "./chunk-PSVTBU3O.js";
import "./chunk-VLMY5NP2.js";
import "./chunk-EOFW2REK.js";
import "./chunk-D6MSWTN6.js";
import "./chunk-JM5GRSWL.js";
import "./chunk-GIMDOBEN.js";
import "./chunk-B7UG7IOS.js";
import "./chunk-KQRM5WX6.js";
import "./chunk-2FKQUGRE.js";
import "./chunk-6PNJZOQ4.js";
import "./chunk-Z3F4H3BY.js";
import "./chunk-ZG272CAW.js";

// node_modules/@angular/material/fesm2022/select.mjs
var matSelectAnimations = {
  // Represents
  // trigger('transformPanel', [
  //   state(
  //     'void',
  //     style({
  //       opacity: 0,
  //       transform: 'scale(1, 0.8)',
  //     }),
  //   ),
  //   transition(
  //     'void => showing',
  //     animate(
  //       '120ms cubic-bezier(0, 0, 0.2, 1)',
  //       style({
  //         opacity: 1,
  //         transform: 'scale(1, 1)',
  //       }),
  //     ),
  //   ),
  //   transition('* => void', animate('100ms linear', style({opacity: 0}))),
  // ])
  /** This animation transforms the select's overlay panel on and off the page. */
  transformPanel: {
    type: 7,
    name: "transformPanel",
    definitions: [
      {
        type: 0,
        name: "void",
        styles: {
          type: 6,
          styles: { opacity: 0, transform: "scale(1, 0.8)" },
          offset: null
        }
      },
      {
        type: 1,
        expr: "void => showing",
        animation: {
          type: 4,
          styles: {
            type: 6,
            styles: { opacity: 1, transform: "scale(1, 1)" },
            offset: null
          },
          timings: "120ms cubic-bezier(0, 0, 0.2, 1)"
        },
        options: null
      },
      {
        type: 1,
        expr: "* => void",
        animation: {
          type: 4,
          styles: { type: 6, styles: { opacity: 0 }, offset: null },
          timings: "100ms linear"
        },
        options: null
      }
    ],
    options: {}
  }
};
export {
  MAT_SELECT_CONFIG,
  MAT_SELECT_SCROLL_STRATEGY,
  MAT_SELECT_SCROLL_STRATEGY_PROVIDER,
  MAT_SELECT_SCROLL_STRATEGY_PROVIDER_FACTORY,
  MAT_SELECT_TRIGGER,
  MatError,
  MatFormField,
  MatHint,
  MatLabel,
  MatOptgroup,
  MatOption,
  MatPrefix,
  MatSelect,
  MatSelectChange,
  MatSelectModule,
  MatSelectTrigger,
  MatSuffix,
  matSelectAnimations
};
//# sourceMappingURL=@angular_material_select.js.map
