@use '../core/style/elevation';
@use '../core/style/sass-utils';
@use '../core/theming/inspection';
@use '../core/theming/theming';
@use '../core/tokens/m2-utils';
@use 'sass:map';

// Tokens that can't be configured through Angular Material's current theming API,
// but may be in a future version of the theming API.
@function get-unthemable-tokens() {
  // visible-track-opacity and hidden-track-opacity:
  // The hidden and visible track represent whichever track is visible or
  // hidden in the ui. This could be the .mdc-switch__track :before or
  // :after depending on whether the switch is selected or unselected.
  //
  // The m2 slide-toggle uses transforms to hide & show the tracks while
  // the m3 slide-toggle uses opacity.
  @return (
    slide-toggle-disabled-handle-opacity: null,
    slide-toggle-disabled-selected-handle-opacity: 0.38,
    slide-toggle-disabled-selected-icon-opacity: 0.38,
    slide-toggle-disabled-track-opacity: 0.12,
    slide-toggle-disabled-unselected-handle-opacity: 0.38,
    slide-toggle-disabled-unselected-icon-opacity: 0.38,
    slide-toggle-disabled-unselected-track-outline-color: transparent,
    slide-toggle-disabled-unselected-track-outline-width: 1px,
    slide-toggle-handle-height: 20px,
    slide-toggle-handle-shape: 10px,
    slide-toggle-handle-width: 20px,
    slide-toggle-hidden-track-opacity: 1,
    slide-toggle-hidden-track-transition: transform 75ms 0ms cubic-bezier(0.4, 0, 0.6, 1),
    slide-toggle-pressed-handle-size: 20px,
    slide-toggle-selected-focus-state-layer-opacity: 0.12,
    slide-toggle-selected-handle-horizontal-margin: 0,
    slide-toggle-selected-handle-size: 20px,
    slide-toggle-selected-hover-state-layer-opacity: 0.04,
    slide-toggle-selected-icon-size: 18px,
    slide-toggle-selected-pressed-handle-horizontal-margin: 0,
    slide-toggle-selected-pressed-state-layer-opacity: 0.1,
    slide-toggle-selected-track-outline-color: transparent,
    slide-toggle-selected-track-outline-width: 1px,
    slide-toggle-selected-with-icon-handle-horizontal-margin: 0,
    slide-toggle-track-height: 14px,
    slide-toggle-track-outline-color: transparent,
    slide-toggle-track-outline-width: 1px,
    slide-toggle-track-shape: 7px,
    slide-toggle-track-width: 36px,
    slide-toggle-unselected-focus-state-layer-opacity: 0.12,
    slide-toggle-unselected-handle-horizontal-margin: 0,
    slide-toggle-unselected-handle-size: 20px,
    slide-toggle-unselected-hover-state-layer-opacity: 0.04,
    slide-toggle-unselected-icon-size: 18px,
    slide-toggle-unselected-pressed-handle-horizontal-margin: 0,
    slide-toggle-unselected-pressed-state-layer-opacity: 0.1,
    slide-toggle-unselected-with-icon-handle-horizontal-margin: 0,
    slide-toggle-visible-track-opacity: 1,
    slide-toggle-visible-track-transition: transform 75ms 0ms cubic-bezier(0, 0, 0.2, 1),
    slide-toggle-with-icon-handle-size: 20px,
  );
}

// Tokens that can be configured through Angular Material's color theming API.
@function get-color-tokens($theme) {
  $is-dark: inspection.get-theme-type($theme) == dark;
  $on-surface: if($is-dark, #f5f5f5, #424242);
  $hairline: if($is-dark, #616161, #e0e0e0);
  $on-surface-variant: if($is-dark, #9e9e9e, #616161);
  $on-surface-state-content: if($is-dark, #fafafa, #212121);
  $disabled-handle-color: if($is-dark, #000, #424242);
  $icon-color: if($is-dark, #212121, #fff);

  // The default for tokens that rely on the theme will use the primary palette
  $theme-color-tokens: private-get-color-palette-color-tokens($theme, primary);

  // TODO(crisbeto): `handle-surface-color` was hardcoded to `var(--mat-surface-color, #fff)`
  // which made it basically hardcoded to #fff. Should it be based on the theme?
  @return map.merge(
      $theme-color-tokens,
      (
        slide-toggle-disabled-handle-elevation-shadow: elevation.get-box-shadow(0),
        slide-toggle-disabled-selected-handle-color: $disabled-handle-color,
        slide-toggle-disabled-selected-icon-color: $icon-color,
        slide-toggle-disabled-selected-track-color: $on-surface,
        slide-toggle-disabled-unselected-handle-color: $disabled-handle-color,
        slide-toggle-disabled-unselected-icon-color: $icon-color,
        slide-toggle-disabled-unselected-track-color: $on-surface,
        slide-toggle-handle-elevation-shadow: elevation.get-box-shadow(1),
        slide-toggle-handle-surface-color: #fff,
        slide-toggle-label-text-color: inspection.get-theme-color($theme, foreground, text),
        slide-toggle-selected-icon-color: $icon-color,
        slide-toggle-unselected-hover-handle-color: $on-surface-state-content,
        slide-toggle-unselected-focus-handle-color: $on-surface-state-content,
        slide-toggle-unselected-focus-state-layer-color: $on-surface,
        slide-toggle-unselected-focus-track-color: $hairline,
        slide-toggle-unselected-icon-color: $icon-color,
        slide-toggle-unselected-handle-color: $on-surface-variant,
        slide-toggle-unselected-hover-state-layer-color: $on-surface,
        slide-toggle-unselected-hover-track-color: $hairline,
        slide-toggle-unselected-pressed-handle-color: $on-surface-state-content,
        slide-toggle-unselected-pressed-track-color: $hairline,
        slide-toggle-unselected-pressed-state-layer-color: $on-surface,
        slide-toggle-unselected-track-color: $hairline,
      )
  );
}

// Generates the mapping for the properties that change based on the slide toggle color.
@function private-get-color-palette-color-tokens($theme, $palette-name) {
  $is-dark: inspection.get-theme-type($theme) == dark;
  $palette-color: inspection.get-theme-color($theme, $palette-name, if($is-dark, 300, 600));
  $state-content: inspection.get-theme-color($theme, $palette-name, if($is-dark, 200, 900));
  $inverse: inspection.get-theme-color($theme, $palette-name, if($is-dark, 600, 300));

  @return (
    slide-toggle-selected-focus-state-layer-color: $palette-color,
    slide-toggle-selected-handle-color: $palette-color,
    slide-toggle-selected-hover-state-layer-color: $palette-color,
    slide-toggle-selected-pressed-state-layer-color: $palette-color,
    slide-toggle-selected-focus-handle-color: $state-content,
    slide-toggle-selected-hover-handle-color: $state-content,
    slide-toggle-selected-pressed-handle-color: $state-content,
    slide-toggle-selected-focus-track-color: $inverse,
    slide-toggle-selected-hover-track-color: $inverse,
    slide-toggle-selected-pressed-track-color: $inverse,
    slide-toggle-selected-track-color: $inverse,
  );
}

// Tokens that can be configured through Angular Material's typography theming API.
@function get-typography-tokens($theme) {
  @return (
    slide-toggle-label-text-font: inspection.get-theme-typography($theme, body-2, font-family),
    slide-toggle-label-text-line-height:
        inspection.get-theme-typography($theme, body-2, line-height),
    slide-toggle-label-text-size: inspection.get-theme-typography($theme, body-2, font-size),
    slide-toggle-label-text-tracking:
        inspection.get-theme-typography($theme, body-2, letter-spacing),
    slide-toggle-label-text-weight: inspection.get-theme-typography($theme, body-2, font-weight)
  );
}

// Tokens that can be configured through Angular Material's density theming API.
@function get-density-tokens($theme) {
  $scale: theming.clamp-density(inspection.get-theme-density($theme), -3);

  @return (
    // The diameter of the handle ripple.
    slide-toggle-state-layer-size: map.get((
      0: 40px,
      -1: 36px,
      -2: 32px,
      -3: 28px,
    ), $scale)
  );
}

// Combines the tokens generated by the above functions into a single map with placeholder values.
// This is used to create token slots.
@function get-token-slots() {
  @return sass-utils.deep-merge-all(
    get-unthemable-tokens(),
    get-color-tokens(m2-utils.$placeholder-color-config),
    get-typography-tokens(m2-utils.$placeholder-typography-config),
    get-density-tokens(m2-utils.$placeholder-density-config)
  );
}
