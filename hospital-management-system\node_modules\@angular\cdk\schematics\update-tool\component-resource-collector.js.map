{"version": 3, "file": "component-resource-collector.js", "sourceRoot": "", "sources": ["component-resource-collector.ts"], "names": [], "mappings": ";AAAA;;;;;;GAMG;;;AAEH,+BAA6B;AAC7B,iCAAiC;AAEjC,mDAAwD;AACxD,iDAAmD;AACnD,yDAI+B;AAC/B,yDAA0D;AAqB1D;;;GAGG;AACH,MAAa,0BAA0B;IAIrC,YACS,WAA2B,EAC1B,WAAuB;QADxB,gBAAW,GAAX,WAAW,CAAgB;QAC1B,gBAAW,GAAX,WAAW,CAAY;QALjC,sBAAiB,GAAuB,EAAE,CAAC;QAC3C,wBAAmB,GAAuB,EAAE,CAAC;IAK1C,CAAC;IAEJ,SAAS,CAAC,IAAa;QACrB,IAAI,IAAI,CAAC,IAAI,KAAK,EAAE,CAAC,UAAU,CAAC,gBAAgB,EAAE,CAAC;YACjD,IAAI,CAAC,sBAAsB,CAAC,IAA2B,CAAC,CAAC;QAC3D,CAAC;IACH,CAAC;IAEO,sBAAsB,CAAC,IAAyB;QACtD,MAAM,UAAU,GAAG,EAAE,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;QAE1C,IAAI,CAAC,UAAU,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC;YACtC,OAAO;QACT,CAAC;QAED,MAAM,YAAY,GAAG,IAAA,iCAAoB,EAAC,IAAI,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC;QACxE,MAAM,kBAAkB,GAAG,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,KAAK,WAAW,CAAC,CAAC;QAE9E,+EAA+E;QAC/E,IAAI,CAAC,kBAAkB,EAAE,CAAC;YACxB,OAAO;QACT,CAAC;QAED,MAAM,aAAa,GAAG,kBAAkB,CAAC,IAAI,CAAC,UAAU,CAAC;QAEzD,kFAAkF;QAClF,IAAI,aAAa,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACzC,OAAO;QACT,CAAC;QAED,MAAM,iBAAiB,GAAG,IAAA,4BAAgB,EAAC,aAAa,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;QAEvE,sEAAsE;QACtE,IAAI,CAAC,EAAE,CAAC,yBAAyB,CAAC,iBAAiB,CAAC,EAAE,CAAC;YACrD,OAAO;QACT,CAAC;QAED,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;QACxC,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;QAC/D,MAAM,iBAAiB,GAAG,IAAA,cAAO,EAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;QAEvD,8EAA8E;QAC9E,6CAA6C;QAC7C,iBAAiB,CAAC,UAAU,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;YAC9C,IAAI,CAAC,EAAE,CAAC,oBAAoB,CAAC,QAAQ,CAAC,EAAE,CAAC;gBACvC,OAAO;YACT,CAAC;YAED,MAAM,YAAY,GAAG,IAAA,mCAAmB,EAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YAExD,IAAI,YAAY,KAAK,QAAQ,EAAE,CAAC;gBAC9B,MAAM,QAAQ,GAAG,EAAE,CAAC,wBAAwB,CAAC,QAAQ,CAAC,WAAW,CAAC;oBAChE,CAAC,CAAC,QAAQ,CAAC,WAAW,CAAC,QAAQ;oBAC/B,CAAC,CAAC,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;gBAE3B,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE;oBACpB,IAAI,EAAE,CAAC,mBAAmB,CAAC,EAAE,CAAC,EAAE,CAAC;wBAC/B,4EAA4E;wBAC5E,oCAAoC;wBACpC,MAAM,gBAAgB,GAAG,EAAE,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC;wBAC3C,MAAM,OAAO,GAAG,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;wBAClC,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC;4BAC5B,QAAQ;4BACR,SAAS,EAAE,IAAI;4BACf,OAAO;4BACP,MAAM,EAAE,IAAI;4BACZ,KAAK,EAAE,gBAAgB;4BACvB,6BAA6B,EAAE,GAAG,CAAC,EAAE,CACnC,EAAE,CAAC,6BAA6B,CAAC,UAAU,EAAE,GAAG,GAAG,gBAAgB,CAAC;yBACvE,CAAC,CAAC;oBACL,CAAC;gBACH,CAAC,CAAC,CAAC;YACL,CAAC;YAED,qFAAqF;YACrF,2EAA2E;YAC3E,IAAI,YAAY,KAAK,UAAU,IAAI,EAAE,CAAC,mBAAmB,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;gBAChF,4EAA4E;gBAC5E,oCAAoC;gBACpC,MAAM,gBAAgB,GAAG,QAAQ,CAAC,WAAW,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC;gBAC7D,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC;oBAC1B,QAAQ;oBACR,SAAS,EAAE,IAAI;oBACf,OAAO,EAAE,QAAQ,CAAC,WAAW,CAAC,IAAI;oBAClC,MAAM,EAAE,IAAI;oBACZ,KAAK,EAAE,gBAAgB;oBACvB,6BAA6B,EAAE,GAAG,CAAC,EAAE,CACnC,EAAE,CAAC,6BAA6B,CAAC,UAAU,EAAE,GAAG,GAAG,gBAAgB,CAAC;iBACvE,CAAC,CAAC;YACL,CAAC;YAED,IAAI,YAAY,KAAK,WAAW,IAAI,EAAE,CAAC,wBAAwB,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;gBACtF,QAAQ,CAAC,WAAW,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE;oBACzC,IAAI,EAAE,CAAC,mBAAmB,CAAC,EAAE,CAAC,EAAE,CAAC;wBAC/B,IAAI,CAAC,wBAAwB,CAAC,iBAAiB,EAAE,EAAE,EAAE,IAAI,CAAC,CAAC;oBAC7D,CAAC;gBACH,CAAC,CAAC,CAAC;YACL,CAAC;YAED,IAAI,YAAY,KAAK,UAAU,IAAI,EAAE,CAAC,mBAAmB,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;gBAChF,IAAI,CAAC,wBAAwB,CAAC,iBAAiB,EAAE,QAAQ,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;YAC/E,CAAC;YAED,IAAI,YAAY,KAAK,aAAa,IAAI,EAAE,CAAC,mBAAmB,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;gBACnF,MAAM,WAAW,GAAG,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC;gBAC9C,MAAM,YAAY,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,iBAAiB,EAAE,WAAW,CAAC,CAAC;gBAE9E,oEAAoE;gBACpE,qBAAqB;gBACrB,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,YAAY,CAAC,EAAE,CAAC;oBAC/C,OAAO;gBACT,CAAC;gBAED,MAAM,WAAW,GAAG,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC,CAAC;gBAExE,IAAI,WAAW,EAAE,CAAC;oBAChB,MAAM,aAAa,GAAG,IAAA,oCAAoB,EAAC,WAAW,CAAC,CAAC;oBAExD,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC;wBAC1B,QAAQ,EAAE,YAAY;wBACtB,SAAS,EAAE,IAAI;wBACf,OAAO,EAAE,WAAW;wBACpB,MAAM,EAAE,KAAK;wBACb,KAAK,EAAE,CAAC;wBACR,6BAA6B,EAAE,CAAC,CAAC,EAAE,CAAC,IAAA,+CAA+B,EAAC,aAAa,EAAE,CAAC,CAAC;qBACtF,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED,0FAA0F;IAC1F,yBAAyB,CACvB,QAAuB,EACvB,SAAqC;QAErC,6DAA6D;QAC7D,6EAA6E;QAC7E,MAAM,WAAW,GAAG,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC;QAEpE,IAAI,CAAC,WAAW,EAAE,CAAC;YACjB,OAAO,IAAI,CAAC;QACd,CAAC;QAED,MAAM,aAAa,GAAG,IAAA,oCAAoB,EAAC,WAAW,CAAC,CAAC;QAExD,OAAO;YACL,QAAQ,EAAE,QAAQ;YAClB,SAAS,EAAE,SAAS;YACpB,OAAO,EAAE,WAAW;YACpB,MAAM,EAAE,KAAK;YACb,KAAK,EAAE,CAAC;YACR,6BAA6B,EAAE,GAAG,CAAC,EAAE,CAAC,IAAA,+CAA+B,EAAC,aAAa,EAAE,GAAG,CAAC;SAC1F,CAAC;IACJ,CAAC;IAEO,wBAAwB,CAC9B,iBAAyB,EACzB,IAA0B,EAC1B,SAA8B;QAE9B,MAAM,cAAc,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,iBAAiB,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;QAC9E,MAAM,UAAU,GAAG,IAAI,CAAC,yBAAyB,CAAC,cAAc,EAAE,SAAS,CAAC,CAAC;QAE7E,IAAI,UAAU,EAAE,CAAC;YACf,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAC5C,CAAC;IACH,CAAC;CACF;AA/KD,gEA+KC;AAED,oCAAoC;AACpC,SAAS,QAAQ,CAAC,OAAe;IAC/B,OAAO,OAAO,CAAC,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;AACxC,CAAC"}