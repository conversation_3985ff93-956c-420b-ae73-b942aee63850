const express = require('express');
const Patient = require('../models/Patient');
const router = express.Router();

// Get all patients
router.get('/', async (req, res) => {
  try {
    const { page = 1, limit = 10, search = '' } = req.query;
    const query = search ? {
      $or: [
        { firstName: { $regex: search, $options: 'i' } },
        { lastName: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } },
        { patientId: { $regex: search, $options: 'i' } }
      ]
    } : {};

    const patients = await Patient.find(query)
      .limit(limit * 1)
      .skip((page - 1) * limit)
      .sort({ createdAt: -1 });

    const total = await Patient.countDocuments(query);

    res.json({
      patients,
      totalPages: Math.ceil(total / limit),
      currentPage: page,
      total
    });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

// Get patient by ID
router.get('/:id', async (req, res) => {
  try {
    const patient = await Patient.findById(req.params.id);
    if (!patient) {
      return res.status(404).json({ message: 'Patient not found' });
    }
    res.json(patient);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

// Create new patient
router.post('/', async (req, res) => {
  try {
    const patient = new Patient(req.body);
    await patient.save();
    res.status(201).json({
      message: 'Patient created successfully',
      patient
    });
  } catch (error) {
    if (error.code === 11000) {
      res.status(400).json({ message: 'Patient with this email already exists' });
    } else {
      res.status(400).json({ message: error.message });
    }
  }
});

// Update patient
router.put('/:id', async (req, res) => {
  try {
    const patient = await Patient.findByIdAndUpdate(
      req.params.id,
      req.body,
      { new: true, runValidators: true }
    );
    
    if (!patient) {
      return res.status(404).json({ message: 'Patient not found' });
    }
    
    res.json({
      message: 'Patient updated successfully',
      patient
    });
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
});

// Delete patient (soft delete)
router.delete('/:id', async (req, res) => {
  try {
    const patient = await Patient.findByIdAndUpdate(
      req.params.id,
      { isActive: false },
      { new: true }
    );
    
    if (!patient) {
      return res.status(404).json({ message: 'Patient not found' });
    }
    
    res.json({ message: 'Patient deactivated successfully' });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

// Get patient's medical history
router.get('/:id/medical-history', async (req, res) => {
  try {
    const patient = await Patient.findById(req.params.id).select('medicalHistory');
    if (!patient) {
      return res.status(404).json({ message: 'Patient not found' });
    }
    res.json(patient.medicalHistory);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

// Add medical history entry
router.post('/:id/medical-history', async (req, res) => {
  try {
    const patient = await Patient.findById(req.params.id);
    if (!patient) {
      return res.status(404).json({ message: 'Patient not found' });
    }
    
    patient.medicalHistory.push(req.body);
    await patient.save();
    
    res.status(201).json({
      message: 'Medical history added successfully',
      medicalHistory: patient.medicalHistory
    });
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
});

module.exports = router;
