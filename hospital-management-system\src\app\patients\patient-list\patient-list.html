<div class="container mx-auto px-4 py-6">
  <div class="flex justify-between items-center mb-6">
    <h1 class="text-2xl font-bold text-gray-900">Patients</h1>
    <button *hasPermission="'manage_patients'"
            class="btn btn-primary"
            routerLink="/patients/new">
      ➕ Add Patient
    </button>
  </div>

  <!-- Loading State -->
  <div *ngIf="loading" class="text-center py-8">
    <div class="spinner"></div>
    <p class="mt-4 text-gray-600">Loading patients...</p>
  </div>

  <!-- Error State -->
  <div *ngIf="error && !loading" class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
    <div class="flex items-center">
      <span class="text-red-500 mr-2">⚠️</span>
      <p class="text-red-700">{{ error }}</p>
    </div>
    <button class="btn btn-primary mt-2" (click)="loadPatients()">
      Try Again
    </button>
  </div>

  <!-- Empty State -->
  <div *ngIf="!loading && !error && patients.length === 0" class="text-center py-12">
    <div class="text-6xl text-gray-400 mb-4">👥</div>
    <h3 class="text-lg font-medium text-gray-900 mb-2">No patients found</h3>
    <p class="text-gray-600 mb-4">Get started by adding your first patient.</p>
    <button *hasPermission="'manage_patients'"
            class="btn btn-primary"
            routerLink="/patients/new">
      ➕ Add Patient
    </button>
  </div>

  <!-- Patients List -->
  <div *ngIf="!loading && !error && patients.length > 0">
    <div class="card overflow-hidden">
      <div class="overflow-x-auto">
        <table class="w-full table">
          <thead>
            <tr>
              <th class="font-semibold">Patient ID</th>
              <th class="font-semibold">Name</th>
              <th class="font-semibold">Contact</th>
              <th class="font-semibold">Age/Gender</th>
              <th class="font-semibold">Status</th>
              <th class="font-semibold">Actions</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let patient of patients">
              <td>{{ patient.patientId || 'N/A' }}</td>
              <td>
                <div class="flex items-center">
                  <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                    <span class="text-blue-600 text-sm">👤</span>
                  </div>
                  <div>
                    <p class="font-medium">{{ patient.firstName }} {{ patient.lastName }}</p>
                    <p class="text-sm text-gray-600">{{ patient.email }}</p>
                  </div>
                </div>
              </td>
              <td>
                <div>
                  <p>{{ patient.phone }}</p>
                  <p class="text-sm text-gray-600">{{ patient.address?.city || 'N/A' }}</p>
                </div>
              </td>
              <td>
                <div>
                  <p>{{ getAge(patient.dateOfBirth) || 'N/A' }} years</p>
                  <p class="text-sm text-gray-600">{{ patient.gender || 'N/A' }}</p>
                </div>
              </td>
              <td>
                <span class="px-2 py-1 text-xs rounded-full"
                      [ngClass]="{
                        'bg-green-100 text-green-800': patient.isActive,
                        'bg-red-100 text-red-800': !patient.isActive
                      }">
                  {{ patient.isActive ? 'Active' : 'Inactive' }}
                </span>
              </td>
              <td>
                <button class="btn btn-sm" [routerLink]="['/patients', patient._id]">
                  👁️ View
                </button>
                <button *hasPermission="'manage_patients'"
                        class="btn btn-sm ml-2"
                        [routerLink]="['/patients', patient._id, 'edit']">
                  ✏️ Edit
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- Pagination -->
    <div class="flex justify-between items-center mt-6" *ngIf="totalPages > 1">
      <p class="text-sm text-gray-600">
        Showing {{ patients.length }} of {{ totalPatients }} patients
      </p>
      <div class="flex justify-center space-x-2">
        <button mat-button
                [disabled]="currentPage === 1"
                (click)="onPageChange(currentPage - 1)">
          Previous
        </button>
        <span class="px-4 py-2">Page {{ currentPage }} of {{ totalPages }}</span>
        <button mat-button
                [disabled]="currentPage === totalPages"
                (click)="onPageChange(currentPage + 1)">
          Next
        </button>
      </div>
    </div>
  </div>
</div>
