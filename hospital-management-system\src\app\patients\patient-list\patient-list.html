<div class="container mx-auto px-4 py-6">
  <div class="flex justify-between items-center mb-6">
    <h1 class="text-2xl font-bold text-gray-900">Patients</h1>
    <button *hasPermission="'manage_patients'"
            mat-raised-button
            color="primary"
            routerLink="/patients/new">
      <mat-icon>person_add</mat-icon>
      Add Patient
    </button>
  </div>

  <!-- Loading State -->
  <div *ngIf="loading" class="text-center py-8">
    <mat-spinner></mat-spinner>
    <p class="mt-4 text-gray-600">Loading patients...</p>
  </div>

  <!-- Error State -->
  <div *ngIf="error && !loading" class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
    <div class="flex items-center">
      <mat-icon class="text-red-500 mr-2">error</mat-icon>
      <p class="text-red-700">{{ error }}</p>
    </div>
    <button mat-button color="primary" (click)="loadPatients()" class="mt-2">
      Try Again
    </button>
  </div>

  <!-- Empty State -->
  <div *ngIf="!loading && !error && patients.length === 0" class="text-center py-12">
    <mat-icon class="text-6xl text-gray-400 mb-4">people_outline</mat-icon>
    <h3 class="text-lg font-medium text-gray-900 mb-2">No patients found</h3>
    <p class="text-gray-600 mb-4">Get started by adding your first patient.</p>
    <button *hasPermission="'manage_patients'"
            mat-raised-button
            color="primary"
            routerLink="/patients/new">
      <mat-icon>person_add</mat-icon>
      Add Patient
    </button>
  </div>

  <!-- Patients List -->
  <div *ngIf="!loading && !error && patients.length > 0">
    <mat-card class="overflow-hidden">
      <div class="overflow-x-auto">
        <table mat-table [dataSource]="patients" class="w-full">
          <!-- Patient ID Column -->
          <ng-container matColumnDef="patientId">
            <th mat-header-cell *matHeaderCellDef class="font-semibold">Patient ID</th>
            <td mat-cell *matCellDef="let patient">{{ patient.patientId || 'N/A' }}</td>
          </ng-container>

          <!-- Name Column -->
          <ng-container matColumnDef="name">
            <th mat-header-cell *matHeaderCellDef class="font-semibold">Name</th>
            <td mat-cell *matCellDef="let patient">
              <div class="flex items-center">
                <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                  <mat-icon class="text-blue-600 text-sm">person</mat-icon>
                </div>
                <div>
                  <p class="font-medium">{{ patient.firstName }} {{ patient.lastName }}</p>
                  <p class="text-sm text-gray-600">{{ patient.email }}</p>
                </div>
              </div>
            </td>
          </ng-container>

          <!-- Contact Column -->
          <ng-container matColumnDef="contact">
            <th mat-header-cell *matHeaderCellDef class="font-semibold">Contact</th>
            <td mat-cell *matCellDef="let patient">
              <div>
                <p>{{ patient.phone }}</p>
                <p class="text-sm text-gray-600">{{ patient.address?.city || 'N/A' }}</p>
              </div>
            </td>
          </ng-container>

          <!-- Age/Gender Column -->
          <ng-container matColumnDef="demographics">
            <th mat-header-cell *matHeaderCellDef class="font-semibold">Age/Gender</th>
            <td mat-cell *matCellDef="let patient">
              <div>
                <p>{{ getAge(patient.dateOfBirth) || 'N/A' }} years</p>
                <p class="text-sm text-gray-600">{{ patient.gender || 'N/A' }}</p>
              </div>
            </td>
          </ng-container>

          <!-- Status Column -->
          <ng-container matColumnDef="status">
            <th mat-header-cell *matHeaderCellDef class="font-semibold">Status</th>
            <td mat-cell *matCellDef="let patient">
              <span class="px-2 py-1 text-xs rounded-full"
                    [ngClass]="{
                      'bg-green-100 text-green-800': patient.isActive,
                      'bg-red-100 text-red-800': !patient.isActive
                    }">
                {{ patient.isActive ? 'Active' : 'Inactive' }}
              </span>
            </td>
          </ng-container>

          <!-- Actions Column -->
          <ng-container matColumnDef="actions">
            <th mat-header-cell *matHeaderCellDef class="font-semibold">Actions</th>
            <td mat-cell *matCellDef="let patient">
              <button mat-icon-button [routerLink]="['/patients', patient._id]">
                <mat-icon>visibility</mat-icon>
              </button>
              <button *hasPermission="'manage_patients'"
                      mat-icon-button
                      [routerLink]="['/patients', patient._id, 'edit']">
                <mat-icon>edit</mat-icon>
              </button>
            </td>
          </ng-container>

          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
        </table>
      </div>
    </mat-card>

    <!-- Pagination -->
    <div class="flex justify-between items-center mt-6" *ngIf="totalPages > 1">
      <p class="text-sm text-gray-600">
        Showing {{ patients.length }} of {{ totalPatients }} patients
      </p>
      <div class="flex justify-center space-x-2">
        <button mat-button
                [disabled]="currentPage === 1"
                (click)="onPageChange(currentPage - 1)">
          Previous
        </button>
        <span class="px-4 py-2">Page {{ currentPage }} of {{ totalPages }}</span>
        <button mat-button
                [disabled]="currentPage === totalPages"
                (click)="onPageChange(currentPage + 1)">
          Next
        </button>
      </div>
    </div>
  </div>
</div>
