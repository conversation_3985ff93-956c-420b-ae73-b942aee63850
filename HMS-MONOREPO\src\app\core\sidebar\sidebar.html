<div class="h-full bg-white border-r border-gray-200">
  <!-- Navigation Menu -->
  <nav class="mt-4">
    <mat-nav-list>
      <!-- Dashboard - All users -->
      <a mat-list-item routerLink="/dashboard" routerLinkActive="bg-primary-50 text-primary-600">
        <mat-icon matListItemIcon>dashboard</mat-icon>
        <span matListItemTitle>Dashboard</span>
      </a>

      <!-- Patients - Admin and Doctors only -->
      <a *hasPermission="['view_patients', 'manage_patients']"
         mat-list-item routerLink="/patients" routerLinkActive="bg-primary-50 text-primary-600">
        <mat-icon matListItemIcon>people</mat-icon>
        <span matListItemTitle>Patients</span>
      </a>

      <!-- My Profile - Patients only -->
      <a *hasRole="'Patient'"
         mat-list-item routerLink="/profile" routerLinkActive="bg-primary-50 text-primary-600">
        <mat-icon matListItemIcon>person</mat-icon>
        <span matListItemTitle>My Profile</span>
      </a>

      <!-- Doctors - All users can view -->
      <a mat-list-item routerLink="/doctors" routerLinkActive="bg-primary-50 text-primary-600">
        <mat-icon matListItemIcon>medical_services</mat-icon>
        <span matListItemTitle>Doctors</span>
      </a>

      <!-- Appointments - All users -->
      <a mat-list-item routerLink="/appointments" routerLinkActive="bg-primary-50 text-primary-600">
        <mat-icon matListItemIcon>event</mat-icon>
        <span matListItemTitle>Appointments</span>
      </a>

      <!-- Prescriptions - All users -->
      <a mat-list-item routerLink="/prescriptions" routerLinkActive="bg-primary-50 text-primary-600">
        <mat-icon matListItemIcon>medication</mat-icon>
        <span matListItemTitle>Prescriptions</span>
      </a>

      <!-- Reports - All users -->
      <a mat-list-item routerLink="/reports" routerLinkActive="bg-primary-50 text-primary-600">
        <mat-icon matListItemIcon>assessment</mat-icon>
        <span matListItemTitle>Reports</span>
      </a>

      <!-- Billing - Admin and Doctors only -->
      <a *hasPermission="['manage_appointments', 'manage_system']"
         mat-list-item routerLink="/billing" routerLinkActive="bg-primary-50 text-primary-600">
        <mat-icon matListItemIcon>receipt</mat-icon>
        <span matListItemTitle>Billing</span>
      </a>

      <mat-divider class="my-4"></mat-divider>

      <!-- User Management - Admin only -->
      <a *hasRole="'Admin'"
         mat-list-item routerLink="/users" routerLinkActive="bg-primary-50 text-primary-600">
        <mat-icon matListItemIcon>group</mat-icon>
        <span matListItemTitle>User Management</span>
      </a>

      <!-- Settings - Admin only -->
      <a *hasRole="'Admin'"
         mat-list-item routerLink="/settings" routerLinkActive="bg-primary-50 text-primary-600">
        <mat-icon matListItemIcon>settings</mat-icon>
        <span matListItemTitle>Settings</span>
      </a>
    </mat-nav-list>
  </nav>
</div>
