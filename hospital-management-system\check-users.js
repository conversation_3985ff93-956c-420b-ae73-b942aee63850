const mongoose = require('mongoose');
const User = require('./back-end/models/User');

async function checkUsers() {
  try {
    await mongoose.connect('mongodb://localhost:27017/hospital_management');
    console.log('🔍 Database में stored users:');
    console.log('================================');
    
    const users = await User.find({}).select('username email role firstName lastName isActive permissions');
    
    if (users.length === 0) {
      console.log('❌ Database में कोई users नहीं मिले!');
      console.log('Test users create करने के लिए test-rbac.js run करें');
    } else {
      users.forEach(user => {
        console.log(`👤 ${user.role}: ${user.firstName} ${user.lastName}`);
        console.log(`   Email: ${user.email}`);
        console.log(`   Username: ${user.username}`);
        console.log(`   Active: ${user.isActive}`);
        console.log(`   Permissions: ${user.permissions.join(', ')}`);
        console.log('   ---');
      });
      
      console.log(`\nTotal users in database: ${users.length}`);
      
      console.log('\n🔑 Login Credentials:');
      console.log('=====================');
      users.forEach(user => {
        const defaultPassword = user.role.toLowerCase() + '123';
        console.log(`${user.role}: ${user.email} / ${defaultPassword}`);
      });
    }
    
  } catch (error) {
    console.error('❌ Database error:', error.message);
  } finally {
    await mongoose.disconnect();
    process.exit(0);
  }
}

checkUsers();
