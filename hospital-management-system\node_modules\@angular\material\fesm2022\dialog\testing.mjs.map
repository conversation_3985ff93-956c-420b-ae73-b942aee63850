{"version": 3, "file": "testing.mjs", "sources": ["../../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/material/dialog/testing/dialog-harness.ts", "../../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/material/dialog/testing/dialog-opener.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  ComponentHarnessConstructor,\n  ContentContainerComponentHarness,\n  HarnessPredicate,\n  TestKey,\n} from '@angular/cdk/testing';\nimport {DialogHarnessFilters} from './dialog-harness-filters';\nimport {DialogRole} from '../../dialog';\n\n/** Selectors for different sections of the mat-dialog that can contain user content. */\nexport enum MatDialogSection {\n  TITLE = '.mat-mdc-dialog-title',\n  CONTENT = '.mat-mdc-dialog-content',\n  ACTIONS = '.mat-mdc-dialog-actions',\n}\n\n/** Harness for interacting with a standard `MatDialog` in tests. */\nexport class MatDialogHarness\n  // @breaking-change 14.0.0 change generic type to MatDialogSection.\n  extends ContentContainerComponentHarness<MatDialogSection | string>\n{\n  /** The selector for the host element of a `MatDialog` instance. */\n  static hostSelector = '.mat-mdc-dialog-container';\n\n  /**\n   * Gets a `HarnessPredicate` that can be used to search for a dialog with specific attributes.\n   * @param options Options for filtering which dialog instances are considered a match.\n   * @return a `HarnessPredicate` configured with the given options.\n   */\n  static with<T extends MatDialogHarness>(\n    this: ComponentHarnessConstructor<T>,\n    options: DialogHarnessFilters = {},\n  ): HarnessPredicate<T> {\n    return new HarnessPredicate(this, options);\n  }\n\n  protected _title = this.locatorForOptional(MatDialogSection.TITLE);\n  protected _content = this.locatorForOptional(MatDialogSection.CONTENT);\n  protected _actions = this.locatorForOptional(MatDialogSection.ACTIONS);\n\n  /** Gets the id of the dialog. */\n  async getId(): Promise<string | null> {\n    const id = await (await this.host()).getAttribute('id');\n    // In case no id has been specified, the \"id\" property always returns\n    // an empty string. To make this method more explicit, we return null.\n    return id !== '' ? id : null;\n  }\n\n  /** Gets the role of the dialog. */\n  async getRole(): Promise<DialogRole | null> {\n    return (await this.host()).getAttribute('role') as Promise<DialogRole | null>;\n  }\n\n  /** Gets the value of the dialog's \"aria-label\" attribute. */\n  async getAriaLabel(): Promise<string | null> {\n    return (await this.host()).getAttribute('aria-label');\n  }\n\n  /** Gets the value of the dialog's \"aria-labelledby\" attribute. */\n  async getAriaLabelledby(): Promise<string | null> {\n    return (await this.host()).getAttribute('aria-labelledby');\n  }\n\n  /** Gets the value of the dialog's \"aria-describedby\" attribute. */\n  async getAriaDescribedby(): Promise<string | null> {\n    return (await this.host()).getAttribute('aria-describedby');\n  }\n\n  /**\n   * Closes the dialog by pressing escape.\n   *\n   * Note: this method does nothing if `disableClose` has been set to `true` for the dialog.\n   */\n  async close(): Promise<void> {\n    await (await this.host()).sendKeys(TestKey.ESCAPE);\n  }\n\n  /** Gets the dialog's text. */\n  async getText() {\n    return (await this.host()).text();\n  }\n\n  /** Gets the dialog's title text. This only works if the dialog is using mat-dialog-title. */\n  async getTitleText() {\n    return (await this._title())?.text() ?? '';\n  }\n\n  /** Gets the dialog's content text. This only works if the dialog is using mat-dialog-content. */\n  async getContentText() {\n    return (await this._content())?.text() ?? '';\n  }\n\n  /** Gets the dialog's actions text. This only works if the dialog is using mat-dialog-actions. */\n  async getActionsText() {\n    return (await this._actions())?.text() ?? '';\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {ComponentType} from '@angular/cdk/overlay';\nimport {\n  ChangeDetectionStrategy,\n  Component,\n  NgModule,\n  NgZone,\n  OnDestroy,\n  ViewEncapsulation,\n  inject,\n} from '@angular/core';\nimport {MatDialog, MatDialogConfig, MatDialogModule, MatDialogRef} from '../../dialog';\nimport {Subscription} from 'rxjs';\nimport {AnimationsConfig, MATERIAL_ANIMATIONS} from '@angular/material/core';\n\n/** Test component that immediately opens a dialog when bootstrapped. */\n@Component({\n  selector: 'mat-test-dialog-opener',\n  template: '',\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  encapsulation: ViewEncapsulation.None,\n})\nexport class MatTestDialogOpener<T = unknown, R = unknown> implements OnD<PERSON>roy {\n  dialog = inject(MatDialog);\n\n  /** Component that should be opened with the MatDialog `open` method. */\n  protected static component: ComponentType<unknown> | undefined;\n\n  /** Config that should be provided to the MatDialog `open` method. */\n  protected static config: MatDialogConfig | undefined;\n\n  /** MatDialogRef returned from the MatDialog `open` method. */\n  dialogRef: MatDialogRef<T, R>;\n\n  /** Data passed to the `MatDialog` close method. */\n  closedResult: R | undefined;\n\n  private readonly _afterClosedSubscription: Subscription;\n\n  private readonly _ngZone = inject(NgZone);\n\n  /** Static method that prepares this class to open the provided component. */\n  static withComponent<T = unknown, R = unknown>(\n    component: ComponentType<T>,\n    config?: MatDialogConfig,\n  ) {\n    MatTestDialogOpener.component = component;\n    MatTestDialogOpener.config = config;\n    return MatTestDialogOpener as ComponentType<MatTestDialogOpener<T, R>>;\n  }\n\n  constructor(...args: unknown[]);\n\n  constructor() {\n    if (!MatTestDialogOpener.component) {\n      throw new Error(`MatTestDialogOpener does not have a component provided.`);\n    }\n\n    this.dialogRef = this._ngZone.run(() => {\n      const config = {...(MatTestDialogOpener.config || {})};\n      config.enterAnimationDuration = 0;\n      config.exitAnimationDuration = 0;\n      return this.dialog.open<T, R>(MatTestDialogOpener.component as ComponentType<T>, config);\n    });\n    this._afterClosedSubscription = this.dialogRef.afterClosed().subscribe(result => {\n      this.closedResult = result;\n    });\n  }\n\n  ngOnDestroy() {\n    this._afterClosedSubscription.unsubscribe();\n    MatTestDialogOpener.component = undefined;\n    MatTestDialogOpener.config = undefined;\n  }\n}\n\n@NgModule({\n  imports: [MatDialogModule, MatTestDialogOpener],\n  providers: [\n    {\n      provide: MATERIAL_ANIMATIONS,\n      useValue: {\n        animationsDisabled: true,\n      } as AnimationsConfig,\n    },\n  ],\n})\nexport class MatTestDialogOpenerModule {}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;AAiBA;IACY;AAAZ,CAAA,UAAY,gBAAgB,EAAA;AAC1B,IAAA,gBAAA,CAAA,OAAA,CAAA,GAAA,uBAA+B;AAC/B,IAAA,gBAAA,CAAA,SAAA,CAAA,GAAA,yBAAmC;AACnC,IAAA,gBAAA,CAAA,SAAA,CAAA,GAAA,yBAAmC;AACrC,CAAC,EAJW,gBAAgB,KAAhB,gBAAgB,GAI3B,EAAA,CAAA,CAAA;AAED;MACa;AACX;AACA,SAAQ,gCAA2D,CAAA;;AAGnE,IAAA,OAAO,YAAY,GAAG,2BAA2B;AAEjD;;;;AAIG;AACH,IAAA,OAAO,IAAI,CAET,OAAA,GAAgC,EAAE,EAAA;AAElC,QAAA,OAAO,IAAI,gBAAgB,CAAC,IAAI,EAAE,OAAO,CAAC;;IAGlC,MAAM,GAAG,IAAI,CAAC,kBAAkB,CAAC,gBAAgB,CAAC,KAAK,CAAC;IACxD,QAAQ,GAAG,IAAI,CAAC,kBAAkB,CAAC,gBAAgB,CAAC,OAAO,CAAC;IAC5D,QAAQ,GAAG,IAAI,CAAC,kBAAkB,CAAC,gBAAgB,CAAC,OAAO,CAAC;;AAGtE,IAAA,MAAM,KAAK,GAAA;AACT,QAAA,MAAM,EAAE,GAAG,MAAM,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,YAAY,CAAC,IAAI,CAAC;;;QAGvD,OAAO,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,IAAI;;;AAI9B,IAAA,MAAM,OAAO,GAAA;AACX,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,YAAY,CAAC,MAAM,CAA+B;;;AAI/E,IAAA,MAAM,YAAY,GAAA;AAChB,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,YAAY,CAAC,YAAY,CAAC;;;AAIvD,IAAA,MAAM,iBAAiB,GAAA;AACrB,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,YAAY,CAAC,iBAAiB,CAAC;;;AAI5D,IAAA,MAAM,kBAAkB,GAAA;AACtB,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,YAAY,CAAC,kBAAkB,CAAC;;AAG7D;;;;AAIG;AACH,IAAA,MAAM,KAAK,GAAA;AACT,QAAA,MAAM,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC;;;AAIpD,IAAA,MAAM,OAAO,GAAA;QACX,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,IAAI,EAAE;;;AAInC,IAAA,MAAM,YAAY,GAAA;AAChB,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,MAAM,EAAE,GAAG,IAAI,EAAE,IAAI,EAAE;;;AAI5C,IAAA,MAAM,cAAc,GAAA;AAClB,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,QAAQ,EAAE,GAAG,IAAI,EAAE,IAAI,EAAE;;;AAI9C,IAAA,MAAM,cAAc,GAAA;AAClB,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,QAAQ,EAAE,GAAG,IAAI,EAAE,IAAI,EAAE;;;;;AChFhD;AAOa,IAAA,mBAAmB,GAAzB,MAAM,mBAAmB,CAAA;;AAC9B,IAAA,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC;;IAGhB,OAAO,SAAS;;IAGhB,OAAO,MAAM;;AAGvB,IAAA,SAAS;;AAGT,IAAA,YAAY;AAEK,IAAA,wBAAwB;AAExB,IAAA,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC;;AAGzC,IAAA,OAAO,aAAa,CAClB,SAA2B,EAC3B,MAAwB,EAAA;AAExB,QAAA,qBAAmB,CAAC,SAAS,GAAG,SAAS;AACzC,QAAA,qBAAmB,CAAC,MAAM,GAAG,MAAM;AACnC,QAAA,OAAO,qBAA+D;;AAKxE,IAAA,WAAA,GAAA;AACE,QAAA,IAAI,CAAC,qBAAmB,CAAC,SAAS,EAAE;AAClC,YAAA,MAAM,IAAI,KAAK,CAAC,CAAA,uDAAA,CAAyD,CAAC;;QAG5E,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAK;AACrC,YAAA,MAAM,MAAM,GAAG,EAAC,IAAI,qBAAmB,CAAC,MAAM,IAAI,EAAE,CAAC,EAAC;AACtD,YAAA,MAAM,CAAC,sBAAsB,GAAG,CAAC;AACjC,YAAA,MAAM,CAAC,qBAAqB,GAAG,CAAC;AAChC,YAAA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAO,qBAAmB,CAAC,SAA6B,EAAE,MAAM,CAAC;AAC1F,SAAC,CAAC;AACF,QAAA,IAAI,CAAC,wBAAwB,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC,SAAS,CAAC,MAAM,IAAG;AAC9E,YAAA,IAAI,CAAC,YAAY,GAAG,MAAM;AAC5B,SAAC,CAAC;;IAGJ,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,wBAAwB,CAAC,WAAW,EAAE;AAC3C,QAAA,qBAAmB,CAAC,SAAS,GAAG,SAAS;AACzC,QAAA,qBAAmB,CAAC,MAAM,GAAG,SAAS;;;AAlD7B,mBAAmB,GAAA,qBAAA,GAAA,UAAA,CAAA;AAN/B,IAAA,SAAS,CAAC;AACT,QAAA,QAAQ,EAAE,wBAAwB;AAClC,QAAA,QAAQ,EAAE,EAAE;QACZ,eAAe,EAAE,uBAAuB,CAAC,MAAM;QAC/C,aAAa,EAAE,iBAAiB,CAAC,IAAI;KACtC,CAAC;;AACW,CAAA,EAAA,mBAAmB,CAoD/B;AAaY,IAAA,yBAAyB,GAA/B,MAAM,yBAAyB,CAAA;;AAAzB,yBAAyB,GAAA,UAAA,CAAA;AAXrC,IAAA,QAAQ,CAAC;AACR,QAAA,OAAO,EAAE,CAAC,eAAe,EAAE,mBAAmB,CAAC;AAC/C,QAAA,SAAS,EAAE;AACT,YAAA;AACE,gBAAA,OAAO,EAAE,mBAAmB;AAC5B,gBAAA,QAAQ,EAAE;AACR,oBAAA,kBAAkB,EAAE,IAAI;AACL,iBAAA;AACtB,aAAA;AACF,SAAA;KACF;AACY,CAAA,EAAA,yBAAyB,CAAG;;;;"}