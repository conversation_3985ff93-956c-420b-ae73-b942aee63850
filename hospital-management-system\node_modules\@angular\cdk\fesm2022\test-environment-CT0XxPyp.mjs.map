{"version": 3, "file": "test-environment-CT0XxPyp.mjs", "sources": ["../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/cdk/platform/features/test-environment.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n/** Gets whether the code is currently running in a test environment. */\nexport function _isTestEnvironment(): boolean {\n  // We can't use `declare const` because it causes conflicts inside Google with the real typings\n  // for these symbols and we can't read them off the global object, because they don't appear to\n  // be attached there for some runners like Jest.\n  // (see: https://github.com/angular/components/issues/23365#issuecomment-938146643)\n  return (\n    // @ts-ignore\n    (typeof __karma__ !== 'undefined' && !!__karma__) ||\n    // @ts-ignore\n    (typeof jasmine !== 'undefined' && !!jasmine) ||\n    // @ts-ignore\n    (typeof jest !== 'undefined' && !!jest) ||\n    // @ts-ignore\n    (typeof Mocha !== 'undefined' && !!Mocha)\n  );\n}\n"], "names": [], "mappings": "AAQA;SACgB,kBAAkB,GAAA;;;;;IAKhC;;IAEE,CAAC,OAAO,SAAS,KAAK,WAAW,IAAI,CAAC,CAAC,SAAS;;SAE/C,OAAO,OAAO,KAAK,WAAW,IAAI,CAAC,CAAC,OAAO,CAAC;;SAE5C,OAAO,IAAI,KAAK,WAAW,IAAI,CAAC,CAAC,IAAI,CAAC;;SAEtC,OAAO,KAAK,KAAK,WAAW,IAAI,CAAC,CAAC,KAAK,CAAC;AAE7C;;;;"}