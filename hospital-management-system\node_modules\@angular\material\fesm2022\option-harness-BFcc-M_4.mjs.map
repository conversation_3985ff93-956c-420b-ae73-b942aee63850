{"version": 3, "file": "option-harness-BFcc-M_4.mjs", "sources": ["../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/material/core/testing/option-harness.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  ComponentHarnessConstructor,\n  ContentContainerComponentHarness,\n  HarnessPredicate,\n} from '@angular/cdk/testing';\nimport {OptionHarnessFilters} from './option-harness-filters';\n\n/** <PERSON><PERSON><PERSON> for interacting with a `mat-option` in tests. */\nexport class MatOptionHarness extends ContentContainerComponentHarness {\n  /** Selector used to locate option instances. */\n  static hostSelector = '.mat-mdc-option';\n\n  /** Element containing the option's text. */\n  private _text = this.locatorFor('.mdc-list-item__primary-text');\n\n  /**\n   * Gets a `HarnessPredicate` that can be used to search for an option with specific attributes.\n   * @param options Options for filtering which option instances are considered a match.\n   * @return a `HarnessPredicate` configured with the given options.\n   */\n  static with<T extends MatOptionHarness>(\n    this: ComponentHarnessConstructor<T>,\n    options: OptionHarnessFilters = {},\n  ): HarnessPredicate<T> {\n    return new HarnessPredicate(this, options)\n      .addOption('text', options.text, async (harness, title) =>\n        HarnessPredicate.stringMatches(await harness.getText(), title),\n      )\n      .addOption(\n        'isSelected',\n        options.isSelected,\n        async (harness, isSelected) => (await harness.isSelected()) === isSelected,\n      );\n  }\n\n  /** Clicks the option. */\n  async click(): Promise<void> {\n    return (await this.host()).click();\n  }\n\n  /** Gets the option's label text. */\n  async getText(): Promise<string> {\n    return (await this._text()).text();\n  }\n\n  /** Gets whether the option is disabled. */\n  async isDisabled(): Promise<boolean> {\n    return (await this.host()).hasClass('mdc-list-item--disabled');\n  }\n\n  /** Gets whether the option is selected. */\n  async isSelected(): Promise<boolean> {\n    return (await this.host()).hasClass('mdc-list-item--selected');\n  }\n\n  /** Gets whether the option is active. */\n  async isActive(): Promise<boolean> {\n    return (await this.host()).hasClass('mat-mdc-option-active');\n  }\n\n  /** Gets whether the option is in multiple selection mode. */\n  async isMultiple(): Promise<boolean> {\n    return (await this.host()).hasClass('mat-mdc-option-multiple');\n  }\n}\n"], "names": [], "mappings": ";;AAeA;AACM,MAAO,gBAAiB,SAAQ,gCAAgC,CAAA;;AAEpE,IAAA,OAAO,YAAY,GAAG,iBAAiB;;AAG/B,IAAA,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,8BAA8B,CAAC;AAE/D;;;;AAIG;AACH,IAAA,OAAO,IAAI,CAET,OAAA,GAAgC,EAAE,EAAA;AAElC,QAAA,OAAO,IAAI,gBAAgB,CAAC,IAAI,EAAE,OAAO;aACtC,SAAS,CAAC,MAAM,EAAE,OAAO,CAAC,IAAI,EAAE,OAAO,OAAO,EAAE,KAAK,KACpD,gBAAgB,CAAC,aAAa,CAAC,MAAM,OAAO,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC;aAE/D,SAAS,CACR,YAAY,EACZ,OAAO,CAAC,UAAU,EAClB,OAAO,OAAO,EAAE,UAAU,KAAK,CAAC,MAAM,OAAO,CAAC,UAAU,EAAE,MAAM,UAAU,CAC3E;;;AAIL,IAAA,MAAM,KAAK,GAAA;QACT,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,KAAK,EAAE;;;AAIpC,IAAA,MAAM,OAAO,GAAA;QACX,OAAO,CAAC,MAAM,IAAI,CAAC,KAAK,EAAE,EAAE,IAAI,EAAE;;;AAIpC,IAAA,MAAM,UAAU,GAAA;AACd,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,QAAQ,CAAC,yBAAyB,CAAC;;;AAIhE,IAAA,MAAM,UAAU,GAAA;AACd,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,QAAQ,CAAC,yBAAyB,CAAC;;;AAIhE,IAAA,MAAM,QAAQ,GAAA;AACZ,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,QAAQ,CAAC,uBAAuB,CAAC;;;AAI9D,IAAA,MAAM,UAAU,GAAA;AACd,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,QAAQ,CAAC,yBAAyB,CAAC;;;;;;"}