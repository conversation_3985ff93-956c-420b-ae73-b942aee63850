@use 'sass:map';
@use 'sass:list';
@use '../core/tokens/m3-utils';
@use '../core/theming/theming';
@use '../core/theming/inspection';
@use '../core/tokens/m3';

/// Generates custom tokens for mat-checkbox.
@function get-tokens($theme: m3.$sys-theme, $color-variant: null) {
  $system: m3-utils.get-system($theme);
  @if $color-variant {
    $system: m3-utils.replace-colors-with-variant($system, primary, $color-variant);
  }

  @return (
    base: (
      checkbox-selected-focus-state-layer-opacity: map.get($system, focus-state-layer-opacity),
      checkbox-selected-hover-state-layer-opacity: map.get($system, hover-state-layer-opacity),
      checkbox-selected-pressed-state-layer-opacity: map.get($system, pressed-state-layer-opacity),
      checkbox-unselected-focus-state-layer-opacity: map.get($system, focus-state-layer-opacity),
      checkbox-unselected-hover-state-layer-opacity: map.get($system, hover-state-layer-opacity),
      checkbox-unselected-pressed-state-layer-opacity:
          map.get($system, pressed-state-layer-opacity),
    ),
    color: (
      checkbox-disabled-label-color: m3-utils.color-with-opacity(map.get($system, on-surface), 38%),
      checkbox-disabled-selected-checkmark-color: map.get($system, surface),
      checkbox-disabled-selected-icon-color:
          m3-utils.color-with-opacity(map.get($system, on-surface), 38%),
      checkbox-disabled-unselected-icon-color:
          m3-utils.color-with-opacity(map.get($system, on-surface), 38%),
      checkbox-label-text-color: map.get($system, on-surface),
      checkbox-selected-checkmark-color: map.get($system, on-primary),
      checkbox-selected-focus-icon-color: map.get($system, primary),
      checkbox-selected-focus-state-layer-color: map.get($system, primary),
      checkbox-selected-hover-icon-color: map.get($system, primary),
      checkbox-selected-hover-state-layer-color: map.get($system, primary),
      checkbox-selected-icon-color: map.get($system, primary),
      checkbox-selected-pressed-icon-color: map.get($system, primary),
      checkbox-selected-pressed-state-layer-color: map.get($system, on-surface),
      checkbox-unselected-focus-icon-color: map.get($system, on-surface),
      checkbox-unselected-focus-state-layer-color: map.get($system, on-surface),
      checkbox-unselected-hover-icon-color: map.get($system, on-surface),
      checkbox-unselected-hover-state-layer-color: map.get($system, on-surface),
      checkbox-unselected-icon-color: map.get($system, on-surface-variant),
      checkbox-unselected-pressed-state-layer-color: map.get($system, primary),
    ),
    typography: (
      checkbox-label-text-font: map.get($system, body-medium-font),
      checkbox-label-text-line-height: map.get($system, body-medium-line-height),
      checkbox-label-text-size: map.get($system, body-medium-size),
      checkbox-label-text-tracking: map.get($system, body-medium-tracking),
      checkbox-label-text-weight: map.get($system, body-medium-weight),
    ),
    density: get-density-tokens(map.get($theme, inspection.$internals, density-scale)),
  );
}

// Tokens that can be configured through Angular Material's density theming API.
@function get-density-tokens($scale) {
  $scale: theming.clamp-density($scale, -3);
  $index: ($scale * -1) + 1;

  @return (
    checkbox-touch-target-display: list.nth((block, block, none, none), $index),
    checkbox-state-layer-size: list.nth((40px, 36px, 32px, 28px), $index)
  );
}
