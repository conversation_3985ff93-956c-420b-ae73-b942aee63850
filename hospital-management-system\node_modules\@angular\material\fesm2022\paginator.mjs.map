{"version": 3, "file": "paginator.mjs", "sources": ["../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/material/paginator/paginator-intl.ts", "../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/material/paginator/paginator.ts", "../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/material/paginator/paginator.html", "../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/material/paginator/module.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Injectable, Optional, SkipSelf} from '@angular/core';\nimport {Subject} from 'rxjs';\n\n/**\n * To modify the labels and text displayed, create a new instance of MatPaginatorIntl and\n * include it in a custom provider\n */\n@Injectable({providedIn: 'root'})\nexport class MatPaginatorIntl {\n  /**\n   * Stream to emit from when labels are changed. Use this to notify components when the labels have\n   * changed after initialization.\n   */\n  readonly changes: Subject<void> = new Subject<void>();\n\n  /** A label for the page size selector. */\n  itemsPerPageLabel: string = 'Items per page:';\n\n  /** A label for the button that increments the current page. */\n  nextPageLabel: string = 'Next page';\n\n  /** A label for the button that decrements the current page. */\n  previousPageLabel: string = 'Previous page';\n\n  /** A label for the button that moves to the first page. */\n  firstPageLabel: string = 'First page';\n\n  /** A label for the button that moves to the last page. */\n  lastPageLabel: string = 'Last page';\n\n  /** A label for the range of items within the current page and the length of the whole list. */\n  getRangeLabel: (page: number, pageSize: number, length: number) => string = (\n    page: number,\n    pageSize: number,\n    length: number,\n  ) => {\n    if (length == 0 || pageSize == 0) {\n      return `0 of ${length}`;\n    }\n\n    length = Math.max(length, 0);\n\n    const startIndex = page * pageSize;\n\n    // If the start index exceeds the list length, do not try and fix the end index to the end.\n    const endIndex =\n      startIndex < length ? Math.min(startIndex + pageSize, length) : startIndex + pageSize;\n\n    return `${startIndex + 1} – ${endIndex} of ${length}`;\n  };\n}\n\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nexport function MAT_PAGINATOR_INTL_PROVIDER_FACTORY(parentIntl: MatPaginatorIntl) {\n  return parentIntl || new MatPaginatorIntl();\n}\n\n/**\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nexport const MAT_PAGINATOR_INTL_PROVIDER = {\n  // If there is already an MatPaginatorIntl available, use that. Otherwise, provide a new one.\n  provide: MatPaginatorIntl,\n  deps: [[new Optional(), new SkipSelf(), MatPaginatorIntl]],\n  useFactory: MAT_PAGINATOR_INTL_PROVIDER_FACTORY,\n};\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  ChangeDetectionStrategy,\n  ChangeDetectorRef,\n  Component,\n  EventEmitter,\n  InjectionToken,\n  Input,\n  OnDestroy,\n  OnInit,\n  Output,\n  ViewEncapsulation,\n  booleanAttribute,\n  inject,\n  numberAttribute,\n} from '@angular/core';\nimport {_IdGenerator} from '@angular/cdk/a11y';\nimport {MatOption, ThemePalette} from '../core';\nimport {MatSelect} from '../select';\nimport {MatIconButton} from '../button';\nimport {MatTooltip} from '../tooltip';\nimport {MatF<PERSON>Field, MatFormFieldAppearance} from '../form-field';\nimport {Observable, ReplaySubject, Subscription} from 'rxjs';\nimport {MatPaginatorIntl} from './paginator-intl';\n\n/** The default page size if there is no page size and there are no provided page size options. */\nconst DEFAULT_PAGE_SIZE = 50;\n\n/** Object that can used to configure the underlying `MatSelect` inside a `MatPaginator`. */\nexport interface MatPaginatorSelectConfig {\n  /** Whether to center the active option over the trigger. */\n  disableOptionCentering?: boolean;\n\n  /** Classes to be passed to the select panel. */\n  panelClass?: string | string[] | Set<string> | {[key: string]: any};\n}\n\n/**\n * Change event object that is emitted when the user selects a\n * different page size or navigates to another page.\n */\nexport class PageEvent {\n  /** The current page index. */\n  pageIndex: number;\n\n  /**\n   * Index of the page that was selected previously.\n   * @breaking-change 8.0.0 To be made into a required property.\n   */\n  previousPageIndex?: number;\n\n  /** The current page size. */\n  pageSize: number;\n\n  /** The current total number of items being paged. */\n  length: number;\n}\n\n// Note that while `MatPaginatorDefaultOptions` and `MAT_PAGINATOR_DEFAULT_OPTIONS` are identical\n// between the MDC and non-MDC versions, we have to duplicate them, because the type of\n// `formFieldAppearance` is narrower in the MDC version.\n\n/** Object that can be used to configure the default options for the paginator module. */\nexport interface MatPaginatorDefaultOptions {\n  /** Number of items to display on a page. By default set to 50. */\n  pageSize?: number;\n\n  /** The set of provided page size options to display to the user. */\n  pageSizeOptions?: number[];\n\n  /** Whether to hide the page size selection UI from the user. */\n  hidePageSize?: boolean;\n\n  /** Whether to show the first/last buttons UI to the user. */\n  showFirstLastButtons?: boolean;\n\n  /** The default form-field appearance to apply to the page size options selector. */\n  formFieldAppearance?: MatFormFieldAppearance;\n}\n\n/** Injection token that can be used to provide the default options for the paginator module. */\nexport const MAT_PAGINATOR_DEFAULT_OPTIONS = new InjectionToken<MatPaginatorDefaultOptions>(\n  'MAT_PAGINATOR_DEFAULT_OPTIONS',\n);\n\n/**\n * Component to provide navigation between paged information. Displays the size of the current\n * page, user-selectable options to change that size, what items are being shown, and\n * navigational button to go to the previous or next page.\n */\n@Component({\n  selector: 'mat-paginator',\n  exportAs: 'matPaginator',\n  templateUrl: 'paginator.html',\n  styleUrl: 'paginator.css',\n  host: {\n    'class': 'mat-mdc-paginator',\n    'role': 'group',\n  },\n  changeDetection: ChangeDetectionStrategy.OnPush,\n  encapsulation: ViewEncapsulation.None,\n  imports: [MatFormField, MatSelect, MatOption, MatIconButton, MatTooltip],\n})\nexport class MatPaginator implements OnInit, OnDestroy {\n  _intl = inject(MatPaginatorIntl);\n  private _changeDetectorRef = inject(ChangeDetectorRef);\n\n  /** If set, styles the \"page size\" form field with the designated style. */\n  _formFieldAppearance?: MatFormFieldAppearance;\n\n  /** ID for the DOM node containing the paginator's items per page label. */\n  readonly _pageSizeLabelId = inject(_IdGenerator).getId('mat-paginator-page-size-label-');\n\n  private _intlChanges: Subscription;\n  private _isInitialized = false;\n  private _initializedStream = new ReplaySubject<void>(1);\n\n  /**\n   * Theme color of the underlying form controls. This API is supported in M2\n   * themes only,it has no effect in M3 themes. For color customization in M3, see https://material.angular.dev/components/paginator/styling.\n   *\n   * For information on applying color variants in M3, see\n   * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n   */\n  @Input() color: ThemePalette;\n\n  /** The zero-based page index of the displayed list of items. Defaulted to 0. */\n  @Input({transform: numberAttribute})\n  get pageIndex(): number {\n    return this._pageIndex;\n  }\n  set pageIndex(value: number) {\n    this._pageIndex = Math.max(value || 0, 0);\n    this._changeDetectorRef.markForCheck();\n  }\n  private _pageIndex = 0;\n\n  /** The length of the total number of items that are being paginated. Defaulted to 0. */\n  @Input({transform: numberAttribute})\n  get length(): number {\n    return this._length;\n  }\n  set length(value: number) {\n    this._length = value || 0;\n    this._changeDetectorRef.markForCheck();\n  }\n  private _length = 0;\n\n  /** Number of items to display on a page. By default set to 50. */\n  @Input({transform: numberAttribute})\n  get pageSize(): number {\n    return this._pageSize;\n  }\n  set pageSize(value: number) {\n    this._pageSize = Math.max(value || 0, 0);\n    this._updateDisplayedPageSizeOptions();\n  }\n  private _pageSize: number;\n\n  /** The set of provided page size options to display to the user. */\n  @Input()\n  get pageSizeOptions(): number[] {\n    return this._pageSizeOptions;\n  }\n  set pageSizeOptions(value: number[] | readonly number[]) {\n    this._pageSizeOptions = (value || ([] as number[])).map(p => numberAttribute(p, 0));\n    this._updateDisplayedPageSizeOptions();\n  }\n  private _pageSizeOptions: number[] = [];\n\n  /** Whether to hide the page size selection UI from the user. */\n  @Input({transform: booleanAttribute})\n  hidePageSize: boolean = false;\n\n  /** Whether to show the first/last buttons UI to the user. */\n  @Input({transform: booleanAttribute})\n  showFirstLastButtons: boolean = false;\n\n  /** Used to configure the underlying `MatSelect` inside the paginator. */\n  @Input() selectConfig: MatPaginatorSelectConfig = {};\n\n  /** Whether the paginator is disabled. */\n  @Input({transform: booleanAttribute})\n  disabled: boolean = false;\n\n  /** Event emitted when the paginator changes the page size or page index. */\n  @Output() readonly page: EventEmitter<PageEvent> = new EventEmitter<PageEvent>();\n\n  /** Displayed set of page size options. Will be sorted and include current page size. */\n  _displayedPageSizeOptions: number[];\n\n  /** Emits when the paginator is initialized. */\n  initialized: Observable<void> = this._initializedStream;\n\n  /** Inserted by Angular inject() migration for backwards compatibility */\n  constructor(...args: unknown[]);\n\n  constructor() {\n    const _intl = this._intl;\n    const defaults = inject<MatPaginatorDefaultOptions>(MAT_PAGINATOR_DEFAULT_OPTIONS, {\n      optional: true,\n    });\n\n    this._intlChanges = _intl.changes.subscribe(() => this._changeDetectorRef.markForCheck());\n\n    if (defaults) {\n      const {pageSize, pageSizeOptions, hidePageSize, showFirstLastButtons} = defaults;\n\n      if (pageSize != null) {\n        this._pageSize = pageSize;\n      }\n\n      if (pageSizeOptions != null) {\n        this._pageSizeOptions = pageSizeOptions;\n      }\n\n      if (hidePageSize != null) {\n        this.hidePageSize = hidePageSize;\n      }\n\n      if (showFirstLastButtons != null) {\n        this.showFirstLastButtons = showFirstLastButtons;\n      }\n    }\n\n    this._formFieldAppearance = defaults?.formFieldAppearance || 'outline';\n  }\n\n  ngOnInit() {\n    this._isInitialized = true;\n    this._updateDisplayedPageSizeOptions();\n    this._initializedStream.next();\n  }\n\n  ngOnDestroy() {\n    this._initializedStream.complete();\n    this._intlChanges.unsubscribe();\n  }\n\n  /** Advances to the next page if it exists. */\n  nextPage(): void {\n    if (this.hasNextPage()) {\n      this._navigate(this.pageIndex + 1);\n    }\n  }\n\n  /** Move back to the previous page if it exists. */\n  previousPage(): void {\n    if (this.hasPreviousPage()) {\n      this._navigate(this.pageIndex - 1);\n    }\n  }\n\n  /** Move to the first page if not already there. */\n  firstPage(): void {\n    // hasPreviousPage being false implies at the start\n    if (this.hasPreviousPage()) {\n      this._navigate(0);\n    }\n  }\n\n  /** Move to the last page if not already there. */\n  lastPage(): void {\n    // hasNextPage being false implies at the end\n    if (this.hasNextPage()) {\n      this._navigate(this.getNumberOfPages() - 1);\n    }\n  }\n\n  /** Whether there is a previous page. */\n  hasPreviousPage(): boolean {\n    return this.pageIndex >= 1 && this.pageSize != 0;\n  }\n\n  /** Whether there is a next page. */\n  hasNextPage(): boolean {\n    const maxPageIndex = this.getNumberOfPages() - 1;\n    return this.pageIndex < maxPageIndex && this.pageSize != 0;\n  }\n\n  /** Calculate the number of pages */\n  getNumberOfPages(): number {\n    if (!this.pageSize) {\n      return 0;\n    }\n\n    return Math.ceil(this.length / this.pageSize);\n  }\n\n  /**\n   * Changes the page size so that the first item displayed on the page will still be\n   * displayed using the new page size.\n   *\n   * For example, if the page size is 10 and on the second page (items indexed 10-19) then\n   * switching so that the page size is 5 will set the third page as the current page so\n   * that the 10th item will still be displayed.\n   */\n  _changePageSize(pageSize: number) {\n    // Current page needs to be updated to reflect the new page size. Navigate to the page\n    // containing the previous page's first item.\n    const startIndex = this.pageIndex * this.pageSize;\n    const previousPageIndex = this.pageIndex;\n\n    this.pageIndex = Math.floor(startIndex / pageSize) || 0;\n    this.pageSize = pageSize;\n    this._emitPageEvent(previousPageIndex);\n  }\n\n  /** Checks whether the buttons for going forwards should be disabled. */\n  _nextButtonsDisabled() {\n    return this.disabled || !this.hasNextPage();\n  }\n\n  /** Checks whether the buttons for going backwards should be disabled. */\n  _previousButtonsDisabled() {\n    return this.disabled || !this.hasPreviousPage();\n  }\n\n  /**\n   * Updates the list of page size options to display to the user. Includes making sure that\n   * the page size is an option and that the list is sorted.\n   */\n  private _updateDisplayedPageSizeOptions() {\n    if (!this._isInitialized) {\n      return;\n    }\n\n    // If no page size is provided, use the first page size option or the default page size.\n    if (!this.pageSize) {\n      this._pageSize =\n        this.pageSizeOptions.length != 0 ? this.pageSizeOptions[0] : DEFAULT_PAGE_SIZE;\n    }\n\n    this._displayedPageSizeOptions = this.pageSizeOptions.slice();\n\n    if (this._displayedPageSizeOptions.indexOf(this.pageSize) === -1) {\n      this._displayedPageSizeOptions.push(this.pageSize);\n    }\n\n    // Sort the numbers using a number-specific sort function.\n    this._displayedPageSizeOptions.sort((a, b) => a - b);\n    this._changeDetectorRef.markForCheck();\n  }\n\n  /** Emits an event notifying that a change of the paginator's properties has been triggered. */\n  private _emitPageEvent(previousPageIndex: number) {\n    this.page.emit({\n      previousPageIndex,\n      pageIndex: this.pageIndex,\n      pageSize: this.pageSize,\n      length: this.length,\n    });\n  }\n\n  /** Navigates to a specific page index. */\n  private _navigate(index: number) {\n    const previousIndex = this.pageIndex;\n\n    if (index !== previousIndex) {\n      this.pageIndex = index;\n      this._emitPageEvent(previousIndex);\n    }\n  }\n\n  /**\n   * Callback invoked when one of the navigation buttons is called.\n   * @param targetIndex Index to which the paginator should navigate.\n   * @param isDisabled Whether the button is disabled.\n   */\n  protected _buttonClicked(targetIndex: number, isDisabled: boolean) {\n    // Note that normally disabled buttons won't dispatch the click event, but the paginator ones\n    // do, because we're using `disabledInteractive` to allow them to be focusable. We need to\n    // check here to avoid the navigation.\n    if (!isDisabled) {\n      this._navigate(targetIndex);\n    }\n  }\n}\n", "<div class=\"mat-mdc-paginator-outer-container\">\n  <div class=\"mat-mdc-paginator-container\">\n    @if (!hidePageSize) {\n      <div class=\"mat-mdc-paginator-page-size\">\n        <div class=\"mat-mdc-paginator-page-size-label\" [attr.id]=\"_pageSizeLabelId\">\n          {{_intl.itemsPerPageLabel}}\n        </div>\n\n        @if (_displayedPageSizeOptions.length > 1) {\n          <mat-form-field\n            [appearance]=\"_formFieldAppearance!\"\n            [color]=\"color\"\n            class=\"mat-mdc-paginator-page-size-select\">\n            <mat-select\n              #selectRef\n              [value]=\"pageSize\"\n              [disabled]=\"disabled\"\n              [aria-labelledby]=\"_pageSizeLabelId\"\n              [panelClass]=\"selectConfig.panelClass || ''\"\n              [disableOptionCentering]=\"selectConfig.disableOptionCentering\"\n              (selectionChange)=\"_changePageSize($event.value)\"\n              hideSingleSelectionIndicator>\n              @for (pageSizeOption of _displayedPageSizeOptions; track pageSizeOption) {\n                <mat-option [value]=\"pageSizeOption\">\n                  {{pageSizeOption}}\n                </mat-option>\n              }\n            </mat-select>\n          <div class=\"mat-mdc-paginator-touch-target\" (click)=\"selectRef.open()\"></div>\n          </mat-form-field>\n        }\n\n        @if (_displayedPageSizeOptions.length <= 1) {\n          <div class=\"mat-mdc-paginator-page-size-value\">{{pageSize}}</div>\n        }\n      </div>\n    }\n\n    <div class=\"mat-mdc-paginator-range-actions\">\n      <div class=\"mat-mdc-paginator-range-label\" aria-live=\"polite\">\n        {{_intl.getRangeLabel(pageIndex, pageSize, length)}}\n      </div>\n\n      <!--\n      The buttons use `disabledInteractive` so that they can retain focus if they become disabled,\n      otherwise focus is moved to the document body. However, users should not be able to navigate\n      into these buttons, so `tabindex` is set to -1 when disabled.\n      -->\n\n      @if (showFirstLastButtons) {\n        <button matIconButton type=\"button\"\n                class=\"mat-mdc-paginator-navigation-first\"\n                (click)=\"_buttonClicked(0, _previousButtonsDisabled())\"\n                [attr.aria-label]=\"_intl.firstPageLabel\"\n                [matTooltip]=\"_intl.firstPageLabel\"\n                [matTooltipDisabled]=\"_previousButtonsDisabled()\"\n                matTooltipPosition=\"above\"\n                [disabled]=\"_previousButtonsDisabled()\"\n                [tabindex]=\"_previousButtonsDisabled() ? -1 : null\"\n                disabledInteractive>\n          <svg class=\"mat-mdc-paginator-icon\"\n              viewBox=\"0 0 24 24\"\n              focusable=\"false\"\n              aria-hidden=\"true\">\n            <path d=\"M18.41 16.59L13.82 12l4.59-4.59L17 6l-6 6 6 6zM6 6h2v12H6z\"/>\n          </svg>\n        </button>\n      }\n      <button matIconButton type=\"button\"\n              class=\"mat-mdc-paginator-navigation-previous\"\n              (click)=\"_buttonClicked(pageIndex - 1, _previousButtonsDisabled())\"\n              [attr.aria-label]=\"_intl.previousPageLabel\"\n              [matTooltip]=\"_intl.previousPageLabel\"\n              [matTooltipDisabled]=\"_previousButtonsDisabled()\"\n              matTooltipPosition=\"above\"\n              [disabled]=\"_previousButtonsDisabled()\"\n              [tabindex]=\"_previousButtonsDisabled() ? -1 : null\"\n              disabledInteractive>\n        <svg class=\"mat-mdc-paginator-icon\"\n             viewBox=\"0 0 24 24\"\n             focusable=\"false\"\n             aria-hidden=\"true\">\n          <path d=\"M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z\"/>\n        </svg>\n      </button>\n      <button matIconButton type=\"button\"\n              class=\"mat-mdc-paginator-navigation-next\"\n              (click)=\"_buttonClicked(pageIndex + 1, _nextButtonsDisabled())\"\n              [attr.aria-label]=\"_intl.nextPageLabel\"\n              [matTooltip]=\"_intl.nextPageLabel\"\n              [matTooltipDisabled]=\"_nextButtonsDisabled()\"\n              matTooltipPosition=\"above\"\n              [disabled]=\"_nextButtonsDisabled()\"\n              [tabindex]=\"_nextButtonsDisabled() ? -1 : null\"\n              disabledInteractive>\n        <svg class=\"mat-mdc-paginator-icon\"\n             viewBox=\"0 0 24 24\"\n             focusable=\"false\"\n             aria-hidden=\"true\">\n          <path d=\"M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z\"/>\n        </svg>\n      </button>\n      @if (showFirstLastButtons) {\n        <button matIconButton type=\"button\"\n                class=\"mat-mdc-paginator-navigation-last\"\n                (click)=\"_buttonClicked(getNumberOfPages() - 1, _nextButtonsDisabled())\"\n                [attr.aria-label]=\"_intl.lastPageLabel\"\n                [matTooltip]=\"_intl.lastPageLabel\"\n                [matTooltipDisabled]=\"_nextButtonsDisabled()\"\n                matTooltipPosition=\"above\"\n                [disabled]=\"_nextButtonsDisabled()\"\n                [tabindex]=\"_nextButtonsDisabled() ? -1 : null\"\n                disabledInteractive>\n          <svg class=\"mat-mdc-paginator-icon\"\n              viewBox=\"0 0 24 24\"\n              focusable=\"false\"\n              aria-hidden=\"true\">\n            <path d=\"M5.59 7.41L10.18 12l-4.59 4.59L7 18l6-6-6-6zM16 6h2v12h-2z\"/>\n          </svg>\n        </button>\n      }\n    </div>\n  </div>\n</div>\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {NgModule} from '@angular/core';\nimport {MAT_PAGINATOR_INTL_PROVIDER} from './paginator-intl';\nimport {MatButtonModule} from '../button';\nimport {MatSelectModule} from '../select';\nimport {MatTooltipModule} from '../tooltip';\nimport {MatPaginator} from './paginator';\n\n@NgModule({\n  imports: [MatButtonModule, MatSelectModule, MatTooltipModule, MatPaginator],\n  exports: [MatPaginator],\n  providers: [MAT_PAGINATOR_INTL_PROVIDER],\n})\nexport class MatPaginatorModule {}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWA;;;AAGG;MAEU,gBAAgB,CAAA;AAC3B;;;AAGG;AACM,IAAA,OAAO,GAAkB,IAAI,OAAO,EAAQ;;IAGrD,iBAAiB,GAAW,iBAAiB;;IAG7C,aAAa,GAAW,WAAW;;IAGnC,iBAAiB,GAAW,eAAe;;IAG3C,cAAc,GAAW,YAAY;;IAGrC,aAAa,GAAW,WAAW;;IAGnC,aAAa,GAA+D,CAC1E,IAAY,EACZ,QAAgB,EAChB,MAAc,KACZ;QACF,IAAI,MAAM,IAAI,CAAC,IAAI,QAAQ,IAAI,CAAC,EAAE;YAChC,OAAO,CAAA,KAAA,EAAQ,MAAM,CAAA,CAAE;;QAGzB,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,CAAC;AAE5B,QAAA,MAAM,UAAU,GAAG,IAAI,GAAG,QAAQ;;QAGlC,MAAM,QAAQ,GACZ,UAAU,GAAG,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,GAAG,QAAQ,EAAE,MAAM,CAAC,GAAG,UAAU,GAAG,QAAQ;QAEvF,OAAO,CAAA,EAAG,UAAU,GAAG,CAAC,MAAM,QAAQ,CAAA,IAAA,EAAO,MAAM,CAAA,CAAE;AACvD,KAAC;uGAzCU,gBAAgB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA;AAAhB,IAAA,OAAA,KAAA,GAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,gBAAgB,cADJ,MAAM,EAAA,CAAA;;2FAClB,gBAAgB,EAAA,UAAA,EAAA,CAAA;kBAD5B,UAAU;mBAAC,EAAC,UAAU,EAAE,MAAM,EAAC;;AA6ChC;;;;AAIG;AACG,SAAU,mCAAmC,CAAC,UAA4B,EAAA;AAC9E,IAAA,OAAO,UAAU,IAAI,IAAI,gBAAgB,EAAE;AAC7C;AAEA;;;;AAIG;AACU,MAAA,2BAA2B,GAAG;;AAEzC,IAAA,OAAO,EAAE,gBAAgB;AACzB,IAAA,IAAI,EAAE,CAAC,CAAC,IAAI,QAAQ,EAAE,EAAE,IAAI,QAAQ,EAAE,EAAE,gBAAgB,CAAC,CAAC;AAC1D,IAAA,UAAU,EAAE,mCAAmC;;;AC9CjD;AACA,MAAM,iBAAiB,GAAG,EAAE;AAW5B;;;AAGG;MACU,SAAS,CAAA;;AAEpB,IAAA,SAAS;AAET;;;AAGG;AACH,IAAA,iBAAiB;;AAGjB,IAAA,QAAQ;;AAGR,IAAA,MAAM;AACP;AAwBD;MACa,6BAA6B,GAAG,IAAI,cAAc,CAC7D,+BAA+B;AAGjC;;;;AAIG;MAcU,YAAY,CAAA;AACvB,IAAA,KAAK,GAAG,MAAM,CAAC,gBAAgB,CAAC;AACxB,IAAA,kBAAkB,GAAG,MAAM,CAAC,iBAAiB,CAAC;;AAGtD,IAAA,oBAAoB;;IAGX,gBAAgB,GAAG,MAAM,CAAC,YAAY,CAAC,CAAC,KAAK,CAAC,gCAAgC,CAAC;AAEhF,IAAA,YAAY;IACZ,cAAc,GAAG,KAAK;AACtB,IAAA,kBAAkB,GAAG,IAAI,aAAa,CAAO,CAAC,CAAC;AAEvD;;;;;;AAMG;AACM,IAAA,KAAK;;AAGd,IAAA,IACI,SAAS,GAAA;QACX,OAAO,IAAI,CAAC,UAAU;;IAExB,IAAI,SAAS,CAAC,KAAa,EAAA;AACzB,QAAA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,IAAI,CAAC,EAAE,CAAC,CAAC;AACzC,QAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE;;IAEhC,UAAU,GAAG,CAAC;;AAGtB,IAAA,IACI,MAAM,GAAA;QACR,OAAO,IAAI,CAAC,OAAO;;IAErB,IAAI,MAAM,CAAC,KAAa,EAAA;AACtB,QAAA,IAAI,CAAC,OAAO,GAAG,KAAK,IAAI,CAAC;AACzB,QAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE;;IAEhC,OAAO,GAAG,CAAC;;AAGnB,IAAA,IACI,QAAQ,GAAA;QACV,OAAO,IAAI,CAAC,SAAS;;IAEvB,IAAI,QAAQ,CAAC,KAAa,EAAA;AACxB,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,IAAI,CAAC,EAAE,CAAC,CAAC;QACxC,IAAI,CAAC,+BAA+B,EAAE;;AAEhC,IAAA,SAAS;;AAGjB,IAAA,IACI,eAAe,GAAA;QACjB,OAAO,IAAI,CAAC,gBAAgB;;IAE9B,IAAI,eAAe,CAAC,KAAmC,EAAA;QACrD,IAAI,CAAC,gBAAgB,GAAG,CAAC,KAAK,IAAK,EAAe,EAAE,GAAG,CAAC,CAAC,IAAI,eAAe,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACnF,IAAI,CAAC,+BAA+B,EAAE;;IAEhC,gBAAgB,GAAa,EAAE;;IAIvC,YAAY,GAAY,KAAK;;IAI7B,oBAAoB,GAAY,KAAK;;IAG5B,YAAY,GAA6B,EAAE;;IAIpD,QAAQ,GAAY,KAAK;;AAGN,IAAA,IAAI,GAA4B,IAAI,YAAY,EAAa;;AAGhF,IAAA,yBAAyB;;AAGzB,IAAA,WAAW,GAAqB,IAAI,CAAC,kBAAkB;AAKvD,IAAA,WAAA,GAAA;AACE,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK;AACxB,QAAA,MAAM,QAAQ,GAAG,MAAM,CAA6B,6BAA6B,EAAE;AACjF,YAAA,QAAQ,EAAE,IAAI;AACf,SAAA,CAAC;AAEF,QAAA,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,MAAM,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,CAAC;QAEzF,IAAI,QAAQ,EAAE;YACZ,MAAM,EAAC,QAAQ,EAAE,eAAe,EAAE,YAAY,EAAE,oBAAoB,EAAC,GAAG,QAAQ;AAEhF,YAAA,IAAI,QAAQ,IAAI,IAAI,EAAE;AACpB,gBAAA,IAAI,CAAC,SAAS,GAAG,QAAQ;;AAG3B,YAAA,IAAI,eAAe,IAAI,IAAI,EAAE;AAC3B,gBAAA,IAAI,CAAC,gBAAgB,GAAG,eAAe;;AAGzC,YAAA,IAAI,YAAY,IAAI,IAAI,EAAE;AACxB,gBAAA,IAAI,CAAC,YAAY,GAAG,YAAY;;AAGlC,YAAA,IAAI,oBAAoB,IAAI,IAAI,EAAE;AAChC,gBAAA,IAAI,CAAC,oBAAoB,GAAG,oBAAoB;;;QAIpD,IAAI,CAAC,oBAAoB,GAAG,QAAQ,EAAE,mBAAmB,IAAI,SAAS;;IAGxE,QAAQ,GAAA;AACN,QAAA,IAAI,CAAC,cAAc,GAAG,IAAI;QAC1B,IAAI,CAAC,+BAA+B,EAAE;AACtC,QAAA,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE;;IAGhC,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,kBAAkB,CAAC,QAAQ,EAAE;AAClC,QAAA,IAAI,CAAC,YAAY,CAAC,WAAW,EAAE;;;IAIjC,QAAQ,GAAA;AACN,QAAA,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE;YACtB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC;;;;IAKtC,YAAY,GAAA;AACV,QAAA,IAAI,IAAI,CAAC,eAAe,EAAE,EAAE;YAC1B,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC;;;;IAKtC,SAAS,GAAA;;AAEP,QAAA,IAAI,IAAI,CAAC,eAAe,EAAE,EAAE;AAC1B,YAAA,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;;;;IAKrB,QAAQ,GAAA;;AAEN,QAAA,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE;YACtB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,gBAAgB,EAAE,GAAG,CAAC,CAAC;;;;IAK/C,eAAe,GAAA;QACb,OAAO,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,IAAI,CAAC,QAAQ,IAAI,CAAC;;;IAIlD,WAAW,GAAA;QACT,MAAM,YAAY,GAAG,IAAI,CAAC,gBAAgB,EAAE,GAAG,CAAC;QAChD,OAAO,IAAI,CAAC,SAAS,GAAG,YAAY,IAAI,IAAI,CAAC,QAAQ,IAAI,CAAC;;;IAI5D,gBAAgB,GAAA;AACd,QAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;AAClB,YAAA,OAAO,CAAC;;AAGV,QAAA,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC;;AAG/C;;;;;;;AAOG;AACH,IAAA,eAAe,CAAC,QAAgB,EAAA;;;QAG9B,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,QAAQ;AACjD,QAAA,MAAM,iBAAiB,GAAG,IAAI,CAAC,SAAS;AAExC,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,QAAQ,CAAC,IAAI,CAAC;AACvD,QAAA,IAAI,CAAC,QAAQ,GAAG,QAAQ;AACxB,QAAA,IAAI,CAAC,cAAc,CAAC,iBAAiB,CAAC;;;IAIxC,oBAAoB,GAAA;QAClB,OAAO,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;;;IAI7C,wBAAwB,GAAA;QACtB,OAAO,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;;AAGjD;;;AAGG;IACK,+BAA+B,GAAA;AACrC,QAAA,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACxB;;;AAIF,QAAA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;AAClB,YAAA,IAAI,CAAC,SAAS;AACZ,gBAAA,IAAI,CAAC,eAAe,CAAC,MAAM,IAAI,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,GAAG,iBAAiB;;QAGlF,IAAI,CAAC,yBAAyB,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE;AAE7D,QAAA,IAAI,IAAI,CAAC,yBAAyB,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE;YAChE,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC;;;AAIpD,QAAA,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AACpD,QAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE;;;AAIhC,IAAA,cAAc,CAAC,iBAAyB,EAAA;AAC9C,QAAA,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;YACb,iBAAiB;YACjB,SAAS,EAAE,IAAI,CAAC,SAAS;YACzB,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,MAAM,EAAE,IAAI,CAAC,MAAM;AACpB,SAAA,CAAC;;;AAII,IAAA,SAAS,CAAC,KAAa,EAAA;AAC7B,QAAA,MAAM,aAAa,GAAG,IAAI,CAAC,SAAS;AAEpC,QAAA,IAAI,KAAK,KAAK,aAAa,EAAE;AAC3B,YAAA,IAAI,CAAC,SAAS,GAAG,KAAK;AACtB,YAAA,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC;;;AAItC;;;;AAIG;IACO,cAAc,CAAC,WAAmB,EAAE,UAAmB,EAAA;;;;QAI/D,IAAI,CAAC,UAAU,EAAE;AACf,YAAA,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC;;;uGA/QpB,YAAY,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAZ,YAAY,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,eAAA,EAAA,MAAA,EAAA,EAAA,KAAA,EAAA,OAAA,EAAA,SAAA,EAAA,CAAA,WAAA,EAAA,WAAA,EAwBJ,eAAe,CAWf,EAAA,MAAA,EAAA,CAAA,QAAA,EAAA,QAAA,EAAA,eAAe,sCAWf,eAAe,CAAA,EAAA,eAAA,EAAA,iBAAA,EAAA,YAAA,EAAA,CAAA,cAAA,EAAA,cAAA,EAsBf,gBAAgB,CAAA,EAAA,oBAAA,EAAA,CAAA,sBAAA,EAAA,sBAAA,EAIhB,gBAAgB,CAAA,EAAA,YAAA,EAAA,cAAA,EAAA,QAAA,EAAA,CAAA,UAAA,EAAA,UAAA,EAOhB,gBAAgB,CC7LrC,EAAA,EAAA,OAAA,EAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,MAAA,EAAA,OAAA,EAAA,EAAA,cAAA,EAAA,mBAAA,EAAA,EAAA,QAAA,EAAA,CAAA,cAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,EAAA,y6KA4HA,EDhBY,MAAA,EAAA,CAAA,4lFAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,YAAY,EAAE,QAAA,EAAA,gBAAA,EAAA,MAAA,EAAA,CAAA,oBAAA,EAAA,OAAA,EAAA,YAAA,EAAA,YAAA,EAAA,iBAAA,EAAA,WAAA,CAAA,EAAA,QAAA,EAAA,CAAA,cAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAA,SAAS,ogBAAE,SAAS,EAAA,QAAA,EAAA,YAAA,EAAA,MAAA,EAAA,CAAA,OAAA,EAAA,IAAA,EAAA,UAAA,CAAA,EAAA,OAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,QAAA,EAAA,CAAA,WAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAE,aAAa,EAAA,QAAA,EAAA,sFAAA,EAAA,QAAA,EAAA,CAAA,WAAA,EAAA,WAAA,CAAA,EAAA,EAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,EAAE,UAAU,EAAA,QAAA,EAAA,cAAA,EAAA,MAAA,EAAA,CAAA,oBAAA,EAAA,4BAAA,EAAA,oBAAA,EAAA,qBAAA,EAAA,qBAAA,EAAA,yBAAA,EAAA,YAAA,EAAA,iBAAA,CAAA,EAAA,QAAA,EAAA,CAAA,YAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,MAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FAE5D,YAAY,EAAA,UAAA,EAAA,CAAA;kBAbxB,SAAS;+BACE,eAAe,EAAA,QAAA,EACf,cAAc,EAGlB,IAAA,EAAA;AACJ,wBAAA,OAAO,EAAE,mBAAmB;AAC5B,wBAAA,MAAM,EAAE,OAAO;AAChB,qBAAA,EAAA,eAAA,EACgB,uBAAuB,CAAC,MAAM,iBAChC,iBAAiB,CAAC,IAAI,EAC5B,OAAA,EAAA,CAAC,YAAY,EAAE,SAAS,EAAE,SAAS,EAAE,aAAa,EAAE,UAAU,CAAC,EAAA,QAAA,EAAA,y6KAAA,EAAA,MAAA,EAAA,CAAA,4lFAAA,CAAA,EAAA;wDAuB/D,KAAK,EAAA,CAAA;sBAAb;gBAIG,SAAS,EAAA,CAAA;sBADZ,KAAK;uBAAC,EAAC,SAAS,EAAE,eAAe,EAAC;gBAY/B,MAAM,EAAA,CAAA;sBADT,KAAK;uBAAC,EAAC,SAAS,EAAE,eAAe,EAAC;gBAY/B,QAAQ,EAAA,CAAA;sBADX,KAAK;uBAAC,EAAC,SAAS,EAAE,eAAe,EAAC;gBAY/B,eAAe,EAAA,CAAA;sBADlB;gBAYD,YAAY,EAAA,CAAA;sBADX,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC;gBAKpC,oBAAoB,EAAA,CAAA;sBADnB,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC;gBAI3B,YAAY,EAAA,CAAA;sBAApB;gBAID,QAAQ,EAAA,CAAA;sBADP,KAAK;uBAAC,EAAC,SAAS,EAAE,gBAAgB,EAAC;gBAIjB,IAAI,EAAA,CAAA;sBAAtB;;;ME7KU,kBAAkB,CAAA;uGAAlB,kBAAkB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA;wGAAlB,kBAAkB,EAAA,OAAA,EAAA,CAJnB,eAAe,EAAE,eAAe,EAAE,gBAAgB,EAAE,YAAY,CAAA,EAAA,OAAA,EAAA,CAChE,YAAY,CAAA,EAAA,CAAA;wGAGX,kBAAkB,EAAA,SAAA,EAFlB,CAAC,2BAA2B,CAAC,EAAA,OAAA,EAAA,CAF9B,eAAe,EAAE,eAAe,EAAE,gBAAgB,EAAE,YAAY,CAAA,EAAA,CAAA;;2FAI/D,kBAAkB,EAAA,UAAA,EAAA,CAAA;kBAL9B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACR,OAAO,EAAE,CAAC,eAAe,EAAE,eAAe,EAAE,gBAAgB,EAAE,YAAY,CAAC;oBAC3E,OAAO,EAAE,CAAC,YAAY,CAAC;oBACvB,SAAS,EAAE,CAAC,2BAA2B,CAAC;AACzC,iBAAA;;;;;"}