@use 'sass:color';
@use 'sass:map';
@use './m2-datepicker';
@use './m3-datepicker';
@use '../core/theming/theming';
@use '../core/theming/inspection';
@use '../core/tokens/token-utils';
@use '../core/style/sass-utils';
@use '../core/typography/typography';
@use '../button/icon-button-theme';

// TODO(crisbeto): these variables aren't used anymore and should be removed.
$selected-today-box-shadow-width: 1px;
$selected-fade-amount: 0.6;
$range-fade-amount: 0.2;
$today-fade-amount: 0.2;
$calendar-body-font-size: 13px !default;
$calendar-weekday-table-font-size: 11px !default;

@mixin _calendar-color($theme, $palette-name) {
  $palette-color: inspection.get-theme-color($theme, $palette-name);
  $range-color: m2-datepicker.private-get-range-background-color($palette-color);
  $range-tokens: m2-datepicker.get-range-color-tokens($range-color);
  $calendar-tokens: m2-datepicker.private-get-calendar-color-palette-color-tokens(
    $theme,
    $palette-name
  );

  @include token-utils.create-token-values-mixed(map.merge($calendar-tokens, $range-tokens));
}

/// Outputs base theme styles (styles not dependent on the color, typography, or density settings)
/// for the mat-datepicker.
/// @param {Map} $theme The theme to generate base styles for.
@mixin base($theme) {
  @if inspection.get-theme-version($theme) == 1 {
    @include token-utils.create-token-values(map.get(m3-datepicker.get-tokens($theme), base));
  } @else {
    @include sass-utils.current-selector-or-root() {
      @include token-utils.create-token-values-mixed(m2-datepicker.get-unthemable-tokens());
    }
  }
}

/// Outputs color theme styles for the mat-datepicker.
/// @param {Map} $theme The theme to generate color styles for.
/// @param {String} $color-variant The color variant to use for the component (M3 only)
@mixin color($theme, $color-variant: null) {
  @if inspection.get-theme-version($theme) == 1 {
    @include token-utils.create-token-values(
        map.get(m3-datepicker.get-tokens($theme, $color-variant), color));
  } @else {
    @include sass-utils.current-selector-or-root() {
      @include token-utils.create-token-values-mixed(m2-datepicker.get-color-tokens($theme));
    }

    .mat-datepicker-content {
      &.mat-accent {
        @include _calendar-color($theme, accent);
      }

      &.mat-warn {
        @include _calendar-color($theme, warn);
      }
    }

    .mat-datepicker-toggle-active {
      &.mat-accent {
        $accent-tokens: m2-datepicker.private-get-toggle-color-palette-color-tokens($theme, accent);
        @include token-utils.create-token-values-mixed($accent-tokens);
      }

      &.mat-warn {
        $warn-tokens: m2-datepicker.private-get-toggle-color-palette-color-tokens($theme, warn);
        @include token-utils.create-token-values-mixed($warn-tokens);
      }
    }
  }
}

/// Outputs typography theme styles for the mat-datepicker.
/// @param {Map} $theme The theme to generate typography styles for.
@mixin typography($theme) {
  @if inspection.get-theme-version($theme) == 1 {
    @include token-utils.create-token-values(map.get(m3-datepicker.get-tokens($theme), typography));
  } @else {
    @include sass-utils.current-selector-or-root() {
      @include token-utils.create-token-values-mixed(m2-datepicker.get-typography-tokens($theme));
    }
  }
}

@mixin date-range-colors(
  $range-color,
  $comparison-color: m2-datepicker.$private-default-comparison-color,
  $overlap-color: m2-datepicker.$private-default-overlap-color,
  $overlap-selected-color:
    m2-datepicker.private-get-default-overlap-selected-color($overlap-color)
) {
  $tokens: m2-datepicker.get-range-color-tokens(
    $range-color: $range-color,
    $comparison-color: $comparison-color,
    $overlap-color: $overlap-color,
    $overlap-selected-color: $overlap-selected-color,
  );

  @include sass-utils.current-selector-or-root() {
    @include token-utils.create-token-values-mixed($tokens);
  }
}

/// Outputs density theme styles for the mat-datepicker.
/// @param {Map} $theme The theme to generate density styles for.
@mixin density($theme) {
  @if inspection.get-theme-version($theme) == 1 {
    @include token-utils.create-token-values(map.get(m3-datepicker.get-tokens($theme), density));
  } @else {
    // TODO(crisbeto): move this into the structural styles
    // once the icon button density is switched to tokens.

    // Regardless of the user-passed density, we want the calendar
    // previous/next buttons to remain at density -2
    .mat-calendar-controls {
      @include icon-button-theme.density(-2);
    }
  }
}

/// Defines the tokens that will be available in the `overrides` mixin and for docs extraction.
@function _define-overrides() {
  @return (
    (
      namespace: datepicker,
      tokens: token-utils.get-overrides(m3-datepicker.get-tokens(), datepicker)
    ),
  );
}

/// Outputs the CSS variable values for the given tokens.
/// @param {Map} $tokens The token values to emit.
@mixin overrides($tokens: ()) {
    @include token-utils.batch-create-token-values($tokens, _define-overrides());
}

/// Outputs all (base, color, typography, and density) theme styles for the mat-datepicker.
/// @param {Map} $theme The theme to generate styles for.
/// @param {String} $color-variant The color variant to use for the component (M3 only)
@mixin theme($theme, $color-variant: null) {
  @include theming.private-check-duplicate-theme-styles($theme, 'mat-datepicker') {
    @if inspection.get-theme-version($theme) == 1 {
      @include base($theme);
      @include color($theme, $color-variant);
      @include density($theme);
      @include typography($theme);
    } @else {
      @include base($theme);
      @if inspection.theme-has($theme, color) {
        @include color($theme);
      }
      @if inspection.theme-has($theme, density) {
        @include density($theme);
      }
      @if inspection.theme-has($theme, typography) {
        @include typography($theme);
      }
    }
  }
}
