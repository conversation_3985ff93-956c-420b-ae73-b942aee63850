{"version": 3, "file": "input-names.js", "sourceRoot": "", "sources": ["input-names.ts"], "names": [], "mappings": ";AAAA;;;;;;GAMG;;;AAGH,qDAAgG;AAEhG,2DAAsD;AAGtD,mDAA8D;AAC9D,kDAAmE;AAEnE;;;;;;GAMG;AACH,MAAa,mBAAoB,SAAQ,qBAAsB;IAA/D;;QACE,iEAAiE;QACjE,SAAI,GAA2B,IAAA,oCAAqB,EAAC,IAAI,EAAE,YAAY,CAAC,CAAC;QAEzE,2DAA2D;QAC3D,YAAO,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,CAAC;IAqDnC,CAAC;IAnDU,eAAe,CAAC,UAA4B;QACnD,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;YACvB,MAAM,eAAe,GAAG,IAAI,IAAI,CAAC,OAAO,GAAG,CAAC;YAC5C,MAAM,eAAe,GAAG,IAAI,IAAI,CAAC,WAAW,GAAG,CAAC;YAEhD,IAAA,iCAAuB,EAAC,UAAU,CAAC,OAAO,EAAE,eAAe,CAAC;iBACzD,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,UAAU,CAAC,KAAK,GAAG,MAAM,CAAC;iBACxC,OAAO,CAAC,KAAK,CAAC,EAAE,CACf,IAAI,CAAC,iBAAiB,CACpB,UAAU,CAAC,QAAQ,EACnB,KAAK,EACL,eAAe,CAAC,MAAM,EACtB,eAAe,CAChB,CACF,CAAC;QACN,CAAC,CAAC,CAAC;IACL,CAAC;IAEQ,aAAa,CAAC,QAA0B;QAC/C,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;YACvB,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;YACjC,MAAM,eAAe,GAAa,EAAE,CAAC;YAErC,IAAI,SAAS,CAAC,UAAU,EAAE,CAAC;gBACzB,eAAe,CAAC,IAAI,CAClB,GAAG,IAAA,qCAA2B,EAAC,QAAQ,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE,SAAS,CAAC,UAAU,CAAC,CACrF,CAAC;YACJ,CAAC;YAED,IAAI,SAAS,CAAC,QAAQ,EAAE,CAAC;gBACvB,eAAe,CAAC,IAAI,CAClB,GAAG,IAAA,oCAA0B,EAAC,QAAQ,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE,SAAS,CAAC,QAAQ,CAAC,CAClF,CAAC;YACJ,CAAC;YAED,eAAe;iBACZ,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,QAAQ,CAAC,KAAK,GAAG,MAAM,CAAC;iBACtC,OAAO,CAAC,KAAK,CAAC,EAAE,CACf,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,QAAQ,EAAE,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC,WAAW,CAAC,CACxF,CAAC;QACN,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,iBAAiB,CACvB,QAAuB,EACvB,KAAa,EACb,KAAa,EACb,OAAe;QAEf,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,WAAW,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;IAClF,CAAC;CACF;AA1DD,kDA0DC"}