<div class="h-full bg-white border-r border-gray-200">
  <!-- Navigation Menu -->
  <nav class="mt-4">
    <mat-nav-list>
      <!-- Dashboard -->
      <a mat-list-item routerLink="/dashboard" routerLinkActive="bg-primary-50 text-primary-600">
        <mat-icon matListItemIcon>dashboard</mat-icon>
        <span matListItemTitle>Dashboard</span>
      </a>

      <!-- Patients -->
      <a mat-list-item routerLink="/patients" routerLinkActive="bg-primary-50 text-primary-600">
        <mat-icon matListItemIcon>people</mat-icon>
        <span matListItemTitle>Patients</span>
      </a>

      <!-- Doctors -->
      <a mat-list-item routerLink="/doctors" routerLinkActive="bg-primary-50 text-primary-600">
        <mat-icon matListItemIcon>medical_services</mat-icon>
        <span matListItemTitle>Doctors</span>
      </a>

      <!-- Appointments -->
      <a mat-list-item routerLink="/appointments" routerLinkActive="bg-primary-50 text-primary-600">
        <mat-icon matListItemIcon>event</mat-icon>
        <span matListItemTitle>Appointments</span>
      </a>

      <!-- Billing -->
      <a mat-list-item routerLink="/billing" routerLinkActive="bg-primary-50 text-primary-600">
        <mat-icon matListItemIcon>receipt</mat-icon>
        <span matListItemTitle>Billing</span>
      </a>

      <mat-divider class="my-4"></mat-divider>

      <!-- Reports -->
      <a mat-list-item routerLink="/reports" routerLinkActive="bg-primary-50 text-primary-600">
        <mat-icon matListItemIcon>assessment</mat-icon>
        <span matListItemTitle>Reports</span>
      </a>

      <!-- Settings -->
      <a mat-list-item routerLink="/settings" routerLinkActive="bg-primary-50 text-primary-600">
        <mat-icon matListItemIcon>settings</mat-icon>
        <span matListItemTitle>Settings</span>
      </a>
    </mat-nav-list>
  </nav>
</div>
