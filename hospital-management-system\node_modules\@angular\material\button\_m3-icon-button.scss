@use 'sass:map';
@use 'sass:list';
@use '../core/tokens/m3-utils';
@use '../core/theming/theming';
@use '../core/theming/inspection';
@use '../core/tokens/m3';

/// Generates custom tokens for the mat-icon-button.
/// @param {Map} $systems The MDC system tokens
/// @param {Boolean} $exclude-hardcoded Whether to exclude hardcoded token values
/// @param {Map} $token-slots Possible token slots
/// @return {Map} A set of custom tokens for the mat-icon-button
@function get-tokens($theme: m3.$sys-theme, $color-variant: null) {
  $system: m3-utils.get-system($theme);
  @if $color-variant {
    $system: m3-utils.replace-colors-with-variant($system, primary, $color-variant);
    $system: map.set($system, on-surface-variant, map.get($system, $color-variant));
  }

  $tokens: (
    base: (
      icon-button-icon-size: 24px,
      icon-button-container-shape: map.get($system, corner-full),
    ),
    color: (
      icon-button-disabled-icon-color:
          m3-utils.color-with-opacity(map.get($system, on-surface), 38%),
      icon-button-disabled-state-layer-color: map.get($system, on-surface-variant),
      icon-button-focus-state-layer-opacity: map.get($system, focus-state-layer-opacity),
      icon-button-hover-state-layer-opacity: map.get($system, hover-state-layer-opacity),
      icon-button-icon-color: map.get($system, on-surface-variant),
      icon-button-pressed-state-layer-opacity: map.get($system, pressed-state-layer-opacity),
      icon-button-ripple-color: m3-utils.color-with-opacity(
          map.get($system, on-surface-variant), map.get($system, pressed-state-layer-opacity)),
      icon-button-state-layer-color: map.get($system, on-surface-variant),
    ),
    typography: (),
    density: get-density-tokens(map.get($theme, inspection.$internals, density-scale)),
  );

  @return $tokens;
}

// Tokens that can be configured through Angular Material's density theming API.
@function get-density-tokens($scale) {
  $scale: theming.clamp-density($scale, -5);
  $index: ($scale * -1) + 1;

  @return (
    icon-button-touch-target-display: list.nth((block, block, none, none, none, none), $index),
    // The size caps out at 24px, because anything lower will be smaller than the icon.
    icon-button-state-layer-size: list.nth((40px, 36px, 32px, 28px, 24px, 24px), $index),
  );
}
