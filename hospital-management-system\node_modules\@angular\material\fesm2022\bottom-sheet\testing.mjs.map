{"version": 3, "file": "testing.mjs", "sources": ["../../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/material/bottom-sheet/testing/bottom-sheet-harness.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {ContentContainerComponentHarness, HarnessPredicate, TestKey} from '@angular/cdk/testing';\nimport {BottomSheetHarnessFilters} from './bottom-sheet-harness-filters';\n\n/** <PERSON><PERSON><PERSON> for interacting with a standard MatBottomSheet in tests. */\nexport class MatBottomSheetHarness extends ContentContainerComponentHarness<string> {\n  // Developers can provide a custom component or template for the\n  // bottom sheet. The canonical parent is the \".mat-bottom-sheet-container\".\n  static hostSelector = '.mat-bottom-sheet-container:not([mat-exit])';\n\n  /**\n   * Gets a `HarnessPredicate` that can be used to search for a bottom sheet with\n   * specific attributes.\n   * @param options Options for narrowing the search.\n   * @return a `HarnessPredicate` configured with the given options.\n   */\n  static with(options: BottomSheetHarnessFilters = {}): HarnessPredicate<MatBottomSheetHarness> {\n    return new HarnessPredicate(MatBottomSheetHarness, options);\n  }\n\n  /** Gets the value of the bottom sheet's \"aria-label\" attribute. */\n  async getAriaLabel(): Promise<string | null> {\n    return (await this.host()).getAttribute('aria-label');\n  }\n\n  /**\n   * Dismisses the bottom sheet by pressing escape. Note that this method cannot\n   * be used if \"disableClose\" has been set to true via the config.\n   */\n  async dismiss(): Promise<void> {\n    await (await this.host()).sendKeys(TestKey.ESCAPE);\n  }\n}\n"], "names": [], "mappings": ";;AAWA;AACM,MAAO,qBAAsB,SAAQ,gCAAwC,CAAA;;;AAGjF,IAAA,OAAO,YAAY,GAAG,6CAA6C;AAEnE;;;;;AAKG;AACH,IAAA,OAAO,IAAI,CAAC,OAAA,GAAqC,EAAE,EAAA;AACjD,QAAA,OAAO,IAAI,gBAAgB,CAAC,qBAAqB,EAAE,OAAO,CAAC;;;AAI7D,IAAA,MAAM,YAAY,GAAA;AAChB,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,YAAY,CAAC,YAAY,CAAC;;AAGvD;;;AAGG;AACH,IAAA,MAAM,OAAO,GAAA;AACX,QAAA,MAAM,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC;;;;;;"}