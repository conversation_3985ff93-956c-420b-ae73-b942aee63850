{"name": "hospital-management-system", "version": "0.0.0", "scripts": {"ng": "ng", "dev": "ng serve --port 4200", "start": "ng serve --port 4200", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test", "serve": "nodemon server.js"}, "private": true, "dependencies": {"@angular/cdk": "^20.0.3", "@angular/common": "^20.0.0", "@angular/compiler": "^20.0.0", "@angular/core": "^20.0.0", "@angular/forms": "^20.0.0", "@angular/material": "^20.0.3", "@angular/platform-browser": "^20.0.0", "@angular/router": "^20.0.0", "bcrypt": "^6.0.0", "body-parser": "^2.2.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.16.0", "rxjs": "~7.8.0", "tslib": "^2.3.0"}, "devDependencies": {"@angular/build": "^20.0.2", "@angular/cli": "^20.0.2", "@angular/compiler-cli": "^20.0.0", "@types/jasmine": "~5.1.0", "autoprefixer": "^10.4.21", "jasmine-core": "~5.7.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "nodemon": "^3.1.10", "postcss": "^8.5.6", "tailwindcss": "^4.1.10", "typescript": "~5.8.2"}}