import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { AuthGuard } from './core/guards/auth.guard';
import { RoleGuard } from './core/guards/role.guard';
import { UnauthorizedComponent } from './shared/components/unauthorized.component';

const routes: Routes = [
  { path: '', redirectTo: '/auth/login', pathMatch: 'full' },
  {
    path: 'auth',
    loadChildren: () => import('./auth/auth-module').then(m => m.AuthModule)
  },
  {
    path: 'dashboard',
    loadChildren: () => import('./dashboard/dashboard-module').then(m => m.DashboardModule),
    canActivate: [AuthGuard]
  },
  {
    path: 'patients',
    loadChildren: () => import('./patients/patients-module').then(m => m.PatientsModule),
    canActivate: [AuthGuard, RoleGuard],
    data: { permissions: ['view_patients', 'manage_patients'] }
  },
  {
    path: 'doctors',
    loadChildren: () => import('./doctors/doctors-module').then(m => m.DoctorsModule),
    canActivate: [AuthGuard]
  },
  {
    path: 'appointments',
    loadChildren: () => import('./appointments/appointments-module').then(m => m.AppointmentsModule),
    canActivate: [AuthGuard]
  },
  {
    path: 'billing',
    loadChildren: () => import('./billing/billing-module').then(m => m.BillingModule),
    canActivate: [AuthGuard, RoleGuard],
    data: { permissions: ['manage_appointments', 'manage_system'] }
  },
  {
    path: 'unauthorized',
    component: UnauthorizedComponent
  },
  { path: '**', redirectTo: '/dashboard' }
];

@NgModule({
  imports: [RouterModule.forRoot(routes)],
  exports: [RouterModule]
})
export class AppRoutingModule { }
