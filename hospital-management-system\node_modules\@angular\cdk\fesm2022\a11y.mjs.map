{"version": 3, "file": "a11y.mjs", "sources": ["../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/cdk/a11y/aria-describer/aria-reference.ts", "../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/cdk/a11y/aria-describer/aria-describer.ts", "../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/cdk/a11y/key-manager/noop-tree-key-manager.ts", "../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/cdk/a11y/focus-trap/configurable-focus-trap.ts", "../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/cdk/a11y/focus-trap/event-listener-inert-strategy.ts", "../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/cdk/a11y/focus-trap/focus-trap-inert-strategy.ts", "../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/cdk/a11y/focus-trap/focus-trap-manager.ts", "../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/cdk/a11y/focus-trap/configurable-focus-trap-factory.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n/** IDs are delimited by an empty space, as per the spec. */\nconst ID_DELIMITER = ' ';\n\n/**\n * Adds the given ID to the specified ARIA attribute on an element.\n * Used for attributes such as aria-labelledby, aria-owns, etc.\n */\nexport function addAriaReferencedId(el: Element, attr: `aria-${string}`, id: string) {\n  const ids = getAriaReferenceIds(el, attr);\n  id = id.trim();\n  if (ids.some(existingId => existingId.trim() === id)) {\n    return;\n  }\n  ids.push(id);\n\n  el.setAttribute(attr, ids.join(ID_DELIMITER));\n}\n\n/**\n * Removes the given ID from the specified ARIA attribute on an element.\n * Used for attributes such as aria-labelledby, aria-owns, etc.\n */\nexport function removeAriaReferencedId(el: Element, attr: `aria-${string}`, id: string) {\n  const ids = getAriaReferenceIds(el, attr);\n  id = id.trim();\n  const filteredIds = ids.filter(val => val !== id);\n\n  if (filteredIds.length) {\n    el.setAttribute(attr, filteredIds.join(ID_DELIMITER));\n  } else {\n    el.removeAttribute(attr);\n  }\n}\n\n/**\n * Gets the list of IDs referenced by the given ARIA attribute on an element.\n * Used for attributes such as aria-labelledby, aria-owns, etc.\n */\nexport function getAriaReferenceIds(el: Element, attr: string): string[] {\n  // Get string array of all individual ids (whitespace delimited) in the attribute value\n  const attrValue = el.getAttribute(attr);\n  return attrValue?.match(/\\S+/g) ?? [];\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Injectable, OnDestroy, APP_ID, inject, DOCUMENT} from '@angular/core';\nimport {Platform} from '../../platform';\nimport {addAriaReferencedId, getAriaReferenceIds, removeAriaReferencedId} from './aria-reference';\nimport {_CdkPrivateStyleLoader, _VisuallyHiddenLoader} from '../../private';\n\n/**\n * Interface used to register message elements and keep a count of how many registrations have\n * the same message and the reference to the message element used for the `aria-describedby`.\n */\nexport interface RegisteredMessage {\n  /** The element containing the message. */\n  messageElement: Element;\n\n  /** The number of elements that reference this message element via `aria-describedby`. */\n  referenceCount: number;\n}\n\n/**\n * ID used for the body container where all messages are appended.\n * @deprecated No longer being used. To be removed.\n * @breaking-change 14.0.0\n */\nexport const MESSAGES_CONTAINER_ID = 'cdk-describedby-message-container';\n\n/**\n * ID prefix used for each created message element.\n * @deprecated To be turned into a private variable.\n * @breaking-change 14.0.0\n */\nexport const CDK_DESCRIBEDBY_ID_PREFIX = 'cdk-describedby-message';\n\n/**\n * Attribute given to each host element that is described by a message element.\n * @deprecated To be turned into a private variable.\n * @breaking-change 14.0.0\n */\nexport const CDK_DESCRIBEDBY_HOST_ATTRIBUTE = 'cdk-describedby-host';\n\n/** Global incremental identifier for each registered message element. */\nlet nextId = 0;\n\n/**\n * Utility that creates visually hidden elements with a message content. Useful for elements that\n * want to use aria-describedby to further describe themselves without adding additional visual\n * content.\n */\n@Injectable({providedIn: 'root'})\nexport class AriaDescriber implements OnDestroy {\n  private _platform = inject(Platform);\n  private _document = inject(DOCUMENT);\n\n  /** Map of all registered message elements that have been placed into the document. */\n  private _messageRegistry = new Map<string | Element, RegisteredMessage>();\n\n  /** Container for all registered messages. */\n  private _messagesContainer: HTMLElement | null = null;\n\n  /** Unique ID for the service. */\n  private readonly _id = `${nextId++}`;\n\n  constructor(...args: unknown[]);\n\n  constructor() {\n    inject(_CdkPrivateStyleLoader).load(_VisuallyHiddenLoader);\n    this._id = inject(APP_ID) + '-' + nextId++;\n  }\n\n  /**\n   * Adds to the host element an aria-describedby reference to a hidden element that contains\n   * the message. If the same message has already been registered, then it will reuse the created\n   * message element.\n   */\n  describe(hostElement: Element, message: string, role?: string): void;\n\n  /**\n   * Adds to the host element an aria-describedby reference to an already-existing message element.\n   */\n  describe(hostElement: Element, message: HTMLElement): void;\n\n  describe(hostElement: Element, message: string | HTMLElement, role?: string): void {\n    if (!this._canBeDescribed(hostElement, message)) {\n      return;\n    }\n\n    const key = getKey(message, role);\n\n    if (typeof message !== 'string') {\n      // We need to ensure that the element has an ID.\n      setMessageId(message, this._id);\n      this._messageRegistry.set(key, {messageElement: message, referenceCount: 0});\n    } else if (!this._messageRegistry.has(key)) {\n      this._createMessageElement(message, role);\n    }\n\n    if (!this._isElementDescribedByMessage(hostElement, key)) {\n      this._addMessageReference(hostElement, key);\n    }\n  }\n\n  /** Removes the host element's aria-describedby reference to the message. */\n  removeDescription(hostElement: Element, message: string, role?: string): void;\n\n  /** Removes the host element's aria-describedby reference to the message element. */\n  removeDescription(hostElement: Element, message: HTMLElement): void;\n\n  removeDescription(hostElement: Element, message: string | HTMLElement, role?: string): void {\n    if (!message || !this._isElementNode(hostElement)) {\n      return;\n    }\n\n    const key = getKey(message, role);\n\n    if (this._isElementDescribedByMessage(hostElement, key)) {\n      this._removeMessageReference(hostElement, key);\n    }\n\n    // If the message is a string, it means that it's one that we created for the\n    // consumer so we can remove it safely, otherwise we should leave it in place.\n    if (typeof message === 'string') {\n      const registeredMessage = this._messageRegistry.get(key);\n      if (registeredMessage && registeredMessage.referenceCount === 0) {\n        this._deleteMessageElement(key);\n      }\n    }\n\n    if (this._messagesContainer?.childNodes.length === 0) {\n      this._messagesContainer.remove();\n      this._messagesContainer = null;\n    }\n  }\n\n  /** Unregisters all created message elements and removes the message container. */\n  ngOnDestroy() {\n    const describedElements = this._document.querySelectorAll(\n      `[${CDK_DESCRIBEDBY_HOST_ATTRIBUTE}=\"${this._id}\"]`,\n    );\n\n    for (let i = 0; i < describedElements.length; i++) {\n      this._removeCdkDescribedByReferenceIds(describedElements[i]);\n      describedElements[i].removeAttribute(CDK_DESCRIBEDBY_HOST_ATTRIBUTE);\n    }\n\n    this._messagesContainer?.remove();\n    this._messagesContainer = null;\n    this._messageRegistry.clear();\n  }\n\n  /**\n   * Creates a new element in the visually hidden message container element with the message\n   * as its content and adds it to the message registry.\n   */\n  private _createMessageElement(message: string, role?: string) {\n    const messageElement = this._document.createElement('div');\n    setMessageId(messageElement, this._id);\n    messageElement.textContent = message;\n\n    if (role) {\n      messageElement.setAttribute('role', role);\n    }\n\n    this._createMessagesContainer();\n    this._messagesContainer!.appendChild(messageElement);\n    this._messageRegistry.set(getKey(message, role), {messageElement, referenceCount: 0});\n  }\n\n  /** Deletes the message element from the global messages container. */\n  private _deleteMessageElement(key: string | Element) {\n    this._messageRegistry.get(key)?.messageElement?.remove();\n    this._messageRegistry.delete(key);\n  }\n\n  /** Creates the global container for all aria-describedby messages. */\n  private _createMessagesContainer() {\n    if (this._messagesContainer) {\n      return;\n    }\n\n    const containerClassName = 'cdk-describedby-message-container';\n    const serverContainers = this._document.querySelectorAll(\n      `.${containerClassName}[platform=\"server\"]`,\n    );\n\n    for (let i = 0; i < serverContainers.length; i++) {\n      // When going from the server to the client, we may end up in a situation where there's\n      // already a container on the page, but we don't have a reference to it. Clear the\n      // old container so we don't get duplicates. Doing this, instead of emptying the previous\n      // container, should be slightly faster.\n      serverContainers[i].remove();\n    }\n\n    const messagesContainer = this._document.createElement('div');\n\n    // We add `visibility: hidden` in order to prevent text in this container from\n    // being searchable by the browser's Ctrl + F functionality.\n    // Screen-readers will still read the description for elements with aria-describedby even\n    // when the description element is not visible.\n    messagesContainer.style.visibility = 'hidden';\n    // Even though we use `visibility: hidden`, we still apply `cdk-visually-hidden` so that\n    // the description element doesn't impact page layout.\n    messagesContainer.classList.add(containerClassName);\n    messagesContainer.classList.add('cdk-visually-hidden');\n\n    if (!this._platform.isBrowser) {\n      messagesContainer.setAttribute('platform', 'server');\n    }\n\n    this._document.body.appendChild(messagesContainer);\n    this._messagesContainer = messagesContainer;\n  }\n\n  /** Removes all cdk-describedby messages that are hosted through the element. */\n  private _removeCdkDescribedByReferenceIds(element: Element) {\n    // Remove all aria-describedby reference IDs that are prefixed by CDK_DESCRIBEDBY_ID_PREFIX\n    const originalReferenceIds = getAriaReferenceIds(element, 'aria-describedby').filter(\n      id => id.indexOf(CDK_DESCRIBEDBY_ID_PREFIX) != 0,\n    );\n    element.setAttribute('aria-describedby', originalReferenceIds.join(' '));\n  }\n\n  /**\n   * Adds a message reference to the element using aria-describedby and increments the registered\n   * message's reference count.\n   */\n  private _addMessageReference(element: Element, key: string | Element) {\n    const registeredMessage = this._messageRegistry.get(key)!;\n\n    // Add the aria-describedby reference and set the\n    // describedby_host attribute to mark the element.\n    addAriaReferencedId(element, 'aria-describedby', registeredMessage.messageElement.id);\n    element.setAttribute(CDK_DESCRIBEDBY_HOST_ATTRIBUTE, this._id);\n    registeredMessage.referenceCount++;\n  }\n\n  /**\n   * Removes a message reference from the element using aria-describedby\n   * and decrements the registered message's reference count.\n   */\n  private _removeMessageReference(element: Element, key: string | Element) {\n    const registeredMessage = this._messageRegistry.get(key)!;\n    registeredMessage.referenceCount--;\n\n    removeAriaReferencedId(element, 'aria-describedby', registeredMessage.messageElement.id);\n    element.removeAttribute(CDK_DESCRIBEDBY_HOST_ATTRIBUTE);\n  }\n\n  /** Returns true if the element has been described by the provided message ID. */\n  private _isElementDescribedByMessage(element: Element, key: string | Element): boolean {\n    const referenceIds = getAriaReferenceIds(element, 'aria-describedby');\n    const registeredMessage = this._messageRegistry.get(key);\n    const messageId = registeredMessage && registeredMessage.messageElement.id;\n\n    return !!messageId && referenceIds.indexOf(messageId) != -1;\n  }\n\n  /** Determines whether a message can be described on a particular element. */\n  private _canBeDescribed(element: Element, message: string | HTMLElement | void): boolean {\n    if (!this._isElementNode(element)) {\n      return false;\n    }\n\n    if (message && typeof message === 'object') {\n      // We'd have to make some assumptions about the description element's text, if the consumer\n      // passed in an element. Assume that if an element is passed in, the consumer has verified\n      // that it can be used as a description.\n      return true;\n    }\n\n    const trimmedMessage = message == null ? '' : `${message}`.trim();\n    const ariaLabel = element.getAttribute('aria-label');\n\n    // We shouldn't set descriptions if they're exactly the same as the `aria-label` of the\n    // element, because screen readers will end up reading out the same text twice in a row.\n    return trimmedMessage ? !ariaLabel || ariaLabel.trim() !== trimmedMessage : false;\n  }\n\n  /** Checks whether a node is an Element node. */\n  private _isElementNode(element: Node): element is Element {\n    return element.nodeType === this._document.ELEMENT_NODE;\n  }\n}\n\n/** Gets a key that can be used to look messages up in the registry. */\nfunction getKey(message: string | Element, role?: string): string | Element {\n  return typeof message === 'string' ? `${role || ''}/${message}` : message;\n}\n\n/** Assigns a unique ID to an element, if it doesn't have one already. */\nfunction setMessageId(element: HTMLElement, serviceId: string) {\n  if (!element.id) {\n    element.id = `${CDK_DESCRIBEDBY_ID_PREFIX}-${serviceId}-${nextId++}`;\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Subject} from 'rxjs';\nimport {TREE_KEY_MANAGER} from './tree-key-manager';\nimport {\n  TreeKeyManagerFactory,\n  TreeKeyManagerItem,\n  TreeKeyManagerStrategy,\n} from './tree-key-manager-strategy';\nimport {Provider} from '@angular/core';\n\n// NoopTreeKeyManager is a \"noop\" implementation of TreeKeyMangerStrategy. Methods are noops. Does\n// not emit to streams.\n//\n// Used for applications built before TreeKeyManager to opt-out of TreeKeyManager and revert to\n// legacy behavior.\n/**\n * @docs-private\n *\n * Opt-out of Tree of key manager behavior.\n *\n * When provided, Tree has same focus management behavior as before TreeKeyManager was introduced.\n *  - Tree does not respond to keyboard interaction\n *  - Tree node allows tabindex to be set by Input binding\n *  - Tree node allows tabindex to be set by attribute binding\n *\n * @deprecated NoopTreeKeyManager deprecated. Use TreeKeyManager or inject a\n * TreeKeyManagerStrategy instead. To be removed in a future version.\n *\n * @breaking-change 21.0.0\n */\nexport class NoopTreeKeyManager<T extends TreeKeyManagerItem> implements TreeKeyManagerStrategy<T> {\n  readonly _isNoopTreeKeyManager = true;\n\n  // Provide change as required by TreeKeyManagerStrategy. NoopTreeKeyManager is a \"noop\"\n  // implementation that does not emit to streams.\n  readonly change = new Subject<T | null>();\n\n  destroy() {\n    this.change.complete();\n  }\n\n  onKeydown() {\n    // noop\n  }\n\n  getActiveItemIndex() {\n    // Always return null. NoopTreeKeyManager is a \"noop\" implementation that does not maintain\n    // the active item.\n    return null;\n  }\n\n  getActiveItem() {\n    // Always return null. NoopTreeKeyManager is a \"noop\" implementation that does not maintain\n    // the active item.\n    return null;\n  }\n\n  focusItem() {\n    // noop\n  }\n}\n\n/**\n * @docs-private\n *\n * Opt-out of Tree of key manager behavior.\n *\n * When provided, Tree has same focus management behavior as before TreeKeyManager was introduced.\n *  - Tree does not respond to keyboard interaction\n *  - Tree node allows tabindex to be set by Input binding\n *  - Tree node allows tabindex to be set by attribute binding\n *\n * @deprecated NoopTreeKeyManager deprecated. Use TreeKeyManager or inject a\n * TreeKeyManagerStrategy instead. To be removed in a future version.\n *\n * @breaking-change 21.0.0\n */\nexport function NOOP_TREE_KEY_MANAGER_FACTORY<\n  T extends TreeKeyManagerItem,\n>(): TreeKeyManagerFactory<T> {\n  return () => new NoopTreeKeyManager<T>();\n}\n\n/**\n * @docs-private\n *\n * Opt-out of Tree of key manager behavior.\n *\n * When provided, Tree has same focus management behavior as before TreeKeyManager was introduced.\n *  - Tree does not respond to keyboard interaction\n *  - Tree node allows tabindex to be set by Input binding\n *  - Tree node allows tabindex to be set by attribute binding\n *\n * @deprecated NoopTreeKeyManager deprecated. Use TreeKeyManager or inject a\n * TreeKeyManagerStrategy instead. To be removed in a future version.\n *\n * @breaking-change 21.0.0\n */\nexport const NOOP_TREE_KEY_MANAGER_FACTORY_PROVIDER: Provider = {\n  provide: TREE_KEY_MANAGER,\n  useFactory: NOOP_TREE_KEY_MANAGER_FACTORY,\n};\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Injector, NgZone} from '@angular/core';\nimport {InteractivityChecker} from '../interactivity-checker/interactivity-checker';\nimport {ConfigurableFocusTrapConfig} from './configurable-focus-trap-config';\nimport {FocusTrap} from './focus-trap';\nimport {FocusTrapInertStrategy} from './focus-trap-inert-strategy';\nimport {FocusTrapManager, ManagedFocusTrap} from './focus-trap-manager';\n\n/**\n * Class that allows for trapping focus within a DOM element.\n *\n * This class uses a strategy pattern that determines how it traps focus.\n * See FocusTrapInertStrategy.\n */\nexport class ConfigurableFocusTrap extends FocusTrap implements ManagedFocusTrap {\n  /** Whether the FocusTrap is enabled. */\n  override get enabled(): boolean {\n    return this._enabled;\n  }\n  override set enabled(value: boolean) {\n    this._enabled = value;\n    if (this._enabled) {\n      this._focusTrapManager.register(this);\n    } else {\n      this._focusTrapManager.deregister(this);\n    }\n  }\n\n  constructor(\n    _element: HTMLElement,\n    _checker: InteractivityChecker,\n    _ngZone: NgZone,\n    _document: Document,\n    private _focusTrapManager: FocusTrapManager,\n    private _inertStrategy: FocusTrapInertStrategy,\n    config: ConfigurableFocusTrapConfig,\n    injector?: Injector,\n  ) {\n    super(_element, _checker, _ngZone, _document, config.defer, injector);\n    this._focusTrapManager.register(this);\n  }\n\n  /** Notifies the FocusTrapManager that this FocusTrap will be destroyed. */\n  override destroy() {\n    this._focusTrapManager.deregister(this);\n    super.destroy();\n  }\n\n  /** @docs-private Implemented as part of ManagedFocusTrap. */\n  _enable() {\n    this._inertStrategy.preventFocus(this);\n    this.toggleAnchors(true);\n  }\n\n  /** @docs-private Implemented as part of ManagedFocusTrap. */\n  _disable() {\n    this._inertStrategy.allowFocus(this);\n    this.toggleAnchors(false);\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {FocusTrapInertStrategy} from './focus-trap-inert-strategy';\nimport {ConfigurableFocusTrap} from './configurable-focus-trap';\n\n/**\n * Lightweight FocusTrapInertStrategy that adds a document focus event\n * listener to redirect focus back inside the FocusTrap.\n */\nexport class EventListenerFocusTrapInertStrategy implements FocusTrapInertStrategy {\n  /** Focus event handler. */\n  private _listener: ((e: FocusEvent) => void) | null = null;\n\n  /** Adds a document event listener that keeps focus inside the FocusTrap. */\n  preventFocus(focusTrap: ConfigurableFocusTrap): void {\n    // Ensure there's only one listener per document\n    if (this._listener) {\n      focusTrap._document.removeEventListener('focus', this._listener!, true);\n    }\n\n    this._listener = (e: FocusEvent) => this._trapFocus(focusTrap, e);\n    focusTrap._ngZone.runOutsideAngular(() => {\n      focusTrap._document.addEventListener('focus', this._listener!, true);\n    });\n  }\n\n  /** Removes the event listener added in preventFocus. */\n  allowFocus(focusTrap: ConfigurableFocusTrap): void {\n    if (!this._listener) {\n      return;\n    }\n    focusTrap._document.removeEventListener('focus', this._listener!, true);\n    this._listener = null;\n  }\n\n  /**\n   * Refocuses the first element in the FocusTrap if the focus event target was outside\n   * the FocusTrap.\n   *\n   * This is an event listener callback. The event listener is added in runOutsideAngular,\n   * so all this code runs outside Angular as well.\n   */\n  private _trapFocus(focusTrap: ConfigurableFocusTrap, event: FocusEvent) {\n    const target = event.target as HTMLElement;\n    const focusTrapRoot = focusTrap._element;\n\n    // Don't refocus if target was in an overlay, because the overlay might be associated\n    // with an element inside the FocusTrap, ex. mat-select.\n    if (target && !focusTrapRoot.contains(target) && !target.closest?.('div.cdk-overlay-pane')) {\n      // Some legacy FocusTrap usages have logic that focuses some element on the page\n      // just before FocusTrap is destroyed. For backwards compatibility, wait\n      // to be sure FocusTrap is still enabled before refocusing.\n      setTimeout(() => {\n        // Check whether focus wasn't put back into the focus trap while the timeout was pending.\n        if (focusTrap.enabled && !focusTrapRoot.contains(focusTrap._document.activeElement)) {\n          focusTrap.focusFirstTabbableElement();\n        }\n      });\n    }\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\nimport {InjectionToken} from '@angular/core';\nimport {FocusTrap} from './focus-trap';\n\n/** The injection token used to specify the inert strategy. */\nexport const FOCUS_TRAP_INERT_STRATEGY = new InjectionToken<FocusTrapInertStrategy>(\n  'FOCUS_TRAP_INERT_STRATEGY',\n);\n\n/**\n * A strategy that dictates how FocusTrap should prevent elements\n * outside of the FocusTrap from being focused.\n */\nexport interface FocusTrapInertStrategy {\n  /** Makes all elements outside focusTrap unfocusable. */\n  preventFocus(focusTrap: FocusTrap): void;\n  /** Reverts elements made unfocusable by preventFocus to their previous state. */\n  allowFocus(focusTrap: FocusTrap): void;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Injectable} from '@angular/core';\n\n/**\n * A FocusTrap managed by FocusTrapManager.\n * Implemented by ConfigurableFocusTrap to avoid circular dependency.\n */\nexport interface ManagedFocusTrap {\n  _enable(): void;\n  _disable(): void;\n  focusInitialElementWhenReady(): Promise<boolean>;\n}\n\n/** Injectable that ensures only the most recently enabled FocusTrap is active. */\n@Injectable({providedIn: 'root'})\nexport class FocusTrapManager {\n  // A stack of the FocusTraps on the page. Only the FocusTrap at the\n  // top of the stack is active.\n  private _focusTrapStack: ManagedFocusTrap[] = [];\n\n  /**\n   * Disables the FocusTrap at the top of the stack, and then pushes\n   * the new FocusTrap onto the stack.\n   */\n  register(focusTrap: ManagedFocusTrap): void {\n    // Dedupe focusTraps that register multiple times.\n    this._focusTrapStack = this._focusTrapStack.filter(ft => ft !== focusTrap);\n\n    let stack = this._focusTrapStack;\n\n    if (stack.length) {\n      stack[stack.length - 1]._disable();\n    }\n\n    stack.push(focusTrap);\n    focusTrap._enable();\n  }\n\n  /**\n   * Removes the FocusTrap from the stack, and activates the\n   * FocusTrap that is the new top of the stack.\n   */\n  deregister(focusTrap: ManagedFocusTrap): void {\n    focusTrap._disable();\n\n    const stack = this._focusTrapStack;\n\n    const i = stack.indexOf(focusTrap);\n    if (i !== -1) {\n      stack.splice(i, 1);\n      if (stack.length) {\n        stack[stack.length - 1]._enable();\n      }\n    }\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Injectable, Injector, NgZone, inject, DOCUMENT} from '@angular/core';\nimport {Interact<PERSON>Checker} from '../interactivity-checker/interactivity-checker';\nimport {ConfigurableFocusTrap} from './configurable-focus-trap';\nimport {ConfigurableFocusTrapConfig} from './configurable-focus-trap-config';\nimport {EventListenerFocusTrapInertStrategy} from './event-listener-inert-strategy';\nimport {FOCUS_TRAP_INERT_STRATEGY, FocusTrapInertStrategy} from './focus-trap-inert-strategy';\nimport {FocusTrapManager} from './focus-trap-manager';\n\n/** Factory that allows easy instantiation of configurable focus traps. */\n@Injectable({providedIn: 'root'})\nexport class ConfigurableFocusTrapFactory {\n  private _checker = inject(InteractivityChecker);\n  private _ngZone = inject(NgZone);\n  private _focusTrapManager = inject(FocusTrapManager);\n\n  private _document = inject(DOCUMENT);\n  private _inertStrategy: FocusTrapInertStrategy;\n\n  private readonly _injector = inject(Injector);\n\n  constructor(...args: unknown[]);\n\n  constructor() {\n    const inertStrategy = inject(FOCUS_TRAP_INERT_STRATEGY, {optional: true});\n\n    // TODO split up the strategies into different modules, similar to DateAdapter.\n    this._inertStrategy = inertStrategy || new EventListenerFocusTrapInertStrategy();\n  }\n\n  /**\n   * Creates a focus-trapped region around the given element.\n   * @param element The element around which focus will be trapped.\n   * @param config The focus trap configuration.\n   * @returns The created focus trap instance.\n   */\n  create(element: HTMLElement, config?: ConfigurableFocusTrapConfig): ConfigurableFocusTrap;\n\n  /**\n   * @deprecated Pass a config object instead of the `deferCaptureElements` flag.\n   * @breaking-change 11.0.0\n   */\n  create(element: HTMLElement, deferCaptureElements: boolean): ConfigurableFocusTrap;\n\n  create(\n    element: HTMLElement,\n    config: ConfigurableFocusTrapConfig | boolean = {defer: false},\n  ): ConfigurableFocusTrap {\n    let configObject: ConfigurableFocusTrapConfig;\n    if (typeof config === 'boolean') {\n      configObject = {defer: config};\n    } else {\n      configObject = config;\n    }\n    return new ConfigurableFocusTrap(\n      element,\n      this._checker,\n      this._ngZone,\n      this._document,\n      this._focusTrapManager,\n      this._inertStrategy,\n      configObject,\n      this._injector,\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQA;AACA,MAAM,YAAY,GAAG,GAAG;AAExB;;;AAGG;SACa,mBAAmB,CAAC,EAAW,EAAE,IAAsB,EAAE,EAAU,EAAA;IACjF,MAAM,GAAG,GAAG,mBAAmB,CAAC,EAAE,EAAE,IAAI,CAAC;AACzC,IAAA,EAAE,GAAG,EAAE,CAAC,IAAI,EAAE;AACd,IAAA,IAAI,GAAG,CAAC,IAAI,CAAC,UAAU,IAAI,UAAU,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC,EAAE;QACpD;;AAEF,IAAA,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;AAEZ,IAAA,EAAE,CAAC,YAAY,CAAC,IAAI,EAAE,GAAG,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;AAC/C;AAEA;;;AAGG;SACa,sBAAsB,CAAC,EAAW,EAAE,IAAsB,EAAE,EAAU,EAAA;IACpF,MAAM,GAAG,GAAG,mBAAmB,CAAC,EAAE,EAAE,IAAI,CAAC;AACzC,IAAA,EAAE,GAAG,EAAE,CAAC,IAAI,EAAE;AACd,IAAA,MAAM,WAAW,GAAG,GAAG,CAAC,MAAM,CAAC,GAAG,IAAI,GAAG,KAAK,EAAE,CAAC;AAEjD,IAAA,IAAI,WAAW,CAAC,MAAM,EAAE;AACtB,QAAA,EAAE,CAAC,YAAY,CAAC,IAAI,EAAE,WAAW,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;;SAChD;AACL,QAAA,EAAE,CAAC,eAAe,CAAC,IAAI,CAAC;;AAE5B;AAEA;;;AAGG;AACa,SAAA,mBAAmB,CAAC,EAAW,EAAE,IAAY,EAAA;;IAE3D,MAAM,SAAS,GAAG,EAAE,CAAC,YAAY,CAAC,IAAI,CAAC;IACvC,OAAO,SAAS,EAAE,KAAK,CAAC,MAAM,CAAC,IAAI,EAAE;AACvC;;ACzBA;;;;AAIG;AACI,MAAM,qBAAqB,GAAG;AAErC;;;;AAIG;AACI,MAAM,yBAAyB,GAAG;AAEzC;;;;AAIG;AACI,MAAM,8BAA8B,GAAG;AAE9C;AACA,IAAI,MAAM,GAAG,CAAC;AAEd;;;;AAIG;MAEU,aAAa,CAAA;AAChB,IAAA,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC;AAC5B,IAAA,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC;;AAG5B,IAAA,gBAAgB,GAAG,IAAI,GAAG,EAAuC;;IAGjE,kBAAkB,GAAuB,IAAI;;AAGpC,IAAA,GAAG,GAAG,CAAA,EAAG,MAAM,EAAE,EAAE;AAIpC,IAAA,WAAA,GAAA;QACE,MAAM,CAAC,sBAAsB,CAAC,CAAC,IAAI,CAAC,qBAAqB,CAAC;AAC1D,QAAA,IAAI,CAAC,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC,GAAG,GAAG,GAAG,MAAM,EAAE;;AAe5C,IAAA,QAAQ,CAAC,WAAoB,EAAE,OAA6B,EAAE,IAAa,EAAA;QACzE,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,WAAW,EAAE,OAAO,CAAC,EAAE;YAC/C;;QAGF,MAAM,GAAG,GAAG,MAAM,CAAC,OAAO,EAAE,IAAI,CAAC;AAEjC,QAAA,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;;AAE/B,YAAA,YAAY,CAAC,OAAO,EAAE,IAAI,CAAC,GAAG,CAAC;AAC/B,YAAA,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,GAAG,EAAE,EAAC,cAAc,EAAE,OAAO,EAAE,cAAc,EAAE,CAAC,EAAC,CAAC;;aACvE,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;AAC1C,YAAA,IAAI,CAAC,qBAAqB,CAAC,OAAO,EAAE,IAAI,CAAC;;QAG3C,IAAI,CAAC,IAAI,CAAC,4BAA4B,CAAC,WAAW,EAAE,GAAG,CAAC,EAAE;AACxD,YAAA,IAAI,CAAC,oBAAoB,CAAC,WAAW,EAAE,GAAG,CAAC;;;AAU/C,IAAA,iBAAiB,CAAC,WAAoB,EAAE,OAA6B,EAAE,IAAa,EAAA;QAClF,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,EAAE;YACjD;;QAGF,MAAM,GAAG,GAAG,MAAM,CAAC,OAAO,EAAE,IAAI,CAAC;QAEjC,IAAI,IAAI,CAAC,4BAA4B,CAAC,WAAW,EAAE,GAAG,CAAC,EAAE;AACvD,YAAA,IAAI,CAAC,uBAAuB,CAAC,WAAW,EAAE,GAAG,CAAC;;;;AAKhD,QAAA,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;YAC/B,MAAM,iBAAiB,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,GAAG,CAAC;YACxD,IAAI,iBAAiB,IAAI,iBAAiB,CAAC,cAAc,KAAK,CAAC,EAAE;AAC/D,gBAAA,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC;;;QAInC,IAAI,IAAI,CAAC,kBAAkB,EAAE,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE;AACpD,YAAA,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE;AAChC,YAAA,IAAI,CAAC,kBAAkB,GAAG,IAAI;;;;IAKlC,WAAW,GAAA;AACT,QAAA,MAAM,iBAAiB,GAAG,IAAI,CAAC,SAAS,CAAC,gBAAgB,CACvD,CAAI,CAAA,EAAA,8BAA8B,KAAK,IAAI,CAAC,GAAG,CAAA,EAAA,CAAI,CACpD;AAED,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,iBAAiB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACjD,IAAI,CAAC,iCAAiC,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC;YAC5D,iBAAiB,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,8BAA8B,CAAC;;AAGtE,QAAA,IAAI,CAAC,kBAAkB,EAAE,MAAM,EAAE;AACjC,QAAA,IAAI,CAAC,kBAAkB,GAAG,IAAI;AAC9B,QAAA,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE;;AAG/B;;;AAGG;IACK,qBAAqB,CAAC,OAAe,EAAE,IAAa,EAAA;QAC1D,MAAM,cAAc,GAAG,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC;AAC1D,QAAA,YAAY,CAAC,cAAc,EAAE,IAAI,CAAC,GAAG,CAAC;AACtC,QAAA,cAAc,CAAC,WAAW,GAAG,OAAO;QAEpC,IAAI,IAAI,EAAE;AACR,YAAA,cAAc,CAAC,YAAY,CAAC,MAAM,EAAE,IAAI,CAAC;;QAG3C,IAAI,CAAC,wBAAwB,EAAE;AAC/B,QAAA,IAAI,CAAC,kBAAmB,CAAC,WAAW,CAAC,cAAc,CAAC;QACpD,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,MAAM,CAAC,OAAO,EAAE,IAAI,CAAC,EAAE,EAAC,cAAc,EAAE,cAAc,EAAE,CAAC,EAAC,CAAC;;;AAI/E,IAAA,qBAAqB,CAAC,GAAqB,EAAA;AACjD,QAAA,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,cAAc,EAAE,MAAM,EAAE;AACxD,QAAA,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,GAAG,CAAC;;;IAI3B,wBAAwB,GAAA;AAC9B,QAAA,IAAI,IAAI,CAAC,kBAAkB,EAAE;YAC3B;;QAGF,MAAM,kBAAkB,GAAG,mCAAmC;AAC9D,QAAA,MAAM,gBAAgB,GAAG,IAAI,CAAC,SAAS,CAAC,gBAAgB,CACtD,CAAI,CAAA,EAAA,kBAAkB,CAAqB,mBAAA,CAAA,CAC5C;AAED,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,gBAAgB,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;;;;;AAKhD,YAAA,gBAAgB,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE;;QAG9B,MAAM,iBAAiB,GAAG,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC;;;;;AAM7D,QAAA,iBAAiB,CAAC,KAAK,CAAC,UAAU,GAAG,QAAQ;;;AAG7C,QAAA,iBAAiB,CAAC,SAAS,CAAC,GAAG,CAAC,kBAAkB,CAAC;AACnD,QAAA,iBAAiB,CAAC,SAAS,CAAC,GAAG,CAAC,qBAAqB,CAAC;AAEtD,QAAA,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE;AAC7B,YAAA,iBAAiB,CAAC,YAAY,CAAC,UAAU,EAAE,QAAQ,CAAC;;QAGtD,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,iBAAiB,CAAC;AAClD,QAAA,IAAI,CAAC,kBAAkB,GAAG,iBAAiB;;;AAIrC,IAAA,iCAAiC,CAAC,OAAgB,EAAA;;QAExD,MAAM,oBAAoB,GAAG,mBAAmB,CAAC,OAAO,EAAE,kBAAkB,CAAC,CAAC,MAAM,CAClF,EAAE,IAAI,EAAE,CAAC,OAAO,CAAC,yBAAyB,CAAC,IAAI,CAAC,CACjD;AACD,QAAA,OAAO,CAAC,YAAY,CAAC,kBAAkB,EAAE,oBAAoB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;;AAG1E;;;AAGG;IACK,oBAAoB,CAAC,OAAgB,EAAE,GAAqB,EAAA;QAClE,MAAM,iBAAiB,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,GAAG,CAAE;;;QAIzD,mBAAmB,CAAC,OAAO,EAAE,kBAAkB,EAAE,iBAAiB,CAAC,cAAc,CAAC,EAAE,CAAC;QACrF,OAAO,CAAC,YAAY,CAAC,8BAA8B,EAAE,IAAI,CAAC,GAAG,CAAC;QAC9D,iBAAiB,CAAC,cAAc,EAAE;;AAGpC;;;AAGG;IACK,uBAAuB,CAAC,OAAgB,EAAE,GAAqB,EAAA;QACrE,MAAM,iBAAiB,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,GAAG,CAAE;QACzD,iBAAiB,CAAC,cAAc,EAAE;QAElC,sBAAsB,CAAC,OAAO,EAAE,kBAAkB,EAAE,iBAAiB,CAAC,cAAc,CAAC,EAAE,CAAC;AACxF,QAAA,OAAO,CAAC,eAAe,CAAC,8BAA8B,CAAC;;;IAIjD,4BAA4B,CAAC,OAAgB,EAAE,GAAqB,EAAA;QAC1E,MAAM,YAAY,GAAG,mBAAmB,CAAC,OAAO,EAAE,kBAAkB,CAAC;QACrE,MAAM,iBAAiB,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,GAAG,CAAC;QACxD,MAAM,SAAS,GAAG,iBAAiB,IAAI,iBAAiB,CAAC,cAAc,CAAC,EAAE;AAE1E,QAAA,OAAO,CAAC,CAAC,SAAS,IAAI,YAAY,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;;;IAIrD,eAAe,CAAC,OAAgB,EAAE,OAAoC,EAAA;QAC5E,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,EAAE;AACjC,YAAA,OAAO,KAAK;;AAGd,QAAA,IAAI,OAAO,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;;;;AAI1C,YAAA,OAAO,IAAI;;AAGb,QAAA,MAAM,cAAc,GAAG,OAAO,IAAI,IAAI,GAAG,EAAE,GAAG,CAAG,EAAA,OAAO,EAAE,CAAC,IAAI,EAAE;QACjE,MAAM,SAAS,GAAG,OAAO,CAAC,YAAY,CAAC,YAAY,CAAC;;;AAIpD,QAAA,OAAO,cAAc,GAAG,CAAC,SAAS,IAAI,SAAS,CAAC,IAAI,EAAE,KAAK,cAAc,GAAG,KAAK;;;AAI3E,IAAA,cAAc,CAAC,OAAa,EAAA;QAClC,OAAO,OAAO,CAAC,QAAQ,KAAK,IAAI,CAAC,SAAS,CAAC,YAAY;;uGAtO9C,aAAa,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA;AAAb,IAAA,OAAA,KAAA,GAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,aAAa,cADD,MAAM,EAAA,CAAA;;2FAClB,aAAa,EAAA,UAAA,EAAA,CAAA;kBADzB,UAAU;mBAAC,EAAC,UAAU,EAAE,MAAM,EAAC;;AA2OhC;AACA,SAAS,MAAM,CAAC,OAAyB,EAAE,IAAa,EAAA;AACtD,IAAA,OAAO,OAAO,OAAO,KAAK,QAAQ,GAAG,CAAG,EAAA,IAAI,IAAI,EAAE,IAAI,OAAO,CAAA,CAAE,GAAG,OAAO;AAC3E;AAEA;AACA,SAAS,YAAY,CAAC,OAAoB,EAAE,SAAiB,EAAA;AAC3D,IAAA,IAAI,CAAC,OAAO,CAAC,EAAE,EAAE;QACf,OAAO,CAAC,EAAE,GAAG,CAAG,EAAA,yBAAyB,CAAI,CAAA,EAAA,SAAS,CAAI,CAAA,EAAA,MAAM,EAAE,CAAA,CAAE;;AAExE;;AC1RA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AAcG;MACU,kBAAkB,CAAA;IACpB,qBAAqB,GAAG,IAAI;;;AAI5B,IAAA,MAAM,GAAG,IAAI,OAAO,EAAY;IAEzC,OAAO,GAAA;AACL,QAAA,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE;;IAGxB,SAAS,GAAA;;;IAIT,kBAAkB,GAAA;;;AAGhB,QAAA,OAAO,IAAI;;IAGb,aAAa,GAAA;;;AAGX,QAAA,OAAO,IAAI;;IAGb,SAAS,GAAA;;;AAGV;AAED;;;;;;;;;;;;;;AAcG;SACa,6BAA6B,GAAA;AAG3C,IAAA,OAAO,MAAM,IAAI,kBAAkB,EAAK;AAC1C;AAEA;;;;;;;;;;;;;;AAcG;AACU,MAAA,sCAAsC,GAAa;AAC9D,IAAA,OAAO,EAAE,gBAAgB;AACzB,IAAA,UAAU,EAAE,6BAA6B;;;AC5F3C;;;;;AAKG;AACG,MAAO,qBAAsB,SAAQ,SAAS,CAAA;AAmBxC,IAAA,iBAAA;AACA,IAAA,cAAA;;AAlBV,IAAA,IAAa,OAAO,GAAA;QAClB,OAAO,IAAI,CAAC,QAAQ;;IAEtB,IAAa,OAAO,CAAC,KAAc,EAAA;AACjC,QAAA,IAAI,CAAC,QAAQ,GAAG,KAAK;AACrB,QAAA,IAAI,IAAI,CAAC,QAAQ,EAAE;AACjB,YAAA,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,IAAI,CAAC;;aAChC;AACL,YAAA,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,IAAI,CAAC;;;AAI3C,IAAA,WAAA,CACE,QAAqB,EACrB,QAA8B,EAC9B,OAAe,EACf,SAAmB,EACX,iBAAmC,EACnC,cAAsC,EAC9C,MAAmC,EACnC,QAAmB,EAAA;AAEnB,QAAA,KAAK,CAAC,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,SAAS,EAAE,MAAM,CAAC,KAAK,EAAE,QAAQ,CAAC;QAL7D,IAAiB,CAAA,iBAAA,GAAjB,iBAAiB;QACjB,IAAc,CAAA,cAAA,GAAd,cAAc;AAKtB,QAAA,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,IAAI,CAAC;;;IAI9B,OAAO,GAAA;AACd,QAAA,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,IAAI,CAAC;QACvC,KAAK,CAAC,OAAO,EAAE;;;IAIjB,OAAO,GAAA;AACL,QAAA,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,IAAI,CAAC;AACtC,QAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC;;;IAI1B,QAAQ,GAAA;AACN,QAAA,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,IAAI,CAAC;AACpC,QAAA,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC;;AAE5B;;ACvDD;;;AAGG;MACU,mCAAmC,CAAA;;IAEtC,SAAS,GAAqC,IAAI;;AAG1D,IAAA,YAAY,CAAC,SAAgC,EAAA;;AAE3C,QAAA,IAAI,IAAI,CAAC,SAAS,EAAE;AAClB,YAAA,SAAS,CAAC,SAAS,CAAC,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAAC,SAAU,EAAE,IAAI,CAAC;;AAGzE,QAAA,IAAI,CAAC,SAAS,GAAG,CAAC,CAAa,KAAK,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE,CAAC,CAAC;AACjE,QAAA,SAAS,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAK;AACvC,YAAA,SAAS,CAAC,SAAS,CAAC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC,SAAU,EAAE,IAAI,CAAC;AACtE,SAAC,CAAC;;;AAIJ,IAAA,UAAU,CAAC,SAAgC,EAAA;AACzC,QAAA,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB;;AAEF,QAAA,SAAS,CAAC,SAAS,CAAC,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAAC,SAAU,EAAE,IAAI,CAAC;AACvE,QAAA,IAAI,CAAC,SAAS,GAAG,IAAI;;AAGvB;;;;;;AAMG;IACK,UAAU,CAAC,SAAgC,EAAE,KAAiB,EAAA;AACpE,QAAA,MAAM,MAAM,GAAG,KAAK,CAAC,MAAqB;AAC1C,QAAA,MAAM,aAAa,GAAG,SAAS,CAAC,QAAQ;;;AAIxC,QAAA,IAAI,MAAM,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,GAAG,sBAAsB,CAAC,EAAE;;;;YAI1F,UAAU,CAAC,MAAK;;AAEd,gBAAA,IAAI,SAAS,CAAC,OAAO,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,SAAS,CAAC,SAAS,CAAC,aAAa,CAAC,EAAE;oBACnF,SAAS,CAAC,yBAAyB,EAAE;;AAEzC,aAAC,CAAC;;;AAGP;;ACxDD;MACa,yBAAyB,GAAG,IAAI,cAAc,CACzD,2BAA2B;;ACQ7B;MAEa,gBAAgB,CAAA;;;IAGnB,eAAe,GAAuB,EAAE;AAEhD;;;AAGG;AACH,IAAA,QAAQ,CAAC,SAA2B,EAAA;;AAElC,QAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,KAAK,SAAS,CAAC;AAE1E,QAAA,IAAI,KAAK,GAAG,IAAI,CAAC,eAAe;AAEhC,QAAA,IAAI,KAAK,CAAC,MAAM,EAAE;YAChB,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,QAAQ,EAAE;;AAGpC,QAAA,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC;QACrB,SAAS,CAAC,OAAO,EAAE;;AAGrB;;;AAGG;AACH,IAAA,UAAU,CAAC,SAA2B,EAAA;QACpC,SAAS,CAAC,QAAQ,EAAE;AAEpB,QAAA,MAAM,KAAK,GAAG,IAAI,CAAC,eAAe;QAElC,MAAM,CAAC,GAAG,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC;AAClC,QAAA,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;AACZ,YAAA,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;AAClB,YAAA,IAAI,KAAK,CAAC,MAAM,EAAE;gBAChB,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,OAAO,EAAE;;;;uGApC5B,gBAAgB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA;AAAhB,IAAA,OAAA,KAAA,GAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,gBAAgB,cADJ,MAAM,EAAA,CAAA;;2FAClB,gBAAgB,EAAA,UAAA,EAAA,CAAA;kBAD5B,UAAU;mBAAC,EAAC,UAAU,EAAE,MAAM,EAAC;;;ACLhC;MAEa,4BAA4B,CAAA;AAC/B,IAAA,QAAQ,GAAG,MAAM,CAAC,oBAAoB,CAAC;AACvC,IAAA,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC;AACxB,IAAA,iBAAiB,GAAG,MAAM,CAAC,gBAAgB,CAAC;AAE5C,IAAA,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC;AAC5B,IAAA,cAAc;AAEL,IAAA,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC;AAI7C,IAAA,WAAA,GAAA;AACE,QAAA,MAAM,aAAa,GAAG,MAAM,CAAC,yBAAyB,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;;QAGzE,IAAI,CAAC,cAAc,GAAG,aAAa,IAAI,IAAI,mCAAmC,EAAE;;IAiBlF,MAAM,CACJ,OAAoB,EACpB,MAAA,GAAgD,EAAC,KAAK,EAAE,KAAK,EAAC,EAAA;AAE9D,QAAA,IAAI,YAAyC;AAC7C,QAAA,IAAI,OAAO,MAAM,KAAK,SAAS,EAAE;AAC/B,YAAA,YAAY,GAAG,EAAC,KAAK,EAAE,MAAM,EAAC;;aACzB;YACL,YAAY,GAAG,MAAM;;AAEvB,QAAA,OAAO,IAAI,qBAAqB,CAC9B,OAAO,EACP,IAAI,CAAC,QAAQ,EACb,IAAI,CAAC,OAAO,EACZ,IAAI,CAAC,SAAS,EACd,IAAI,CAAC,iBAAiB,EACtB,IAAI,CAAC,cAAc,EACnB,YAAY,EACZ,IAAI,CAAC,SAAS,CACf;;uGApDQ,4BAA4B,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA;AAA5B,IAAA,OAAA,KAAA,GAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,4BAA4B,cADhB,MAAM,EAAA,CAAA;;2FAClB,4BAA4B,EAAA,UAAA,EAAA,CAAA;kBADxC,UAAU;mBAAC,EAAC,UAAU,EAAE,MAAM,EAAC;;;;;"}