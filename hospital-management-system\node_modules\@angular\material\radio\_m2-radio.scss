@use 'sass:map';
@use '../core/tokens/m2-utils';
@use '../core/theming/theming';
@use '../core/theming/inspection';
@use '../core/style/sass-utils';
@use '../core/m2/palette' as m2-palette;
@use '../core/m2/theming' as m2-theming;

// Tokens that can't be configured through Angular Material's current theming API,
// but may be in a future version of the theming API.
@function get-unthemable-tokens() {
  @return (
    radio-disabled-selected-icon-opacity: 0.38,
    radio-disabled-unselected-icon-opacity: 0.38,
    // This is specified both here and in the density tokens, because it
    // determines the size of the radio button itself and there are internal
    // tests who don't configure the theme correctly.
    radio-state-layer-size: 40px,
  );
}

// Tokens that can be configured through Angular Material's color theming API.
@function get-color-tokens($theme, $palette-name: accent) {
  $is-dark: inspection.get-theme-type($theme) == dark;
  $palette-color: inspection.get-theme-color($theme, $palette-name, default);
  $icon-color: m2-theming.get-color-from-palette(m2-palette.$gray-palette, if($is-dark, 200, 900));

  @return (
    radio-checked-ripple-color: inspection.get-theme-color($theme, $palette-name, default),
    radio-disabled-label-color: inspection.get-theme-color($theme, foreground, disabled-text),
    radio-disabled-selected-icon-color: inspection.get-theme-color($theme, foreground, icon, 1),
    radio-disabled-unselected-icon-color: inspection.get-theme-color($theme, foreground, icon, 1),
    radio-label-text-color: inspection.get-theme-color($theme, foreground, text),
    radio-ripple-color: inspection.get-theme-color($theme, foreground, base),
    radio-selected-focus-icon-color: $palette-color,
    radio-selected-hover-icon-color: $palette-color,
    radio-selected-icon-color: $palette-color,
    radio-selected-pressed-icon-color: $palette-color,
    radio-unselected-focus-icon-color: $icon-color,
    radio-unselected-hover-icon-color: $icon-color,
    radio-unselected-icon-color: inspection.get-theme-color($theme, foreground, icon, 0.54),
    radio-unselected-pressed-icon-color: inspection.get-theme-color($theme, foreground, icon, 0.54),
  );
}

// Tokens that can be configured through Angular Material's typography theming API.
@function get-typography-tokens($theme) {
  @return (
    radio-label-text-font: inspection.get-theme-typography($theme, body-2, font-family),
    radio-label-text-line-height: inspection.get-theme-typography($theme, body-2, line-height),
    radio-label-text-size: inspection.get-theme-typography($theme, body-2, font-size),
    radio-label-text-tracking: inspection.get-theme-typography($theme, body-2, letter-spacing),
    radio-label-text-weight: inspection.get-theme-typography($theme, body-2, font-weight)
  );
}

// Tokens that can be configured through Angular Material's density theming API.
@function get-density-tokens($theme) {
  $scale: theming.clamp-density(inspection.get-theme-density($theme), -3);

  @return (
    // The diameter of the radio's ripple.
    radio-state-layer-size: map.get((
      0: 40px,
      -1: 36px,
      -2: 32px,
      -3: 28px,
    ), $scale),
    radio-touch-target-display: if($scale < -1, none, block)
  );
}

// Combines the tokens generated by the above functions into a single map with placeholder values.
// This is used to create token slots.
@function get-token-slots() {
  @return sass-utils.deep-merge-all(
    get-unthemable-tokens(),
    get-color-tokens(m2-utils.$placeholder-color-config),
    get-typography-tokens(m2-utils.$placeholder-typography-config),
    get-density-tokens(m2-utils.$placeholder-density-config)
  );
}
