{"version": 3, "file": "passive-listeners-esHZRgIN.mjs", "sources": ["../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/cdk/platform/features/passive-listeners.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n/** Cached result of whether the user's browser supports passive event listeners. */\nlet supportsPassiveEvents: boolean;\n\n/**\n * Checks whether the user's browser supports passive event listeners.\n * See: https://github.com/WICG/EventListenerOptions/blob/gh-pages/explainer.md\n */\nexport function supportsPassiveEventListeners(): boolean {\n  if (supportsPassiveEvents == null && typeof window !== 'undefined') {\n    try {\n      window.addEventListener(\n        'test',\n        null!,\n        Object.defineProperty({}, 'passive', {\n          get: () => (supportsPassiveEvents = true),\n        }),\n      );\n    } finally {\n      supportsPassiveEvents = supportsPassiveEvents || false;\n    }\n  }\n\n  return supportsPassiveEvents;\n}\n\n/**\n * Normalizes an `AddEventListener` object to something that can be passed\n * to `addEventListener` on any browser, no matter whether it supports the\n * `options` parameter.\n * @param options Object to be normalized.\n */\nexport function normalizePassiveListenerOptions(\n  options: AddEventListenerOptions,\n): AddEventListenerOptions | boolean {\n  return supportsPassiveEventListeners() ? options : !!options.capture;\n}\n"], "names": [], "mappings": "AAQA;AACA,IAAI,qBAA8B;AAElC;;;AAGG;SACa,6BAA6B,GAAA;IAC3C,IAAI,qBAAqB,IAAI,IAAI,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;AAClE,QAAA,IAAI;AACF,YAAA,MAAM,CAAC,gBAAgB,CACrB,MAAM,EACN,IAAK,EACL,MAAM,CAAC,cAAc,CAAC,EAAE,EAAE,SAAS,EAAE;gBACnC,GAAG,EAAE,OAAO,qBAAqB,GAAG,IAAI,CAAC;AAC1C,aAAA,CAAC,CACH;;gBACO;AACR,YAAA,qBAAqB,GAAG,qBAAqB,IAAI,KAAK;;;AAI1D,IAAA,OAAO,qBAAqB;AAC9B;AAEA;;;;;AAKG;AACG,SAAU,+BAA+B,CAC7C,OAAgC,EAAA;AAEhC,IAAA,OAAO,6BAA6B,EAAE,GAAG,OAAO,GAAG,CAAC,CAAC,OAAO,CAAC,OAAO;AACtE;;;;"}