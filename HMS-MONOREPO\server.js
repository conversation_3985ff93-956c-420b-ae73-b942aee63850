const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');
const connectDB = require('./back-end/config/database');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 3000;

// Connect to MongoDB
connectDB();

// Middleware
app.use(cors());
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));

// Routes
app.get('/', (req, res) => {
  res.json({ message: 'Hospital Management System API is running!' });
});

// API Routes
app.use('/api/auth', require('./back-end/routes/auth'));
app.use('/api/patients', require('./back-end/routes/patients'));
app.use('/api/doctors', require('./back-end/routes/doctors'));
app.use('/api/appointments', require('./back-end/routes/appointments'));
app.use('/api/reports', require('./back-end/routes/reports'));
app.use('/api/prescriptions', require('./back-end/routes/prescriptions'));
// app.use('/api/billing', require('./back-end/routes/billing'));

// Error handling middleware
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({ message: 'Something went wrong!' });
});

// Start server
app.listen(PORT, () => {
  console.log(`Server is running on port ${PORT}`);
});

module.exports = app;
