const axios = require('axios');

const API_BASE = 'http://localhost:3000/api';

// Test users for different roles
const testUsers = {
  admin: {
    username: 'admin',
    email: '<EMAIL>',
    password: 'admin123',
    role: 'Admin',
    firstName: 'System',
    lastName: 'Administrator',
    phone: '**********'
  },
  doctor: {
    username: 'doctor1',
    email: '<EMAIL>',
    password: 'doctor123',
    role: 'Doctor',
    firstName: 'Dr. <PERSON>',
    lastName: '<PERSON>',
    phone: '**********'
  },
  patient: {
    username: 'patient1',
    email: '<EMAIL>',
    password: 'patient123',
    role: 'Patient',
    firstName: '<PERSON>',
    lastName: 'Doe',
    phone: '**********'
  }
};

let tokens = {};
let userIds = {};

// Helper function to make API calls
const apiCall = async (method, endpoint, data = null, token = null) => {
  try {
    const config = {
      method,
      url: `${API_BASE}${endpoint}`,
      headers: token ? { Authorization: `Bearer ${token}` } : {}
    };

    if (data) {
      config.data = data;
    }

    const response = await axios(config);
    return { success: true, data: response.data };
  } catch (error) {
    return {
      success: false,
      error: error.response?.data?.message || error.message,
      status: error.response?.status
    };
  }
};

// Register and login test users
const setupTestUsers = async () => {
  console.log('🔧 Setting up test users...\n');

  for (const [role, userData] of Object.entries(testUsers)) {
    console.log(`Setting up ${role}...`);

    // Try to register user first
    const registerResult = await apiCall('POST', '/auth/register', userData);
    if (registerResult.success) {
      tokens[role] = registerResult.data.token;
      userIds[role] = registerResult.data.user._id;
      console.log(`✅ ${role} registered successfully`);
    } else {
      // If registration fails (user exists), try to login
      console.log(`User exists, attempting login for ${role}...`);
      const loginResult = await apiCall('POST', '/auth/login', {
        email: userData.email,
        password: userData.password
      });

      if (loginResult.success) {
        tokens[role] = loginResult.data.token;
        userIds[role] = loginResult.data.user._id;
        console.log(`✅ ${role} logged in successfully`);
      } else {
        console.log(`❌ ${role} login failed: ${loginResult.error}`);
      }
    }
  }
  console.log('\n');
};

// Test role-based access to patients endpoint
const testPatientsAccess = async () => {
  console.log('🧪 Testing Patients Access Control...\n');

  // Test Admin access (should work)
  console.log('Admin accessing patients list:');
  const adminResult = await apiCall('GET', '/patients', null, tokens.admin);
  console.log(adminResult.success ? '✅ Admin can access patients' : `❌ Admin denied: ${adminResult.error}`);

  // Test Doctor access (should work)
  console.log('Doctor accessing patients list:');
  const doctorResult = await apiCall('GET', '/patients', null, tokens.doctor);
  console.log(doctorResult.success ? '✅ Doctor can access patients' : `❌ Doctor denied: ${doctorResult.error}`);

  // Test Patient access (should be denied)
  console.log('Patient accessing patients list:');
  const patientResult = await apiCall('GET', '/patients', null, tokens.patient);
  console.log(!patientResult.success ? '✅ Patient correctly denied access' : `❌ Patient should not access patients list`);

  console.log('\n');
};

// Test patient accessing their own data vs others
const testPatientOwnDataAccess = async () => {
  console.log('🧪 Testing Patient Own Data Access...\n');

  // Patient accessing their own data (should work)
  console.log('Patient accessing own profile:');
  const ownDataResult = await apiCall('GET', `/patients/${userIds.patient}`, null, tokens.patient);
  console.log(ownDataResult.success ? '✅ Patient can access own data' : `❌ Patient denied own data: ${ownDataResult.error}`);

  // Patient trying to access admin's data (should be denied)
  console.log('Patient trying to access admin data:');
  const otherDataResult = await apiCall('GET', `/patients/${userIds.admin}`, null, tokens.patient);
  console.log(!otherDataResult.success ? '✅ Patient correctly denied access to other data' : `❌ Patient should not access other data`);

  console.log('\n');
};

// Test doctor management (only admin should be able to create/update/delete)
const testDoctorManagement = async () => {
  console.log('🧪 Testing Doctor Management Access...\n');

  const newDoctor = {
    firstName: 'Test',
    lastName: 'Doctor',
    email: '<EMAIL>',
    phone: '**********',
    specialization: 'General Medicine',
    qualification: 'MBBS',
    experience: 5,
    department: 'General',
    consultationFee: 500,
    licenseNumber: 'LIC123456'
  };

  // Admin creating doctor (should work)
  console.log('Admin creating new doctor:');
  const adminCreateResult = await apiCall('POST', '/doctors', newDoctor, tokens.admin);
  console.log(adminCreateResult.success ? '✅ Admin can create doctors' : `❌ Admin denied: ${adminCreateResult.error}`);

  // Doctor trying to create doctor (should be denied)
  console.log('Doctor trying to create doctor:');
  const doctorCreateResult = await apiCall('POST', '/doctors', newDoctor, tokens.doctor);
  console.log(!doctorCreateResult.success ? '✅ Doctor correctly denied doctor creation' : `❌ Doctor should not create doctors`);

  // Patient trying to create doctor (should be denied)
  console.log('Patient trying to create doctor:');
  const patientCreateResult = await apiCall('POST', '/doctors', newDoctor, tokens.patient);
  console.log(!patientCreateResult.success ? '✅ Patient correctly denied doctor creation' : `❌ Patient should not create doctors`);

  console.log('\n');
};

// Test prescription access
const testPrescriptionAccess = async () => {
  console.log('🧪 Testing Prescription Access Control...\n');

  // Admin accessing prescriptions (should work)
  console.log('Admin accessing prescriptions:');
  const adminResult = await apiCall('GET', '/prescriptions', null, tokens.admin);
  console.log(adminResult.success ? '✅ Admin can access prescriptions' : `❌ Admin denied: ${adminResult.error}`);

  // Doctor accessing prescriptions (should work - only their own)
  console.log('Doctor accessing prescriptions:');
  const doctorResult = await apiCall('GET', '/prescriptions', null, tokens.doctor);
  console.log(doctorResult.success ? '✅ Doctor can access prescriptions' : `❌ Doctor denied: ${doctorResult.error}`);

  // Patient accessing prescriptions (should work - only their own)
  console.log('Patient accessing prescriptions:');
  const patientResult = await apiCall('GET', '/prescriptions', null, tokens.patient);
  console.log(patientResult.success ? '✅ Patient can access own prescriptions' : `❌ Patient denied: ${patientResult.error}`);

  console.log('\n');
};

// Run all tests
const runTests = async () => {
  console.log('🚀 Starting Role-Based Access Control Tests\n');
  console.log('=' .repeat(50) + '\n');

  try {
    await setupTestUsers();
    await testPatientsAccess();
    await testPatientOwnDataAccess();
    await testDoctorManagement();
    await testPrescriptionAccess();

    console.log('=' .repeat(50));
    console.log('✅ All RBAC tests completed!');
    console.log('Check the results above to verify role-based access control is working correctly.');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
};

// Run tests if this file is executed directly
if (require.main === module) {
  runTests();
}

module.exports = { runTests, apiCall, testUsers };
