<div class="dashboard-container">
  <!-- Page Header -->
  <div class="mb-6">
    <h1 class="text-3xl font-bold text-gray-900">Dashboard</h1>
    <p class="text-gray-600 mt-1">Welcome back! Here's what's happening at your hospital today.</p>
  </div>

  <!-- Stats Cards -->
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    <!-- Total Patients -->
    <mat-card class="p-6">
      <div class="flex items-center justify-between">
        <div>
          <p class="text-sm font-medium text-gray-600">Total Patients</p>
          <p class="text-3xl font-bold text-gray-900">1,234</p>
          <p class="text-sm text-green-600">+12% from last month</p>
        </div>
        <div class="p-3 bg-blue-100 rounded-full">
          <mat-icon class="text-blue-600">people</mat-icon>
        </div>
      </div>
    </mat-card>

    <!-- Today's Appointments -->
    <mat-card class="p-6">
      <div class="flex items-center justify-between">
        <div>
          <p class="text-sm font-medium text-gray-600">Today's Appointments</p>
          <p class="text-3xl font-bold text-gray-900">48</p>
          <p class="text-sm text-green-600">+5% from yesterday</p>
        </div>
        <div class="p-3 bg-green-100 rounded-full">
          <mat-icon class="text-green-600">event</mat-icon>
        </div>
      </div>
    </mat-card>

    <!-- Active Doctors -->
    <mat-card class="p-6">
      <div class="flex items-center justify-between">
        <div>
          <p class="text-sm font-medium text-gray-600">Active Doctors</p>
          <p class="text-3xl font-bold text-gray-900">24</p>
          <p class="text-sm text-blue-600">All departments</p>
        </div>
        <div class="p-3 bg-purple-100 rounded-full">
          <mat-icon class="text-purple-600">medical_services</mat-icon>
        </div>
      </div>
    </mat-card>

    <!-- Revenue -->
    <mat-card class="p-6">
      <div class="flex items-center justify-between">
        <div>
          <p class="text-sm font-medium text-gray-600">Monthly Revenue</p>
          <p class="text-3xl font-bold text-gray-900">₹2.4L</p>
          <p class="text-sm text-green-600">+18% from last month</p>
        </div>
        <div class="p-3 bg-yellow-100 rounded-full">
          <mat-icon class="text-yellow-600">currency_rupee</mat-icon>
        </div>
      </div>
    </mat-card>
  </div>

  <!-- Quick Actions -->
  <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
    <!-- Recent Appointments -->
    <mat-card class="p-6">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-semibold text-gray-900">Recent Appointments</h3>
        <button mat-button color="primary" routerLink="/appointments">View All</button>
      </div>
      <div class="space-y-3">
        <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
          <div class="flex items-center space-x-3">
            <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
              <mat-icon class="text-blue-600 text-sm">person</mat-icon>
            </div>
            <div>
              <p class="font-medium text-gray-900">John Doe</p>
              <p class="text-sm text-gray-600">Dr. Smith - 10:00 AM</p>
            </div>
          </div>
          <span class="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">Confirmed</span>
        </div>
        <!-- More appointments... -->
      </div>
    </mat-card>

    <!-- Quick Actions -->
    <mat-card class="p-6">
      <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
      <div class="grid grid-cols-2 gap-4">
        <button mat-raised-button color="primary" routerLink="/patients/new" class="h-16">
          <div class="flex flex-col items-center">
            <mat-icon>person_add</mat-icon>
            <span class="text-sm mt-1">Add Patient</span>
          </div>
        </button>
        <button mat-raised-button color="accent" routerLink="/appointments/new" class="h-16">
          <div class="flex flex-col items-center">
            <mat-icon>event_available</mat-icon>
            <span class="text-sm mt-1">Book Appointment</span>
          </div>
        </button>
        <button mat-raised-button routerLink="/doctors" class="h-16">
          <div class="flex flex-col items-center">
            <mat-icon>medical_services</mat-icon>
            <span class="text-sm mt-1">View Doctors</span>
          </div>
        </button>
        <button mat-raised-button routerLink="/billing" class="h-16">
          <div class="flex flex-col items-center">
            <mat-icon>receipt</mat-icon>
            <span class="text-sm mt-1">Billing</span>
          </div>
        </button>
      </div>
    </mat-card>
  </div>
</div>
