@use 'sass:map';
@use 'sass:meta';
@use '../core/tokens/m2-utils';
@use '../core/theming/theming';
@use '../core/theming/inspection';
@use '../core/style/sass-utils';

// Tokens that can't be configured through Angular Material's current theming API,
// but may be in a future version of the theming API.
@function get-unthemable-tokens() {
  @return (
    button-toggle-focus-state-layer-opacity: 0.12,
    button-toggle-hover-state-layer-opacity: 0.04,
    button-toggle-legacy-focus-state-layer-opacity: 1,
    button-toggle-legacy-height: 36px,
    button-toggle-legacy-shape: 2px,
    button-toggle-shape: 4px,
  );
}

// Tokens that can be configured through Angular Material's color theming API.
@function get-color-tokens($theme) {
  $theme-divider-color: inspection.get-theme-color($theme, foreground, divider);

  // By default the theme usually has an rgba color for the dividers, which can
  // stack up with the background of a button toggle. This can cause the border
  // of a selected toggle to look different from an unselected one. We use a solid
  // color to ensure that the border always stays the same.
  $divider-color: if(
      meta.type-of($theme-divider-color) == color,
      theming.private-rgba-to-hex(
          $theme-divider-color, inspection.get-theme-color($theme, background, card)),
      $theme-divider-color);

  @return (
    button-toggle-background-color: inspection.get-theme-color($theme, background, card),
    button-toggle-disabled-selected-state-background-color:
        inspection.get-theme-color($theme, background, selected-disabled-button),
    button-toggle-disabled-selected-state-text-color:
        inspection.get-theme-color($theme, foreground, text),
    button-toggle-disabled-state-background-color:
        inspection.get-theme-color($theme, background, card),
    button-toggle-disabled-state-text-color:
        inspection.get-theme-color($theme, foreground, disabled-button),
    button-toggle-divider-color: $divider-color,
    button-toggle-legacy-disabled-selected-state-background-color:
        inspection.get-theme-color($theme, background, selected-disabled-button),
    button-toggle-legacy-disabled-state-background-color:
        inspection.get-theme-color($theme, background, disabled-button-toggle),
    button-toggle-legacy-disabled-state-text-color:
        inspection.get-theme-color($theme, foreground, disabled-button),
    button-toggle-legacy-selected-state-background-color:
        inspection.get-theme-color($theme, background, selected-button),
    button-toggle-legacy-selected-state-text-color:
        inspection.get-theme-color($theme, foreground, secondary-text),
    button-toggle-legacy-state-layer-color:
        inspection.get-theme-color($theme, background, focused-button),
    button-toggle-legacy-text-color: inspection.get-theme-color($theme, foreground, hint-text),
    button-toggle-selected-state-background-color:
        inspection.get-theme-color($theme, background, selected-button),
    button-toggle-selected-state-text-color: inspection.get-theme-color($theme, foreground, text),
    button-toggle-state-layer-color: sass-utils.safe-color-change(
        inspection.get-theme-color($theme, background, focused-button), $alpha: 1),
    button-toggle-text-color: inspection.get-theme-color($theme, foreground, text),
  );
}

// Tokens that can be configured through Angular Material's typography theming API.
@function get-typography-tokens($theme) {
  @return (
    button-toggle-label-text-font: inspection.get-theme-typography($theme, body-1, font-family),
    button-toggle-label-text-line-height:
        inspection.get-theme-typography($theme, body-1, line-height),
    button-toggle-label-text-size: inspection.get-theme-typography($theme, body-1, font-size),
    button-toggle-label-text-tracking:
        inspection.get-theme-typography($theme, body-1, letter-spacing),
    button-toggle-label-text-weight: inspection.get-theme-typography($theme, body-1, font-weight),
    button-toggle-legacy-label-text-font:
        inspection.get-theme-typography($theme, body-1, font-family),
    button-toggle-legacy-label-text-line-height:
        inspection.get-theme-typography($theme, body-1, line-height),
    button-toggle-legacy-label-text-size:
        inspection.get-theme-typography($theme, body-1, font-size),
    button-toggle-legacy-label-text-tracking:
        inspection.get-theme-typography($theme, body-1, letter-spacing),
    button-toggle-legacy-label-text-weight:
        inspection.get-theme-typography($theme, body-1, font-weight),
  );
}

// Tokens that can be configured through Angular Material's density theming API.
@function get-density-tokens($theme) {
  $density-scale: theming.clamp-density(inspection.get-theme-density($theme), -4);
  $size-scale: (
    0: 48px,
    -1: 44px,
    -2: 40px,
    -3: 36px,
    -4: 24px,
  );

  @return (
    button-toggle-height: map.get($size-scale, $density-scale)
  );
}

// Combines the tokens generated by the above functions into a single map with placeholder values.
// This is used to create token slots.
@function get-token-slots() {
  @return sass-utils.deep-merge-all(
      get-unthemable-tokens(),
      get-color-tokens(m2-utils.$placeholder-color-config),
      get-typography-tokens(m2-utils.$placeholder-typography-config),
      get-density-tokens(m2-utils.$placeholder-density-config)
  );
}
