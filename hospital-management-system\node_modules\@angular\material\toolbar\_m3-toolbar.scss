@use 'sass:map';
@use 'sass:list';
@use '../core/tokens/m3-utils';
@use '../core/theming/theming';
@use '../core/theming/inspection';
@use '../core/tokens/m3';

/// Generates custom tokens for the mat-toolbar.
/// @param {Map} $systems The MDC system tokens
/// @param {Boolean} $exclude-hardcoded Whether to exclude hardcoded token values
/// @param {Map} $token-slots Possible token slots
/// @return {Map} A set of custom tokens for the mat-toolbar
@function get-tokens($theme: m3.$sys-theme) {
  $system: m3-utils.get-system($theme);

  @return (
    base: (),
    color: (
      toolbar-container-background-color: map.get($system, surface),
      toolbar-container-text-color: map.get($system, on-surface),
    ),
    typography: (
      toolbar-title-text-font: map.get($system, title-large-font),
      toolbar-title-text-line-height: map.get($system, title-large-line-height),
      toolbar-title-text-size: map.get($system, title-large-size),
      toolbar-title-text-tracking: map.get($system, title-large-tracking),
      toolbar-title-text-weight: map.get($system, title-large-weight),
    ),
    density: get-density-tokens(map.get($theme, inspection.$internals, density-scale)),
  );
}

// Tokens that can be configured through Angular Material's density theming API.
@function get-density-tokens($scale) {
  $scale: theming.clamp-density($scale, -3);
  $index: ($scale * -1) + 1;

  @return (
    toolbar-standard-height: list.nth((64px, 60px, 56px, 52px), $index),
    toolbar-mobile-height: list.nth((56px, 52px, 48px, 44px), $index),
  );
}
