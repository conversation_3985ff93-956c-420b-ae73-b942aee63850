@use '../core/theming/theming';
@use '../core/theming/inspection';
@use '../core/typography/typography';
@use '../core/style/sass-utils';
@use '../core/tokens/token-utils';
@use './m2-stepper';
@use './m3-stepper';
@use 'sass:map';

/// Outputs base theme styles (styles not dependent on the color, typography, or density settings)
/// for mat-stepper.
/// @param {Map} $theme The theme to generate base styles for.
@mixin base($theme) {
  @if inspection.get-theme-version($theme) == 1 {
    @include token-utils.create-token-values(map.get(m3-stepper.get-tokens($theme), base));
  } @else {
  }
}

/// Outputs color theme styles for mat-stepper.
/// @param {Map} $theme The theme to generate color styles for.
/// @param {String} $color-variant The color variant to use for the component (M3 only)
@mixin color($theme, $color-variant: null) {
  @if inspection.get-theme-version($theme) == 1 {
    @include token-utils.create-token-values(
        map.get(m3-stepper.get-tokens($theme, $color-variant), color));
  } @else {
    @include sass-utils.current-selector-or-root() {
      @include token-utils.create-token-values-mixed(m2-stepper.get-color-tokens($theme));

      .mat-step-header.mat-accent {
        @include token-utils.create-token-values-mixed(
          m2-stepper.private-get-color-palette-color-tokens($theme, accent));
      }

      .mat-step-header.mat-warn {
        @include token-utils.create-token-values-mixed(
          m2-stepper.private-get-color-palette-color-tokens($theme, warn));
      }
    }
  }
}

/// Outputs typography theme styles for mat-stepper.
/// @param {Map} $theme The theme to generate typography styles for.
@mixin typography($theme) {
  @if inspection.get-theme-version($theme) == 1 {
    @include token-utils.create-token-values(map.get(m3-stepper.get-tokens($theme), typography));
  } @else {
    @include sass-utils.current-selector-or-root() {
      @include token-utils.create-token-values-mixed(m2-stepper.get-typography-tokens($theme));
    }
  }
}

/// Outputs density theme styles for mat-stepper.
/// @param {Map} $theme The theme to generate density styles for.
@mixin density($theme) {
  @if inspection.get-theme-version($theme) == 1 {
    @include token-utils.create-token-values(map.get(m3-stepper.get-tokens($theme), density));
  } @else {
    @include sass-utils.current-selector-or-root() {
      @include token-utils.create-token-values-mixed(m2-stepper.get-density-tokens($theme));
    }
  }
}

/// Defines the tokens that will be available in the `overrides` mixin and for docs extraction.
@function _define-overrides() {
  @return (
    (
      namespace: stepper,
      tokens: token-utils.get-overrides(m3-stepper.get-tokens(), stepper)
    ),
  );
}

/// Outputs the CSS variable values for the given tokens.
/// @param {Map} $tokens The token values to emit.
@mixin overrides($tokens: ()) {
    @include token-utils.batch-create-token-values($tokens, _define-overrides());
}

/// Outputs all (base, color, typography, and density) theme styles for mat-stepper.
/// @param {Map} $theme The theme to generate color styles for.
/// @param {String} $color-variant The color variant to use for the component (M3 only)
@mixin theme($theme, $color-variant: null) {
  @include theming.private-check-duplicate-theme-styles($theme, 'mat-stepper') {
    @if inspection.get-theme-version($theme) == 1 {
      @include base($theme);
      @include color($theme, $color-variant);
      @include density($theme);
      @include typography($theme);
      @if inspection.theme-has($theme, density) {
        @include density($theme);
      }
    } @else {
      @include base($theme);
      @if inspection.theme-has($theme, color) {
        @include color($theme);
      }
      @if inspection.theme-has($theme, density) {
        @include density($theme);
      }
      @if inspection.theme-has($theme, typography) {
        @include typography($theme);
      }
    }
  }
}
