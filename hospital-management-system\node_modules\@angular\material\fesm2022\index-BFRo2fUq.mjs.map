{"version": 3, "file": "index-BFRo2fUq.mjs", "sources": ["../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/material/core/ripple/index.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {NgModule} from '@angular/core';\nimport {MatCommonModule} from '../common-behaviors/common-module';\nimport {MatRipple} from './ripple';\n\nexport * from './ripple';\nexport * from './ripple-ref';\nexport {RippleRenderer, RippleTarget, defaultRippleAnimationConfig} from './ripple-renderer';\n\n@NgModule({\n  imports: [MatCommonModule, MatRipple],\n  exports: [MatR<PERSON>ple, MatCommonModule],\n})\nexport class MatRippleModule {}\n"], "names": [], "mappings": ";;;;;MAoBa,eAAe,CAAA;uGAAf,eAAe,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA;AAAf,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,eAAe,YAHhB,eAAe,EAAE,SAAS,CAC1B,EAAA,OAAA,EAAA,CAAA,SAAS,EAAE,eAAe,CAAA,EAAA,CAAA;wGAEzB,eAAe,EAAA,OAAA,EAAA,CAHhB,eAAe,EACJ,eAAe,CAAA,EAAA,CAAA;;2FAEzB,eAAe,EAAA,UAAA,EAAA,CAAA;kBAJ3B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACR,oBAAA,OAAO,EAAE,CAAC,eAAe,EAAE,SAAS,CAAC;AACrC,oBAAA,OAAO,EAAE,CAAC,SAAS,EAAE,eAAe,CAAC;AACtC,iBAAA;;;;;"}