const mongoose = require('mongoose');
const bcrypt = require('bcrypt');

const userSchema = new mongoose.Schema({
  username: {
    type: String,
    required: true,
    unique: true,
    trim: true
  },
  email: {
    type: String,
    required: true,
    unique: true,
    lowercase: true
  },
  password: {
    type: String,
    required: true,
    minlength: 6
  },
  role: {
    type: String,
    enum: ['Admin', 'Doctor', 'Patient'],
    required: true
  },
  permissions: [{
    type: String,
    enum: [
      // Admin permissions
      'manage_users', 'manage_doctors', 'manage_patients', 'manage_appointments',
      'manage_reports', 'manage_prescriptions', 'view_all_data', 'manage_system',
      // Doctor permissions
      'view_patients', 'view_appointments', 'create_prescriptions', 'create_reports',
      'view_medical_history', 'update_appointments',
      // Patient permissions
      'view_own_appointments', 'view_own_prescriptions', 'view_own_reports',
      'update_own_profile'
    ]
  }],
  firstName: {
    type: String,
    required: true,
    trim: true
  },
  lastName: {
    type: String,
    required: true,
    trim: true
  },
  phone: {
    type: String,
    required: true
  },
  isActive: {
    type: Boolean,
    default: true
  },
  lastLogin: {
    type: Date
  },
  profilePicture: {
    type: String
  }
}, {
  timestamps: true
});

// Assign role-based permissions
const assignRolePermissions = (role) => {
  const rolePermissions = {
    'Admin': [
      'manage_users', 'manage_doctors', 'manage_patients', 'manage_appointments',
      'manage_reports', 'manage_prescriptions', 'view_all_data', 'manage_system'
    ],
    'Doctor': [
      'view_patients', 'view_appointments', 'create_prescriptions', 'create_reports',
      'view_medical_history', 'update_appointments'
    ],
    'Patient': [
      'view_own_appointments', 'view_own_prescriptions', 'view_own_reports',
      'update_own_profile'
    ]
  };
  return rolePermissions[role] || [];
};

// Hash password and assign permissions before saving
userSchema.pre('save', async function(next) {
  // Assign permissions based on role
  if (this.isModified('role') || this.isNew) {
    this.permissions = assignRolePermissions(this.role);
  }

  // Hash password if modified
  if (!this.isModified('password')) return next();

  try {
    const salt = await bcrypt.genSalt(10);
    this.password = await bcrypt.hash(this.password, salt);
    next();
  } catch (error) {
    next(error);
  }
});

// Compare password method
userSchema.methods.comparePassword = async function(candidatePassword) {
  return bcrypt.compare(candidatePassword, this.password);
};

// Check if user has specific permission
userSchema.methods.hasPermission = function(permission) {
  return this.permissions.includes(permission);
};

// Check if user has any of the specified permissions
userSchema.methods.hasAnyPermission = function(permissions) {
  return permissions.some(permission => this.permissions.includes(permission));
};

// Check if user can access resource based on ownership
userSchema.methods.canAccessResource = function(resourceOwnerId, requiredPermission) {
  // Admin can access everything
  if (this.role === 'Admin') return true;

  // Check if user has the required permission
  if (!this.hasPermission(requiredPermission)) return false;

  // For patients, they can only access their own resources
  if (this.role === 'Patient') {
    return this._id.toString() === resourceOwnerId.toString();
  }

  // Doctors can access based on their permissions
  return true;
};

// Remove password from JSON output
userSchema.methods.toJSON = function() {
  const userObject = this.toObject();
  delete userObject.password;
  return userObject;
};

module.exports = mongoose.model('User', userSchema);
