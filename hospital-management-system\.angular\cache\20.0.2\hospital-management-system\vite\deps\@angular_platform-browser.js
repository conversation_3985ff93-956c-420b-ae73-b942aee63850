import {
  By,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  HAMMER_GESTURE_CONFIG,
  HAMMER_LOADER,
  HammerGestureConfig,
  HammerGesturesPlugin,
  HammerModule,
  HydrationFeatureKind,
  Meta,
  Title,
  VERSION,
  disableDebugTools,
  enableDebugTools,
  provideClientHydration,
  withEventReplay,
  withHttpTransferCacheOptions,
  withI18nSupport,
  withIncrementalHydration,
  withNoHttpTransferCache
} from "./chunk-3KVLLFQL.js";
import {
  BrowserDomAdapter,
  BrowserGetTestability,
  BrowserModule,
  DomEventsPlugin,
  DomRendererFactory2,
  EVENT_MANAGER_PLUGINS,
  EventManager,
  EventManagerPlugin,
  KeyEventsPlugin,
  REMOVE_STYLES_ON_COMPONENT_DESTROY,
  SharedStylesHost,
  bootstrapApplication,
  createApplication,
  platformBrowser,
  provideProtractorTestingSupport
} from "./chunk-TPKXPHM5.js";
import "./chunk-H4CBVWJR.js";
import {
  getDOM
} from "./chunk-B7UG7IOS.js";
import "./chunk-KQRM5WX6.js";
import "./chunk-2FKQUGRE.js";
import "./chunk-Z3F4H3BY.js";
import "./chunk-6PNJZOQ4.js";
import "./chunk-ZG272CAW.js";
export {
  BrowserModule,
  By,
  DomSanitizer,
  EVENT_MANAGER_PLUGINS,
  EventManager,
  EventManagerPlugin,
  HAMMER_GESTURE_CONFIG,
  HAMMER_LOADER,
  HammerGestureConfig,
  HammerModule,
  HydrationFeatureKind,
  Meta,
  REMOVE_STYLES_ON_COMPONENT_DESTROY,
  Title,
  VERSION,
  bootstrapApplication,
  createApplication,
  disableDebugTools,
  enableDebugTools,
  platformBrowser,
  provideClientHydration,
  provideProtractorTestingSupport,
  withEventReplay,
  withHttpTransferCacheOptions,
  withI18nSupport,
  withIncrementalHydration,
  withNoHttpTransferCache,
  BrowserDomAdapter as ɵBrowserDomAdapter,
  BrowserGetTestability as ɵBrowserGetTestability,
  DomEventsPlugin as ɵDomEventsPlugin,
  DomRendererFactory2 as ɵDomRendererFactory2,
  DomSanitizerImpl as ɵDomSanitizerImpl,
  HammerGesturesPlugin as ɵHammerGesturesPlugin,
  KeyEventsPlugin as ɵKeyEventsPlugin,
  SharedStylesHost as ɵSharedStylesHost,
  getDOM as ɵgetDOM
};
