{"version": 3, "file": "testing.mjs", "sources": ["../../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/material/icon/testing/icon-harness-filters.ts", "../../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/material/icon/testing/icon-harness.ts", "../../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/material/icon/testing/fake-icon-registry.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {BaseHarnessFilters} from '@angular/cdk/testing';\n\n/** Possible types of icons. */\nexport enum IconType {\n  SVG,\n  FONT,\n}\n\n/** A set of criteria that can be used to filter a list of `MatIconHarness` instances. */\nexport interface IconHarnessFilters extends BaseHarnessFilters {\n  /** Filters based on the typef of the icon. */\n  type?: IconType;\n  /** Filters based on the name of the icon. */\n  name?: string | RegExp;\n  /** Filters based on the namespace of the icon. */\n  namespace?: string | null | RegExp;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {ComponentHarness, HarnessPredicate} from '@angular/cdk/testing';\nimport {IconHarnessFilters, IconType} from './icon-harness-filters';\n\n/** Harness for interacting with a standard mat-icon in tests. */\nexport class MatIconHarness extends ComponentHarness {\n  /** The selector for the host element of a `MatIcon` instance. */\n  static hostSelector = '.mat-icon';\n\n  /**\n   * Gets a `HarnessPredicate` that can be used to search for a `MatIconHarness` that meets\n   * certain criteria.\n   * @param options Options for filtering which icon instances are considered a match.\n   * @return a `HarnessPredicate` configured with the given options.\n   */\n  static with(options: IconHarnessFilters = {}): HarnessPredicate<MatIconHarness> {\n    return new HarnessPredicate(MatIconHarness, options)\n      .addOption('type', options.type, async (harness, type) => (await harness.getType()) === type)\n      .addOption('name', options.name, (harness, text) =>\n        HarnessPredicate.stringMatches(harness.getName(), text),\n      )\n      .addOption('namespace', options.namespace, (harness, text) =>\n        HarnessPredicate.stringMatches(harness.getNamespace(), text),\n      );\n  }\n\n  /** Gets the type of the icon. */\n  async getType(): Promise<IconType> {\n    const type = await (await this.host()).getAttribute('data-mat-icon-type');\n    return type === 'svg' ? IconType.SVG : IconType.FONT;\n  }\n\n  /** Gets the name of the icon. */\n  async getName(): Promise<string | null> {\n    const host = await this.host();\n    const nameFromDom = await host.getAttribute('data-mat-icon-name');\n\n    // If we managed to figure out the name from the attribute, use it.\n    if (nameFromDom) {\n      return nameFromDom;\n    }\n\n    // Some icons support defining the icon as a ligature.\n    // As a fallback, try to extract it from the DOM text.\n    if ((await this.getType()) === IconType.FONT) {\n      // Other directives may add content to the icon (e.g. `MatBadge`), however only the direct\n      // text nodes affect the name of the icon. Exclude all element descendants from the result.\n      const text = await host.text({exclude: '*'});\n\n      // There are some internal cases where the icon name is wrapped in another node.\n      // Fall back to extracting the entire text if we ended up excluding everything above.\n      return text.length > 0 ? text : host.text();\n    }\n\n    return null;\n  }\n\n  /** Gets the namespace of the icon. */\n  async getNamespace(): Promise<string | null> {\n    return (await this.host()).getAttribute('data-mat-icon-namespace');\n  }\n\n  /** Gets whether the icon is inline. */\n  async isInline(): Promise<boolean> {\n    return (await this.host()).hasClass('mat-icon-inline');\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Injectable, NgModule, OnDestroy} from '@angular/core';\nimport {MatIconRegistry} from '../../icon';\nimport {Observable, of as observableOf} from 'rxjs';\n\ntype PublicApi<T> = {\n  [K in keyof T]: T[K] extends (...x: any[]) => T ? (...x: any[]) => PublicApi<T> : T[K];\n};\n\n/**\n * A null icon registry that must be imported to allow disabling of custom\n * icons.\n */\n@Injectable()\nexport class FakeMatIconRegistry implements PublicApi<MatIconRegistry>, OnDestroy {\n  addSvgIcon(): this {\n    return this;\n  }\n\n  addSvgIconLiteral(): this {\n    return this;\n  }\n\n  addSvgIconInNamespace(): this {\n    return this;\n  }\n\n  addSvgIconLiteralInNamespace(): this {\n    return this;\n  }\n\n  addSvgIconSet(): this {\n    return this;\n  }\n\n  addSvgIconSetLiteral(): this {\n    return this;\n  }\n\n  addSvgIconSetInNamespace(): this {\n    return this;\n  }\n\n  addSvgIconSetLiteralInNamespace(): this {\n    return this;\n  }\n\n  registerFontClassAlias(): this {\n    return this;\n  }\n\n  classNameForFontAlias(alias: string): string {\n    return alias;\n  }\n\n  getDefaultFontSetClass() {\n    return ['material-icons'];\n  }\n\n  getSvgIconFromUrl(): Observable<SVGElement> {\n    return observableOf(this._generateEmptySvg());\n  }\n\n  getNamedSvgIcon(): Observable<SVGElement> {\n    return observableOf(this._generateEmptySvg());\n  }\n\n  setDefaultFontSetClass(): this {\n    return this;\n  }\n\n  addSvgIconResolver(): this {\n    return this;\n  }\n\n  ngOnDestroy() {}\n\n  private _generateEmptySvg(): SVGElement {\n    const emptySvg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');\n    emptySvg.classList.add('fake-testing-svg');\n    // Emulate real icon characteristics from `MatIconRegistry` so size remains consistent in tests.\n    emptySvg.setAttribute('fit', '');\n    emptySvg.setAttribute('height', '100%');\n    emptySvg.setAttribute('width', '100%');\n    emptySvg.setAttribute('preserveAspectRatio', 'xMidYMid meet');\n    emptySvg.setAttribute('focusable', 'false');\n    return emptySvg;\n  }\n}\n\n/** Import this module in tests to install the null icon registry. */\n@NgModule({\n  providers: [{provide: MatIconRegistry, useClass: FakeMatIconRegistry}],\n})\nexport class MatIconTestingModule {}\n"], "names": ["observableOf"], "mappings": ";;;;;;;;;AAUA;IACY;AAAZ,CAAA,UAAY,QAAQ,EAAA;AAClB,IAAA,QAAA,CAAA,QAAA,CAAA,KAAA,CAAA,GAAA,CAAA,CAAA,GAAA,KAAG;AACH,IAAA,QAAA,CAAA,QAAA,CAAA,MAAA,CAAA,GAAA,CAAA,CAAA,GAAA,MAAI;AACN,CAAC,EAHW,QAAQ,KAAR,QAAQ,GAGnB,EAAA,CAAA,CAAA;;ACHD;AACM,MAAO,cAAe,SAAQ,gBAAgB,CAAA;;AAElD,IAAA,OAAO,YAAY,GAAG,WAAW;AAEjC;;;;;AAKG;AACH,IAAA,OAAO,IAAI,CAAC,OAAA,GAA8B,EAAE,EAAA;AAC1C,QAAA,OAAO,IAAI,gBAAgB,CAAC,cAAc,EAAE,OAAO;aAChD,SAAS,CAAC,MAAM,EAAE,OAAO,CAAC,IAAI,EAAE,OAAO,OAAO,EAAE,IAAI,KAAK,CAAC,MAAM,OAAO,CAAC,OAAO,EAAE,MAAM,IAAI;aAC3F,SAAS,CAAC,MAAM,EAAE,OAAO,CAAC,IAAI,EAAE,CAAC,OAAO,EAAE,IAAI,KAC7C,gBAAgB,CAAC,aAAa,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,IAAI,CAAC;aAExD,SAAS,CAAC,WAAW,EAAE,OAAO,CAAC,SAAS,EAAE,CAAC,OAAO,EAAE,IAAI,KACvD,gBAAgB,CAAC,aAAa,CAAC,OAAO,CAAC,YAAY,EAAE,EAAE,IAAI,CAAC,CAC7D;;;AAIL,IAAA,MAAM,OAAO,GAAA;AACX,QAAA,MAAM,IAAI,GAAG,MAAM,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,YAAY,CAAC,oBAAoB,CAAC;AACzE,QAAA,OAAO,IAAI,KAAK,KAAK,GAAG,QAAQ,CAAC,GAAG,GAAG,QAAQ,CAAC,IAAI;;;AAItD,IAAA,MAAM,OAAO,GAAA;AACX,QAAA,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE;QAC9B,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,oBAAoB,CAAC;;QAGjE,IAAI,WAAW,EAAE;AACf,YAAA,OAAO,WAAW;;;;AAKpB,QAAA,IAAI,CAAC,MAAM,IAAI,CAAC,OAAO,EAAE,MAAM,QAAQ,CAAC,IAAI,EAAE;;;AAG5C,YAAA,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,EAAC,OAAO,EAAE,GAAG,EAAC,CAAC;;;AAI5C,YAAA,OAAO,IAAI,CAAC,MAAM,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC,IAAI,EAAE;;AAG7C,QAAA,OAAO,IAAI;;;AAIb,IAAA,MAAM,YAAY,GAAA;AAChB,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,YAAY,CAAC,yBAAyB,CAAC;;;AAIpE,IAAA,MAAM,QAAQ,GAAA;AACZ,QAAA,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,QAAQ,CAAC,iBAAiB,CAAC;;;;ACvD1D;;;AAGG;MAEU,mBAAmB,CAAA;IAC9B,UAAU,GAAA;AACR,QAAA,OAAO,IAAI;;IAGb,iBAAiB,GAAA;AACf,QAAA,OAAO,IAAI;;IAGb,qBAAqB,GAAA;AACnB,QAAA,OAAO,IAAI;;IAGb,4BAA4B,GAAA;AAC1B,QAAA,OAAO,IAAI;;IAGb,aAAa,GAAA;AACX,QAAA,OAAO,IAAI;;IAGb,oBAAoB,GAAA;AAClB,QAAA,OAAO,IAAI;;IAGb,wBAAwB,GAAA;AACtB,QAAA,OAAO,IAAI;;IAGb,+BAA+B,GAAA;AAC7B,QAAA,OAAO,IAAI;;IAGb,sBAAsB,GAAA;AACpB,QAAA,OAAO,IAAI;;AAGb,IAAA,qBAAqB,CAAC,KAAa,EAAA;AACjC,QAAA,OAAO,KAAK;;IAGd,sBAAsB,GAAA;QACpB,OAAO,CAAC,gBAAgB,CAAC;;IAG3B,iBAAiB,GAAA;AACf,QAAA,OAAOA,EAAY,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC;;IAG/C,eAAe,GAAA;AACb,QAAA,OAAOA,EAAY,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC;;IAG/C,sBAAsB,GAAA;AACpB,QAAA,OAAO,IAAI;;IAGb,kBAAkB,GAAA;AAChB,QAAA,OAAO,IAAI;;AAGb,IAAA,WAAW;IAEH,iBAAiB,GAAA;QACvB,MAAM,QAAQ,GAAG,QAAQ,CAAC,eAAe,CAAC,4BAA4B,EAAE,KAAK,CAAC;AAC9E,QAAA,QAAQ,CAAC,SAAS,CAAC,GAAG,CAAC,kBAAkB,CAAC;;AAE1C,QAAA,QAAQ,CAAC,YAAY,CAAC,KAAK,EAAE,EAAE,CAAC;AAChC,QAAA,QAAQ,CAAC,YAAY,CAAC,QAAQ,EAAE,MAAM,CAAC;AACvC,QAAA,QAAQ,CAAC,YAAY,CAAC,OAAO,EAAE,MAAM,CAAC;AACtC,QAAA,QAAQ,CAAC,YAAY,CAAC,qBAAqB,EAAE,eAAe,CAAC;AAC7D,QAAA,QAAQ,CAAC,YAAY,CAAC,WAAW,EAAE,OAAO,CAAC;AAC3C,QAAA,OAAO,QAAQ;;uGAxEN,mBAAmB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA;2GAAnB,mBAAmB,EAAA,CAAA;;2FAAnB,mBAAmB,EAAA,UAAA,EAAA,CAAA;kBAD/B;;AA6ED;MAIa,oBAAoB,CAAA;uGAApB,oBAAoB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA;wGAApB,oBAAoB,EAAA,CAAA;wGAApB,oBAAoB,EAAA,SAAA,EAFpB,CAAC,EAAC,OAAO,EAAE,eAAe,EAAE,QAAQ,EAAE,mBAAmB,EAAC,CAAC,EAAA,CAAA;;2FAE3D,oBAAoB,EAAA,UAAA,EAAA,CAAA;kBAHhC,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACR,SAAS,EAAE,CAAC,EAAC,OAAO,EAAE,eAAe,EAAE,QAAQ,EAAE,mBAAmB,EAAC,CAAC;AACvE,iBAAA;;;;;"}