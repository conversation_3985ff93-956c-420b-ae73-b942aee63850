import { Component, OnInit } from '@angular/core';
import { PatientService, Patient } from '../../core/services/patient';
import { Auth } from '../../core/services/auth';

@Component({
  selector: 'app-patient-list',
  standalone: false,
  templateUrl: './patient-list.html',
  styleUrl: './patient-list.css'
})
export class PatientList implements OnInit {
  patients: Patient[] = [];
  loading = false;
  error = '';
  currentPage = 1;
  totalPages = 1;
  totalPatients = 0;
  displayedColumns: string[] = ['patientId', 'name', 'contact', 'demographics', 'status', 'actions'];

  constructor(
    private patientService: PatientService,
    private authService: Auth
  ) {}

  ngOnInit(): void {
    this.loadPatients();
  }

  loadPatients(): void {
    this.loading = true;
    this.error = '';

    this.patientService.getPatients(this.currentPage, 10).subscribe({
      next: (response) => {
        this.patients = response.patients || [];
        this.totalPages = response.totalPages || 1;
        this.totalPatients = response.total || 0;
        this.loading = false;
      },
      error: (error) => {
        this.error = 'Failed to load patients. Please try again.';
        this.loading = false;
        console.error('Error loading patients:', error);
      }
    });
  }

  onPageChange(page: number): void {
    this.currentPage = page;
    this.loadPatients();
  }

  canManagePatients(): boolean {
    return this.authService.hasAnyPermission(['manage_patients']);
  }

  getAge(dateOfBirth: string | Date): number | null {
    if (!dateOfBirth) return null;

    const today = new Date();
    const birthDate = new Date(dateOfBirth);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();

    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }

    return age;
  }
}
