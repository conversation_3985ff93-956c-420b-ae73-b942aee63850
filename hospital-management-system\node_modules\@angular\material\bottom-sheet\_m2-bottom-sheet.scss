@use '../core/tokens/m2-utils';
@use '../core/theming/inspection';
@use '../core/style/sass-utils';

// Tokens that can't be configured through Angular Material's current theming API,
// but may be in a future version of the theming API.
@function get-unthemable-tokens() {
  @return (
    // TODO: will be necessary for M3.
    bottom-sheet-container-shape: 4px,
  );
}

// Tokens that can be configured through Angular Material's color theming API.
@function get-color-tokens($theme) {
  @return (
    bottom-sheet-container-text-color: inspection.get-theme-color($theme, foreground, text),
    bottom-sheet-container-background-color: inspection.get-theme-color($theme, background, dialog),
  );
}

// Tokens that can be configured through Angular Material's typography theming API.
@function get-typography-tokens($theme) {
  @return (
    bottom-sheet-container-text-font: inspection.get-theme-typography($theme, body-2, font-family),
    bottom-sheet-container-text-line-height:
        inspection.get-theme-typography($theme, body-2, line-height),
    bottom-sheet-container-text-size: inspection.get-theme-typography($theme, body-2, font-size),
    bottom-sheet-container-text-tracking:
        inspection.get-theme-typography($theme, body-2, letter-spacing),
    bottom-sheet-container-text-weight:
        inspection.get-theme-typography($theme, body-2, font-weight),
  );
}

// Tokens that can be configured through Angular Material's density theming API.
@function get-density-tokens($theme) {
  @return ();
}

// Combines the tokens generated by the above functions into a single map with placeholder values.
// This is used to create token slots.
@function get-token-slots() {
  @return sass-utils.deep-merge-all(
      get-unthemable-tokens(),
      get-color-tokens(m2-utils.$placeholder-color-config),
      get-typography-tokens(m2-utils.$placeholder-typography-config),
      get-density-tokens(m2-utils.$placeholder-density-config)
  );
}
