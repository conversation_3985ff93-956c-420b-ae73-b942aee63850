import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';

export interface Patient {
  _id?: string;
  patientId?: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  dateOfBirth: Date;
  gender: 'Male' | 'Female' | 'Other';
  address: {
    street?: string;
    city?: string;
    state?: string;
    zipCode?: string;
    country?: string;
  };
  emergencyContact?: {
    name?: string;
    relationship?: string;
    phone?: string;
  };
  medicalHistory?: Array<{
    condition: string;
    diagnosedDate: Date;
    status: 'Active' | 'Resolved' | 'Chronic';
  }>;
  allergies?: string[];
  bloodGroup?: 'A+' | 'A-' | 'B+' | 'B-' | 'AB+' | 'AB-' | 'O+' | 'O-';
  isActive?: boolean;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface PatientListResponse {
  patients: Patient[];
  totalPages: number;
  currentPage: number;
  total: number;
}

@Injectable({
  providedIn: 'root'
})
export class PatientService {
  private apiUrl = 'http://localhost:3000/api/patients';

  constructor(private http: HttpClient) { }

  getPatients(page: number = 1, limit: number = 10, search: string = ''): Observable<PatientListResponse> {
    let params = new HttpParams()
      .set('page', page.toString())
      .set('limit', limit.toString());

    if (search) {
      params = params.set('search', search);
    }

    return this.http.get<PatientListResponse>(this.apiUrl, { params });
  }

  getPatientById(id: string): Observable<Patient> {
    return this.http.get<Patient>(`${this.apiUrl}/${id}`);
  }

  createPatient(patient: Patient): Observable<{ message: string; patient: Patient }> {
    return this.http.post<{ message: string; patient: Patient }>(this.apiUrl, patient);
  }

  updatePatient(id: string, patient: Partial<Patient>): Observable<{ message: string; patient: Patient }> {
    return this.http.put<{ message: string; patient: Patient }>(`${this.apiUrl}/${id}`, patient);
  }

  deletePatient(id: string): Observable<{ message: string }> {
    return this.http.delete<{ message: string }>(`${this.apiUrl}/${id}`);
  }

  getPatientMedicalHistory(id: string): Observable<Patient['medicalHistory']> {
    return this.http.get<Patient['medicalHistory']>(`${this.apiUrl}/${id}/medical-history`);
  }

  addMedicalHistory(id: string, history: { condition: string; diagnosedDate: Date; status: 'Active' | 'Resolved' | 'Chronic' }): Observable<{ message: string; medicalHistory: Patient['medicalHistory'] }> {
    return this.http.post<{ message: string; medicalHistory: Patient['medicalHistory'] }>(`${this.apiUrl}/${id}/medical-history`, history);
  }
}
