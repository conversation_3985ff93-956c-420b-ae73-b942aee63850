{"version": 3, "file": "pseudo-checkbox-module-4F8Up4PL.mjs", "sources": ["../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/material/core/selection/pseudo-checkbox/pseudo-checkbox-module.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {NgModule} from '@angular/core';\nimport {MatPseudoCheckbox} from './pseudo-checkbox';\nimport {MatCommonModule} from '../../common-behaviors/common-module';\n\n@NgModule({\n  imports: [MatCommonModule, MatPseudoCheckbox],\n  exports: [MatPseudoCheckbox],\n})\nexport class MatPseudoCheckboxModule {}\n"], "names": [], "mappings": ";;;;;MAgBa,uBAAuB,CAAA;uGAAvB,uBAAuB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA;AAAvB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,uBAAuB,EAHxB,OAAA,EAAA,CAAA,eAAe,EAAE,iBAAiB,aAClC,iBAAiB,CAAA,EAAA,CAAA;AAEhB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,uBAAuB,YAHxB,eAAe,CAAA,EAAA,CAAA;;2FAGd,uBAAuB,EAAA,UAAA,EAAA,CAAA;kBAJnC,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACR,oBAAA,OAAO,EAAE,CAAC,eAAe,EAAE,iBAAiB,CAAC;oBAC7C,OAAO,EAAE,CAAC,iBAAiB,CAAC;AAC7B,iBAAA;;;;;"}