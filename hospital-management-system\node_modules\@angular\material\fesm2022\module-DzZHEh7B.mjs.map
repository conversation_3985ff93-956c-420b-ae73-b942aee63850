{"version": 3, "file": "module-DzZHEh7B.mjs", "sources": ["../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/material/form-field/module.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {ObserversModule} from '@angular/cdk/observers';\nimport {NgModule} from '@angular/core';\nimport {MatCommonModule} from '../core';\nimport {MatError} from './directives/error';\nimport {MatHint} from './directives/hint';\nimport {MatLabel} from './directives/label';\nimport {MatPrefix} from './directives/prefix';\nimport {MatSuffix} from './directives/suffix';\nimport {MatFormField} from './form-field';\n\n@NgModule({\n  imports: [\n    MatCommonModule,\n    ObserversModule,\n    MatFormField,\n    MatLabel,\n    MatError,\n    MatHint,\n    MatPrefix,\n    MatSuffix,\n  ],\n  exports: [MatFormField, MatLabel, MatHint, MatError, MatPrefix, MatSuffix, MatCommonModule],\n})\nexport class MatFormFieldModule {}\n"], "names": [], "mappings": ";;;;;;MA+Ba,kBAAkB,CAAA;uGAAlB,kBAAkB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA;AAAlB,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,kBAAkB,YAX3B,eAAe;YACf,eAAe;YACf,YAAY;YACZ,QAAQ;YACR,QAAQ;YACR,OAAO;YACP,SAAS;AACT,YAAA,SAAS,CAED,EAAA,OAAA,EAAA,CAAA,YAAY,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,eAAe,CAAA,EAAA,CAAA;AAE/E,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,kBAAkB,YAX3B,eAAe;YACf,eAAe;AACf,YAAA,YAAY,EAO6D,eAAe,CAAA,EAAA,CAAA;;2FAE/E,kBAAkB,EAAA,UAAA,EAAA,CAAA;kBAb9B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACR,oBAAA,OAAO,EAAE;wBACP,eAAe;wBACf,eAAe;wBACf,YAAY;wBACZ,QAAQ;wBACR,QAAQ;wBACR,OAAO;wBACP,SAAS;wBACT,SAAS;AACV,qBAAA;AACD,oBAAA,OAAO,EAAE,CAAC,YAAY,EAAE,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,eAAe,CAAC;AAC5F,iBAAA;;;;;"}