<div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
  <div class="card w-full max-w-md p-8">
    <!-- Header -->
    <div class="text-center mb-8">
      <div class="mx-auto w-16 h-16 bg-primary-600 rounded-full flex items-center justify-center mb-4">
        <mat-icon class="text-white text-3xl">local_hospital</mat-icon>
      </div>
      <h1 class="text-2xl font-bold text-gray-900 mb-2">Hospital Management System</h1>
      <p class="text-gray-600">Sign in to your account</p>
    </div>

    <!-- Login Form -->
    <form [formGroup]="loginForm" (ngSubmit)="onSubmit()" class="space-y-6">
      <!-- Email Field -->
      <mat-form-field appearance="outline" class="w-full">
        <mat-label>Email Address</mat-label>
        <input
          matInput
          type="email"
          formControlName="email"
          placeholder="Enter your email"
          autocomplete="email"
        >
        <mat-icon matSuffix>email</mat-icon>
        <mat-error *ngIf="email?.hasError('required')">
          Email is required
        </mat-error>
        <mat-error *ngIf="email?.hasError('email')">
          Please enter a valid email
        </mat-error>
      </mat-form-field>

      <!-- Password Field -->
      <mat-form-field appearance="outline" class="w-full">
        <mat-label>Password</mat-label>
        <input
          matInput
          [type]="hidePassword ? 'password' : 'text'"
          formControlName="password"
          placeholder="Enter your password"
          autocomplete="current-password"
        >
        <button
          mat-icon-button
          matSuffix
          type="button"
          (click)="hidePassword = !hidePassword"
          [attr.aria-label]="'Hide password'"
          [attr.aria-pressed]="hidePassword"
        >
          <mat-icon>{{hidePassword ? 'visibility_off' : 'visibility'}}</mat-icon>
        </button>
        <mat-error *ngIf="password?.hasError('required')">
          Password is required
        </mat-error>
        <mat-error *ngIf="password?.hasError('minlength')">
          Password must be at least 6 characters
        </mat-error>
      </mat-form-field>

      <!-- Error Message -->
      <div *ngIf="error" class="bg-red-50 border border-red-200 rounded-lg p-3">
        <div class="flex items-center">
          <mat-icon class="text-red-500 mr-2">error</mat-icon>
          <span class="text-red-700 text-sm">{{ error }}</span>
        </div>
      </div>

      <!-- Submit Button -->
      <button
        mat-raised-button
        color="primary"
        type="submit"
        class="w-full h-12 text-base font-medium"
        [disabled]="loginForm.invalid || loading"
      >
        <mat-icon *ngIf="loading" class="animate-spin mr-2">refresh</mat-icon>
        <span *ngIf="loading">Signing in...</span>
        <span *ngIf="!loading">Sign In</span>
      </button>
    </form>

    <!-- Footer -->
    <div class="mt-6 text-center">
      <p class="text-gray-600">
        Don't have an account?
        <a routerLink="/auth/register" class="text-primary-600 hover:text-primary-700 font-medium">
          Register here
        </a>
      </p>
    </div>
  </div>
</div>
