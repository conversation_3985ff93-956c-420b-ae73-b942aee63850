<div class="login-container">
  <div class="login-card">
    <div class="login-header">
      <h2>Hospital Management System</h2>
      <p>Sign in to your account</p>
    </div>

    <form [formGroup]="loginForm" (ngSubmit)="onSubmit()" class="login-form">
      <div class="form-group">
        <label for="email">Email Address</label>
        <input
          type="email"
          id="email"
          formControlName="email"
          class="form-control"
          [class.error]="email?.invalid && email?.touched"
          placeholder="Enter your email"
        >
        <div class="error-message" *ngIf="email?.invalid && email?.touched">
          <span *ngIf="email?.errors?.['required']">Email is required</span>
          <span *ngIf="email?.errors?.['email']">Please enter a valid email</span>
        </div>
      </div>

      <div class="form-group">
        <label for="password">Password</label>
        <input
          type="password"
          id="password"
          formControlName="password"
          class="form-control"
          [class.error]="password?.invalid && password?.touched"
          placeholder="Enter your password"
        >
        <div class="error-message" *ngIf="password?.invalid && password?.touched">
          <span *ngIf="password?.errors?.['required']">Password is required</span>
          <span *ngIf="password?.errors?.['minlength']">Password must be at least 6 characters</span>
        </div>
      </div>

      <div class="error-message" *ngIf="error">
        {{ error }}
      </div>

      <button
        type="submit"
        class="btn-primary"
        [disabled]="loginForm.invalid || loading"
      >
        <span *ngIf="loading">Signing in...</span>
        <span *ngIf="!loading">Sign In</span>
      </button>
    </form>

    <div class="login-footer">
      <p>Don't have an account? <a routerLink="/auth/register">Register here</a></p>
    </div>
  </div>
</div>
