@use 'sass:map';
@use 'sass:list';
@use '../core/tokens/m3-utils';
@use '../core/theming/theming';
@use '../core/theming/inspection';
@use '../core/tokens/m3';

/// Generates custom tokens for the mat-radio.
/// @param {String} $color-variant The color variant to use for the component
@function get-tokens($theme: m3.$sys-theme, $color-variant: null) {
  $system: m3-utils.get-system($theme);
  @if $color-variant {
    $system: m3-utils.replace-colors-with-variant($system, primary, $color-variant);
  }

  $tokens: (
    base: (
      radio-disabled-unselected-icon-opacity: 0.38,
      radio-disabled-selected-icon-opacity: 0.38,
    ),
    color: (
      radio-checked-ripple-color: map.get($system, primary),
      radio-disabled-label-color: m3-utils.color-with-opacity(map.get($system, on-surface), 38%),
      radio-disabled-selected-icon-color: map.get($system, on-surface),
      radio-disabled-unselected-icon-color: map.get($system, on-surface),
      radio-label-text-color: map.get($system, on-surface),
      radio-ripple-color: map.get($system, on-surface),
      radio-selected-focus-icon-color: map.get($system, primary),
      radio-selected-hover-icon-color: map.get($system, primary),
      radio-selected-icon-color: map.get($system, primary),
      radio-selected-pressed-icon-color: map.get($system, primary),
      radio-unselected-focus-icon-color: map.get($system, on-surface),
      radio-unselected-hover-icon-color: map.get($system, on-surface),
      radio-unselected-icon-color: map.get($system, on-surface-variant),
      radio-unselected-pressed-icon-color: map.get($system, on-surface),
    ),
    typography: (
      radio-label-text-font: map.get($system, body-medium-font),
      radio-label-text-line-height: map.get($system, body-medium-line-height),
      radio-label-text-size: map.get($system, body-medium-size),
      radio-label-text-tracking: map.get($system, body-medium-tracking),
      radio-label-text-weight: map.get($system, body-medium-weight),
    ),
    density: get-density-tokens(map.get($theme, inspection.$internals, density-scale)),
  );

  // Temporary removal where color variants previously did not include the
  // unselected icon color. Remove this and approve internal screenshot changes.
  @if $color-variant {
    $tokens: map.deep-remove($tokens, color, radio-unselected-icon-color);
  }

  @return $tokens;
}

// Tokens that can be configured through Angular Material's density theming API.
@function get-density-tokens($scale) {
  $scale: theming.clamp-density($scale, -3);
  $index: ($scale * -1) + 1;

  @return (
    radio-touch-target-display: list.nth((block, block, none, none), $index),
    radio-state-layer-size: list.nth((40px, 36px, 32px, 28px), $index),
  );
}
