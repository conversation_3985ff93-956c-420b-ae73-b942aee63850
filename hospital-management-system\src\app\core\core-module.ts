import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { HTTP_INTERCEPTORS } from '@angular/common/http';
import { Header } from './header/header';
import { Footer } from './footer/footer';
import { Sidebar } from './sidebar/sidebar';
import { AuthInterceptor } from './interceptors/auth.interceptor';

@NgModule({
  declarations: [
    Header,
    Footer,
    Sidebar
  ],
  imports: [
    CommonModule,
    RouterModule
  ],
  exports: [
    Header,
    Footer,
    Sidebar
  ],
  providers: [
    {
      provide: HTTP_INTERCEPTORS,
      useClass: AuthInterceptor,
      multi: true
    }
  ]
})
export class CoreModule { }
