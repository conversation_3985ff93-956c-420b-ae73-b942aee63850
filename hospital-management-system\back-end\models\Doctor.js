const mongoose = require('mongoose');

const doctorSchema = new mongoose.Schema({
  doctorId: {
    type: String,
    unique: true
  },
  firstName: {
    type: String,
    required: true,
    trim: true
  },
  lastName: {
    type: String,
    required: true,
    trim: true
  },
  email: {
    type: String,
    required: true,
    unique: true,
    lowercase: true
  },
  phone: {
    type: String,
    required: true
  },
  specialization: {
    type: String,
    required: true
  },
  qualification: {
    type: String,
    required: true
  },
  experience: {
    type: Number,
    required: true,
    min: 0
  },
  department: {
    type: String,
    required: true
  },
  consultationFee: {
    type: Number,
    required: true,
    min: 0
  },
  availableSlots: [{
    day: {
      type: String,
      enum: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
    },
    startTime: String,
    endTime: String
  }],
  address: {
    street: String,
    city: String,
    state: String,
    zipCode: String,
    country: String
  },
  licenseNumber: {
    type: String,
    required: true,
    unique: true
  },
  isActive: {
    type: Boolean,
    default: true
  }
}, {
  timestamps: true
});

// Generate doctor ID automatically
doctorSchema.pre('save', async function(next) {
  if (!this.doctorId) {
    try {
      const count = await mongoose.model('Doctor').countDocuments();
      this.doctorId = `DOC${String(count + 1).padStart(6, '0')}`;
    } catch (error) {
      // If there's an error getting count, use timestamp
      this.doctorId = `DOC${Date.now().toString().slice(-6)}`;
    }
  }
  next();
});

module.exports = mongoose.model('Doctor', doctorSchema);
