@use 'sass:meta';
@use 'sass:map';
@use 'sass:math';
@use 'sass:color';
@use '../core/tokens/m2-utils';
@use '../core/theming/inspection';
@use '../core/style/sass-utils';

$_default-size: 22px;
$_small-size: $_default-size - 6px;
$_large-size: $_default-size + 6px;

// Tokens that can't be configured through Angular Material's current theming API,
// but may be in a future version of the theming API.
@function get-unthemable-tokens() {
  $default-size: $_default-size;
  $small-size: $_small-size;
  $large-size: $_large-size;

  @return (
    badge-container-shape: 50%,
    badge-container-size: unset,
    badge-small-size-container-size: unset,
    badge-large-size-container-size: unset,

    badge-legacy-container-size: $default-size,
    badge-legacy-small-size-container-size: $small-size,
    badge-legacy-large-size-container-size: $large-size,

    badge-container-offset: math.div($default-size, -2) 0,
    badge-small-size-container-offset: math.div($small-size, -2) 0,
    badge-large-size-container-offset: math.div($large-size, -2) 0,

    badge-container-overlap-offset: math.div($default-size, -2),
    badge-small-size-container-overlap-offset: math.div($small-size, -2),
    badge-large-size-container-overlap-offset: math.div($large-size, -2),

    badge-container-padding: 0,
    badge-small-size-container-padding: 0,
    badge-large-size-container-padding: 0,
  );
}

// Tokens that can be configured through Angular Material's color theming API.
@function get-color-tokens($theme) {
  $primary-color-tokens: private-get-color-palette-color-tokens($theme, primary);
  $app-background: inspection.get-theme-color($theme, background, background);
  $disabled-background: inspection.get-theme-color($theme, foreground, disabled-button);

  // The disabled color usually has some kind of opacity, but because the badge is overlayed
  // on top of something else, it won't look good if it's opaque. If it is a color *type*,
  // we convert it into a solid color by taking the opacity from the rgba value and using
  // the value to determine the percentage of the background to put into foreground when
  // mixing the colors together.
  @if (meta.type-of($disabled-background) == color and meta.type-of($app-background) == color) {
    $badge-opacity: color.opacity($disabled-background);
    $disabled-background: color.mix($app-background,
      rgba($disabled-background, 1), (1 - $badge-opacity) * 100%);
  }

  @return map.merge($primary-color-tokens, (
    badge-disabled-state-background-color: $disabled-background,
    badge-disabled-state-text-color: inspection.get-theme-color($theme, foreground, disabled-text),
  ));
}

// Generates the tokens used to theme the badge based on a palette.
@function private-get-color-palette-color-tokens($theme, $palette-name: primary) {
  @return (
    badge-background-color: inspection.get-theme-color($theme, $palette-name),
    badge-text-color: inspection.get-theme-color($theme, $palette-name, default-contrast),
  );
}

// Tokens that can be configured through Angular Material's typography theming API.
@function get-typography-tokens($theme) {
  $base-size: 12px;

  @return (
    badge-text-font: inspection.get-theme-typography($theme, body-2, font-family),
    badge-line-height: $_default-size,
    badge-text-size: $base-size,
    badge-text-weight: 600,

    badge-small-size-text-size: $base-size * 0.75,
    badge-small-size-line-height: $_small-size,

    badge-large-size-text-size: $base-size * 2,
    badge-large-size-line-height: $_large-size,
  );
}

// Tokens that can be configured through Angular Material's density theming API.
@function get-density-tokens($theme) {
  @return ();
}

// Combines the tokens generated by the above functions into a single map with placeholder values.
// This is used to create token slots.
@function get-token-slots() {
  @return sass-utils.deep-merge-all(
      get-unthemable-tokens(),
      get-color-tokens(m2-utils.$placeholder-color-config),
      get-typography-tokens(m2-utils.$placeholder-typography-config),
      get-density-tokens(m2-utils.$placeholder-density-config)
  );
}
