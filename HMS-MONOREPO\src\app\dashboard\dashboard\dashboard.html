<div class="dashboard-container">
  <!-- Page Header -->
  <div class="mb-6">
    <h1 class="text-3xl font-bold text-gray-900">Dashboard</h1>
    <p class="text-gray-600 mt-1">Welcome back! Here's what's happening at your hospital today.</p>
  </div>

  <!-- Stats Cards - Role-based display -->
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    <!-- Total Patients - Admin and Doctors only -->
    <mat-card *hasPermission="['view_patients', 'manage_patients']" class="p-6">
      <div class="flex items-center justify-between">
        <div>
          <p class="text-sm font-medium text-gray-600">Total Patients</p>
          <p class="text-3xl font-bold text-gray-900">{{ stats.totalPatients || 0 }}</p>
          <p class="text-sm text-gray-500" *ngIf="stats.totalPatients === 0">No data available</p>
        </div>
        <div class="p-3 bg-blue-100 rounded-full">
          <mat-icon class="text-blue-600">people</mat-icon>
        </div>
      </div>
    </mat-card>

    <!-- My Appointments - Patients -->
    <mat-card *hasRole="'Patient'" class="p-6">
      <div class="flex items-center justify-between">
        <div>
          <p class="text-sm font-medium text-gray-600">My Appointments</p>
          <p class="text-3xl font-bold text-gray-900">{{ stats.myAppointments || 0 }}</p>
          <p class="text-sm text-blue-600">This month</p>
        </div>
        <div class="p-3 bg-green-100 rounded-full">
          <mat-icon class="text-green-600">event</mat-icon>
        </div>
      </div>
    </mat-card>

    <!-- Today's Appointments - Admin and Doctors -->
    <mat-card *hasPermission="['view_appointments', 'manage_appointments']" class="p-6">
      <div class="flex items-center justify-between">
        <div>
          <p class="text-sm font-medium text-gray-600">
            <span *hasRole="'Doctor'">My Appointments Today</span>
            <span *hasRole="'Admin'">Today's Appointments</span>
          </p>
          <p class="text-3xl font-bold text-gray-900">{{ stats.todayAppointments || 0 }}</p>
          <p class="text-sm text-gray-500" *ngIf="stats.todayAppointments === 0">No appointments today</p>
        </div>
        <div class="p-3 bg-green-100 rounded-full">
          <mat-icon class="text-green-600">event</mat-icon>
        </div>
      </div>
    </mat-card>

    <!-- Active Doctors - Admin only -->
    <mat-card *hasRole="'Admin'" class="p-6">
      <div class="flex items-center justify-between">
        <div>
          <p class="text-sm font-medium text-gray-600">Active Doctors</p>
          <p class="text-3xl font-bold text-gray-900">{{ stats.activeDoctors || 0 }}</p>
          <p class="text-sm text-blue-600">All departments</p>
        </div>
        <div class="p-3 bg-purple-100 rounded-full">
          <mat-icon class="text-purple-600">medical_services</mat-icon>
        </div>
      </div>
    </mat-card>

    <!-- My Prescriptions - Patients -->
    <mat-card *hasRole="'Patient'" class="p-6">
      <div class="flex items-center justify-between">
        <div>
          <p class="text-sm font-medium text-gray-600">Active Prescriptions</p>
          <p class="text-3xl font-bold text-gray-900">{{ stats.activePrescriptions || 0 }}</p>
          <p class="text-sm text-blue-600">Current medications</p>
        </div>
        <div class="p-3 bg-purple-100 rounded-full">
          <mat-icon class="text-purple-600">medication</mat-icon>
        </div>
      </div>
    </mat-card>

    <!-- System Status - Admin only -->
    <mat-card *hasRole="'Admin'" class="p-6">
      <div class="flex items-center justify-between">
        <div>
          <p class="text-sm font-medium text-gray-600">System Status</p>
          <p class="text-3xl font-bold text-gray-900">{{ stats.systemStatus || 'Active' }}</p>
          <p class="text-sm text-green-600">All services running</p>
        </div>
        <div class="p-3 bg-green-100 rounded-full">
          <mat-icon class="text-green-600">check_circle</mat-icon>
        </div>
      </div>
    </mat-card>
  </div>

  <!-- Quick Actions -->
  <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
    <!-- Recent Appointments -->
    <mat-card class="p-6">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-semibold text-gray-900">Recent Appointments</h3>
        <button mat-button color="primary" routerLink="/appointments">View All</button>
      </div>
      <div class="space-y-3" *ngIf="recentAppointments && recentAppointments.length > 0; else noAppointments">
        <div *ngFor="let appointment of recentAppointments" class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
          <div class="flex items-center space-x-3">
            <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
              <mat-icon class="text-blue-600 text-sm">person</mat-icon>
            </div>
            <div>
              <p class="font-medium text-gray-900">{{ appointment.patient?.firstName }} {{ appointment.patient?.lastName }}</p>
              <p class="text-sm text-gray-600">{{ appointment.doctor?.firstName }} {{ appointment.doctor?.lastName }} - {{ appointment.appointmentTime }}</p>
            </div>
          </div>
          <span class="px-2 py-1 text-xs rounded-full"
                [ngClass]="{
                  'bg-green-100 text-green-800': appointment.status === 'Confirmed',
                  'bg-blue-100 text-blue-800': appointment.status === 'Scheduled',
                  'bg-yellow-100 text-yellow-800': appointment.status === 'In Progress',
                  'bg-gray-100 text-gray-800': appointment.status === 'Completed'
                }">
            {{ appointment.status }}
          </span>
        </div>
      </div>
      <ng-template #noAppointments>
        <div class="text-center py-8 text-gray-500">
          <mat-icon class="text-4xl mb-2">event_busy</mat-icon>
          <p>No recent appointments</p>
        </div>
      </ng-template>
    </mat-card>

    <!-- Quick Actions - Role-based -->
    <mat-card class="p-6">
      <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>

      <!-- Admin Quick Actions -->
      <div *hasRole="'Admin'" class="grid grid-cols-2 gap-4">
        <button mat-raised-button color="primary" routerLink="/patients/new" class="h-16">
          <div class="flex flex-col items-center">
            <mat-icon>person_add</mat-icon>
            <span class="text-sm mt-1">Add Patient</span>
          </div>
        </button>
        <button mat-raised-button color="accent" routerLink="/appointments/new" class="h-16">
          <div class="flex flex-col items-center">
            <mat-icon>event_available</mat-icon>
            <span class="text-sm mt-1">Book Appointment</span>
          </div>
        </button>
        <button mat-raised-button routerLink="/doctors/new" class="h-16">
          <div class="flex flex-col items-center">
            <mat-icon>medical_services</mat-icon>
            <span class="text-sm mt-1">Add Doctor</span>
          </div>
        </button>
        <button mat-raised-button routerLink="/users" class="h-16">
          <div class="flex flex-col items-center">
            <mat-icon>group</mat-icon>
            <span class="text-sm mt-1">Manage Users</span>
          </div>
        </button>
      </div>

      <!-- Doctor Quick Actions -->
      <div *hasRole="'Doctor'" class="grid grid-cols-2 gap-4">
        <button mat-raised-button color="primary" routerLink="/patients" class="h-16">
          <div class="flex flex-col items-center">
            <mat-icon>people</mat-icon>
            <span class="text-sm mt-1">View Patients</span>
          </div>
        </button>
        <button mat-raised-button color="accent" routerLink="/appointments" class="h-16">
          <div class="flex flex-col items-center">
            <mat-icon>event</mat-icon>
            <span class="text-sm mt-1">My Appointments</span>
          </div>
        </button>
        <button mat-raised-button routerLink="/prescriptions/new" class="h-16">
          <div class="flex flex-col items-center">
            <mat-icon>medication</mat-icon>
            <span class="text-sm mt-1">New Prescription</span>
          </div>
        </button>
        <button mat-raised-button routerLink="/reports/new" class="h-16">
          <div class="flex flex-col items-center">
            <mat-icon>assessment</mat-icon>
            <span class="text-sm mt-1">Create Report</span>
          </div>
        </button>
      </div>

      <!-- Patient Quick Actions -->
      <div *hasRole="'Patient'" class="grid grid-cols-2 gap-4">
        <button mat-raised-button color="primary" routerLink="/appointments" class="h-16">
          <div class="flex flex-col items-center">
            <mat-icon>event</mat-icon>
            <span class="text-sm mt-1">My Appointments</span>
          </div>
        </button>
        <button mat-raised-button color="accent" routerLink="/prescriptions" class="h-16">
          <div class="flex flex-col items-center">
            <mat-icon>medication</mat-icon>
            <span class="text-sm mt-1">My Prescriptions</span>
          </div>
        </button>
        <button mat-raised-button routerLink="/reports" class="h-16">
          <div class="flex flex-col items-center">
            <mat-icon>assessment</mat-icon>
            <span class="text-sm mt-1">My Reports</span>
          </div>
        </button>
        <button mat-raised-button routerLink="/profile" class="h-16">
          <div class="flex flex-col items-center">
            <mat-icon>person</mat-icon>
            <span class="text-sm mt-1">My Profile</span>
          </div>
        </button>
      </div>
    </mat-card>
  </div>
</div>
