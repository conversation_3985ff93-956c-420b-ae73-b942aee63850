{"version": 3, "file": "cdk.mjs", "sources": ["../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/cdk/version.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {Version} from '@angular/core';\n\n/** Current version of the Angular Component Development Kit. */\nexport const VERSION = new Version('20.0.3');\n"], "names": [], "mappings": ";;AAUA;MACa,OAAO,GAAG,IAAI,OAAO,CAAC,mBAAmB;;;;"}