/* You can add global styles to this file, and also import other style files */

/* @tailwind base;
@tailwind components;
@tailwind utilities; */

/* Custom global styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', sans-serif;
  background-color: #f8fafc;
  color: #1e293b;
  line-height: 1.6;
}

/* Custom component classes */
/* @layer components {
  .btn {
    @apply px-4 py-2 rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2;
  }

  .btn-primary {
    @apply bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500;
  }

  .btn-secondary {
    @apply bg-secondary-200 text-secondary-800 hover:bg-secondary-300 focus:ring-secondary-500;
  }

  .btn-success {
    @apply bg-success-600 text-white hover:bg-success-700 focus:ring-success-500;
  }

  .btn-warning {
    @apply bg-warning-600 text-white hover:bg-warning-700 focus:ring-warning-500;
  }

  .btn-danger {
    @apply bg-danger-600 text-white hover:bg-danger-700 focus:ring-danger-500;
  }

  .card {
    @apply bg-white rounded-xl shadow-soft border border-secondary-200;
  }

  .form-input {
    @apply w-full px-3 py-2 border border-secondary-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent;
  }

  .form-label {
    @apply block text-sm font-medium text-secondary-700 mb-1;
  }

  .text-error {
    @apply text-danger-600 text-sm;
  }
} */

html, body { height: 100%; }
body { margin: 0; font-family: Roboto, "Helvetica Neue", sans-serif; }
