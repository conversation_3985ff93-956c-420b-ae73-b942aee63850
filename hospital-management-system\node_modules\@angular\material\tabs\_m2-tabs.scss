@use 'sass:map';
@use '../core/theming/theming';
@use '../core/theming/inspection';
@use '../core/style/sass-utils';
@use '../core/tokens/m2-utils';

// Tokens that can't be configured through Angular Material's current theming API,
// but may be in a future version of the theming API.
//
// Tokens that are available in MDC, but not used in Angular Material should be mapped to `null`.
// `null` indicates that we are intentionally choosing not to emit a slot or value for the token in
// our CSS.
@function get-unthemable-tokens() {
  @return (
    // This is specified both here and in the density tokens, because it determines the size of the
    // tab itself and there are internal tests who don't configure the theme correctly.
    tab-container-height: 48px,
    // For some period of time, the MDC tabs removed the divider. This has been added back in
    // and will be present in M3.
    tab-divider-color: transparent,
    tab-divider-height: 0,
    tab-active-indicator-height: 2px,
    // Currently set to zero, but used by the gmat styles to make the indicator rounded.
    tab-active-indicator-shape: 0,
  );
}

// Tokens that can be configured through Angular Material's color theming API.
@function get-color-tokens($theme, $palette-name: primary, $exclude: ()) {
  $is-dark: inspection.get-theme-type($theme) == dark;
  $inactive-label-text-color: inspection.get-theme-color($theme, foreground, text, 0.6);
  $active-label-text-color: inspection.get-theme-color($theme, $palette-name, default);
  $ripple-color: inspection.get-theme-color($theme, $palette-name, default);

  $tokens: (
    tab-disabled-ripple-color: inspection.get-theme-color($theme, foreground, disabled),
    tab-pagination-icon-color: inspection.get-theme-color($theme, foreground, icon, 1),

    // Note: MDC has equivalents of these tokens, but they lead to much higher selector specificity.
    tab-inactive-label-text-color: $inactive-label-text-color,
    tab-active-label-text-color: $active-label-text-color,

    // Tokens needed to implement the gmat styles. Externally they don't change.
    tab-active-ripple-color: $ripple-color,
    tab-inactive-ripple-color: $ripple-color,
    tab-inactive-focus-label-text-color: $inactive-label-text-color,
    tab-inactive-hover-label-text-color: $inactive-label-text-color,
    tab-active-focus-label-text-color: $active-label-text-color,
    tab-active-hover-label-text-color: $active-label-text-color,
    tab-active-focus-indicator-color: $active-label-text-color,
    tab-active-hover-indicator-color: $active-label-text-color,
    tab-active-indicator-color: inspection.get-theme-color($theme, $palette-name, default),
    tab-background-color: inspection.get-theme-color($theme, $palette-name, default),
    tab-foreground-color: inspection.get-theme-color($theme, $palette-name, default-contrast),
  );

  @each $token in $exclude {
    $tokens: map.remove($tokens, $token);
  }

  @return $tokens;
}

// Tokens that can be configured through Angular Material's typography theming API.
@function get-typography-tokens($theme) {
  @return (
    // Note: MDC has equivalents of these tokens, but they lead to much higher selector specificity.
    tab-label-text-font: inspection.get-theme-typography($theme, button, font-family),
    tab-label-text-size: inspection.get-theme-typography($theme, button, font-size),
    tab-label-text-tracking: inspection.get-theme-typography($theme, button, letter-spacing),
    tab-label-text-line-height: inspection.get-theme-typography($theme, button, line-height),
    tab-label-text-weight: inspection.get-theme-typography($theme, button, font-weight),
  );
}

// Tokens that can be configured through Angular Material's density theming API.
@function get-density-tokens($theme) {
  $scale: theming.clamp-density(inspection.get-theme-density($theme), -4);

  @return (
    tab-container-height: map.get((
      0: 48px,
      -1: 44px,
      -2: 40px,
      -3: 36px,
      -4: 32px,
    ), $scale),
  );
}

// Combines the tokens generated by the above functions into a single map with placeholder values.
// This is used to create token slots.
@function get-token-slots() {
  @return sass-utils.deep-merge-all(
      get-unthemable-tokens(),
      get-color-tokens(m2-utils.$placeholder-color-config),
      get-typography-tokens(m2-utils.$placeholder-typography-config),
      get-density-tokens(m2-utils.$placeholder-density-config)
  );
}
