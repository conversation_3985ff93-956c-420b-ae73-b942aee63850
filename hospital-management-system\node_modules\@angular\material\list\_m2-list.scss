@use '../core/tokens/m2-utils';
@use '../core/style/sass-utils';
@use '../core/theming/inspection';
@use 'sass:map';

// Tokens that can't be configured through Angular Material's current theming API,
// but may be in a future version of the theming API.
@function get-unthemable-tokens() {
  @return (
    list-active-indicator-color: transparent,
    list-active-indicator-shape: 4px,
    list-list-item-container-shape: 0,
    list-list-item-leading-avatar-shape: 50%,
    list-list-item-container-color: transparent,
    list-list-item-selected-container-color: transparent,
    list-list-item-leading-avatar-color: transparent,
    list-list-item-leading-icon-size: 24px,
    list-list-item-leading-avatar-size: 40px,
    list-list-item-trailing-icon-size: 24px,
    list-list-item-disabled-state-layer-color: transparent,
    list-list-item-disabled-state-layer-opacity: 0,
    list-list-item-disabled-label-text-opacity: 0.38,
    list-list-item-disabled-leading-icon-opacity: 0.38,
    list-list-item-disabled-trailing-icon-opacity: 0.38,
  );
}

// Tokens that can be configured through Angular Material's color theming API.
@function get-color-tokens($theme) {
  $is-dark: inspection.get-theme-type($theme) == dark;
  $foreground-base: inspection.get-theme-color($theme, foreground, base);
  $foreground-text: inspection.get-theme-color($theme, foreground, text);
  $foreground-secondary-text: inspection.get-theme-color($theme, foreground, secondary-text);
  $foreground-hint-text: inspection.get-theme-color($theme, foreground, hint-text);
  $text-icon-on-background: sass-utils.safe-color-change(
      $foreground-base, $alpha: if($is-dark, 0.5, 0.38));

  @return (
    list-list-item-label-text-color: $foreground-text,
    list-list-item-supporting-text-color: $foreground-secondary-text,
    list-list-item-leading-icon-color: $text-icon-on-background,
    list-list-item-trailing-supporting-text-color: $foreground-hint-text,
    list-list-item-trailing-icon-color: $text-icon-on-background,
    list-list-item-selected-trailing-icon-color: $text-icon-on-background,
    list-list-item-disabled-label-text-color: $foreground-base,
    list-list-item-disabled-leading-icon-color: $foreground-base,
    list-list-item-disabled-trailing-icon-color: $foreground-base,
    list-list-item-hover-label-text-color: $foreground-text,
    list-list-item-hover-leading-icon-color: $text-icon-on-background,
    list-list-item-hover-state-layer-color: $foreground-base,
    list-list-item-hover-state-layer-opacity: if($is-dark, 0.08, 0.04),
    list-list-item-hover-trailing-icon-color: $text-icon-on-background,
    list-list-item-focus-label-text-color: $foreground-text,
    list-list-item-focus-state-layer-color: $foreground-base,
    list-list-item-focus-state-layer-opacity: if($is-dark, 0.24, 0.12),
  );
}

// Tokens that can be configured through Angular Material's typography theming API.
@function get-typography-tokens($theme) {
  @return (
    list-list-item-label-text-font: inspection.get-theme-typography($theme, body-1, font-family),
    list-list-item-label-text-line-height:
        inspection.get-theme-typography($theme, body-1, line-height),
    list-list-item-label-text-size: inspection.get-theme-typography($theme, body-1, font-size),
    list-list-item-label-text-tracking:
        inspection.get-theme-typography($theme, body-1, letter-spacing),
    list-list-item-label-text-weight: inspection.get-theme-typography($theme, body-1, font-weight),
    list-list-item-supporting-text-font:
        inspection.get-theme-typography($theme, body-2, font-family),
    list-list-item-supporting-text-line-height:inspection.get-theme-typography(
        $theme, body-2, line-height),
    list-list-item-supporting-text-size: inspection.get-theme-typography($theme, body-2, font-size),
    list-list-item-supporting-text-tracking: inspection.get-theme-typography(
        $theme, body-2, letter-spacing),
    list-list-item-supporting-text-weight:
        inspection.get-theme-typography($theme, body-2, font-weight),
    list-list-item-trailing-supporting-text-font: inspection.get-theme-typography(
        $theme, caption, font-family),
    list-list-item-trailing-supporting-text-line-height: inspection.get-theme-typography(
        $theme, caption, line-height),
    list-list-item-trailing-supporting-text-size: inspection.get-theme-typography(
        $theme, caption, font-size),
    list-list-item-trailing-supporting-text-tracking: inspection.get-theme-typography(
        $theme, caption, letter-spacing),
    list-list-item-trailing-supporting-text-weight: inspection.get-theme-typography(
        $theme, caption, font-weight),
  );
}

// Tokens that can be configured through Angular Material's density theming API.
@function get-density-tokens($theme) {
  $scale: inspection.get-theme-density($theme);

  @return (
    list-list-item-leading-icon-start-space: 16px,
    list-list-item-leading-icon-end-space: 32px,
    list-list-item-one-line-container-height: map.get((
      0: 48px,
      -1: 44px,
      -2: 40px,
      -3: 36px,
      -4: 32px,
      -5: 24px,
    ), $scale),
    list-list-item-two-line-container-height: map.get((
      0: 64px,
      -1: 60px,
      -2: 56px,
      -3: 52px,
      -4: 48px,
      -5: 48px,
    ), $scale),
    list-list-item-three-line-container-height: map.get((
      0: 88px,
      -1: 84px,
      -2: 80px,
      -3: 76px,
      -4: 72px,
      -5: 56px,
    ), $scale),
  );
}

// Combines the tokens generated by the above functions into a single map with placeholder values.
// This is used to create token slots.
@function get-token-slots() {
  @return sass-utils.deep-merge-all(
      get-unthemable-tokens(),
      get-color-tokens(m2-utils.$placeholder-color-config),
      get-typography-tokens(m2-utils.$placeholder-typography-config),
      get-density-tokens(m2-utils.$placeholder-density-config)
  );
}
