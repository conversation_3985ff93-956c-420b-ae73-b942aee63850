@use '../core/style/sass-utils';
@use '../core/theming/inspection';
@use '../core/theming/theming';
@use '../core/tokens/token-utils';
@use '../core/typography/typography';
@use './m2-button';
@use './m3-button';
@use 'sass:map';

@mixin _m2-button-variant($theme, $palette) {
  $mat-tokens: if(
    $palette,
    m2-button.private-get-color-palette-color-tokens($theme, $palette),
    m2-button.get-color-tokens($theme)
  );

  @include token-utils.create-token-values-mixed($mat-tokens);
}

/// Outputs base theme styles (styles not dependent on the color, typography, or density settings)
/// for the mat-button.
/// @param {Map} $theme The theme to generate base styles for.
@mixin base($theme) {
  @if inspection.get-theme-version($theme) == 1 {
    @include token-utils.create-token-values(map.get(m3-button.get-tokens($theme), base));
  } @else {
    @include sass-utils.current-selector-or-root() {
      @include token-utils.create-token-values-mixed(m2-button.get-unthemable-tokens());
    }
  }
}

/// Outputs color theme styles for the mat-button.
/// @param {Map} $theme The theme to generate color styles for.
/// @param {String} $color-variant The color variant to use for
///     the badge: primary, secondary, tertiary, or error (If not specified,
///     default primary color will be used).
@mixin color($theme, $color-variant: null) {
  @if inspection.get-theme-version($theme) == 1 {
    @include token-utils.create-token-values(
        map.get(m3-button.get-tokens($theme, $color-variant), color));
  } @else {
    @include sass-utils.current-selector-or-root() {
      @include _m2-button-variant($theme, null);
    }

    .mat-mdc-button,
    .mat-mdc-unelevated-button,
    .mat-mdc-raised-button,
    .mat-mdc-outlined-button,
    .mat-tonal-button {
      &.mat-primary {
        @include _m2-button-variant($theme, primary);
      }

      &.mat-accent {
        @include _m2-button-variant($theme, accent);
      }

      &.mat-warn {
        @include _m2-button-variant($theme, warn);
      }
    }
  }
}

/// Outputs typography theme styles for the mat-button.
/// @param {Map} $theme The theme to generate typography styles for.
@mixin typography($theme) {
  @if inspection.get-theme-version($theme) == 1 {
    @include token-utils.create-token-values(map.get(m3-button.get-tokens($theme), typography));
  } @else {
    @include sass-utils.current-selector-or-root() {
      @include token-utils.create-token-values-mixed(m2-button.get-typography-tokens($theme));
    }
  }
}

/// Outputs density theme styles for the mat-button.
/// @param {Map} $theme The theme to generate density styles for.
@mixin density($theme) {
  @if inspection.get-theme-version($theme) == 1 {
    @include token-utils.create-token-values(map.get(m3-button.get-tokens($theme), density));
  } @else {
    @include sass-utils.current-selector-or-root() {
      @include token-utils.create-token-values-mixed(m2-button.get-density-tokens($theme));
    }
  }
}

/// Defines the tokens that will be available in the `overrides` mixin and for docs extraction.
@function _define-overrides() {
  @return (
    (
      namespace: button,
      tokens: token-utils.get-overrides(m3-button.get-tokens(), button),
    ),
  );
}

/// Outputs the CSS variable values for the given tokens.
/// @param {Map} $tokens The token values to emit.
@mixin overrides($tokens: ()) {
    @include token-utils.batch-create-token-values($tokens, _define-overrides());
}

/// Outputs all (base, color, typography, and density) theme styles for the mat-button.
/// @param {Map} $theme The theme to generate styles for.
/// @param {String} $color-variant: The color variant to use for the button: primary, secondary,
//         tertiary, or error (If not specified, default primary color will be used).
@mixin theme($theme, $color-variant: null) {
  @include theming.private-check-duplicate-theme-styles($theme, 'mat-button') {
    @if inspection.get-theme-version($theme) == 1 {
      @include base($theme);
      @include color($theme, $color-variant);
      @include density($theme);
      @include typography($theme);
    } @else {
      @include base($theme);
      @if inspection.theme-has($theme, color) {
        @include color($theme);
      }
      @if inspection.theme-has($theme, density) {
        @include density($theme);
      }
      @if inspection.theme-has($theme, typography) {
        @include typography($theme);
      }
    }
  }
}
