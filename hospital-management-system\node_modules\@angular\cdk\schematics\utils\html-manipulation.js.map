{"version": 3, "file": "html-manipulation.js", "sourceRoot": "", "sources": ["html-manipulation.ts"], "names": [], "mappings": ";AAAA;;;;;;GAMG;;AAOH,0DA4BC;AAGD,sDAEC;AAGD,oCAqCC;AA9ED,2DAAqE;AACrE,qDAAqE;AACrE,mCAA0C;AAE1C,kGAAkG;AAClG,SAAgB,uBAAuB,CAAC,IAAU,EAAE,YAAoB,EAAE,WAAmB;IAC3F,MAAM,cAAc,GAAG,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IAE/C,IAAI,CAAC,cAAc,EAAE,CAAC;QACpB,MAAM,IAAI,gCAAmB,CAAC,iCAAiC,YAAY,EAAE,CAAC,CAAC;IACjF,CAAC;IAED,MAAM,WAAW,GAAG,cAAc,CAAC,QAAQ,EAAE,CAAC;IAE9C,IAAI,WAAW,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;QACtC,OAAO;IACT,CAAC;IAED,MAAM,OAAO,GAAG,qBAAqB,CAAC,WAAW,CAAC,CAAC;IAEnD,IAAI,CAAC,OAAO,EAAE,CAAC;QACb,MAAM,KAAK,CAAC,iDAAiD,cAAc,EAAE,CAAC,CAAC;IACjF,CAAC;IAED,yFAAyF;IACzF,uEAAuE;IACvE,MAAM,YAAY,GAAG,OAAO,CAAC,kBAAmB,CAAC,MAAO,CAAC,WAAW,CAAC;IACrE,MAAM,iBAAiB,GAAG,IAAA,2CAA0B,EAAC,OAAO,CAAC,CAAC;IAC9D,MAAM,SAAS,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC,iBAAiB,CAAC,GAAG,WAAW,EAAE,CAAC;IAEnE,MAAM,cAAc,GAAG,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC,WAAW,CAAC,YAAY,EAAE,GAAG,SAAS,IAAI,CAAC,CAAC;IAElG,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,CAAC;AACpC,CAAC;AAED,4EAA4E;AAC5E,SAAgB,qBAAqB,CAAC,WAAmB;IACvD,OAAO,mBAAmB,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;AAClD,CAAC;AAED,gDAAgD;AAChD,SAAgB,YAAY,CAAC,IAAU,EAAE,YAAoB,EAAE,SAAiB;IAC9E,MAAM,cAAc,GAAG,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IAE/C,IAAI,CAAC,cAAc,EAAE,CAAC;QACpB,MAAM,IAAI,gCAAmB,CAAC,iCAAiC,YAAY,EAAE,CAAC,CAAC;IACjF,CAAC;IAED,MAAM,WAAW,GAAG,cAAc,CAAC,QAAQ,EAAE,CAAC;IAC9C,MAAM,IAAI,GAAG,mBAAmB,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;IAEtD,IAAI,CAAC,IAAI,EAAE,CAAC;QACV,MAAM,KAAK,CAAC,+CAA+C,cAAc,EAAE,CAAC,CAAC;IAC/E,CAAC;IAED,MAAM,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,SAAS,CAAC,IAAI,KAAK,OAAO,CAAC,CAAC;IAEhF,IAAI,cAAc,EAAE,CAAC;QACnB,MAAM,QAAQ,GAAG,cAAc,CAAC,KAAK;aAClC,KAAK,CAAC,GAAG,CAAC;aACV,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;aACxB,QAAQ,CAAC,SAAS,CAAC,CAAC;QAEvB,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,iFAAiF;YACjF,sDAAsD;YACtD,MAAM,sBAAsB,GAAG,IAAI,CAAC,kBAAmB,CAAC,KAAM,CAAC,OAAO,CAAC,CAAC;YACxE,MAAM,cAAc,GAAG,IAAI;iBACxB,WAAW,CAAC,YAAY,CAAC;iBACzB,WAAW,CAAC,sBAAsB,CAAC,SAAS,GAAG,CAAC,EAAE,IAAI,SAAS,EAAE,CAAC,CAAC;YACtE,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,CAAC;QACpC,CAAC;IACH,CAAC;SAAM,CAAC;QACN,MAAM,cAAc,GAAG,IAAI;aACxB,WAAW,CAAC,YAAY,CAAC;aACzB,WAAW,CAAC,IAAI,CAAC,kBAAmB,CAAC,QAAS,CAAC,SAAS,GAAG,CAAC,EAAE,WAAW,SAAS,GAAG,CAAC,CAAC;QAC1F,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,CAAC;IACpC,CAAC;AACH,CAAC;AAED,wCAAwC;AACxC,SAAS,mBAAmB,CAAC,OAAe,EAAE,WAAmB;IAC/D,MAAM,QAAQ,GAAG,IAAA,cAAS,EAAC,WAAW,EAAE,EAAC,sBAAsB,EAAE,IAAI,EAAC,CAAC,CAAC;IACxE,MAAM,SAAS,GAAG,CAAC,GAAG,QAAQ,CAAC,UAAU,CAAC,CAAC;IAE3C,OAAO,SAAS,CAAC,MAAM,EAAE,CAAC;QACxB,MAAM,IAAI,GAAG,SAAS,CAAC,KAAK,EAAa,CAAC;QAE1C,IAAI,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,KAAK,OAAO,EAAE,CAAC;YAC5C,OAAO,IAAI,CAAC;QACd,CAAC;aAAM,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YAC3B,SAAS,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC;QACrC,CAAC;IACH,CAAC;IAED,OAAO,IAAI,CAAC;AACd,CAAC"}