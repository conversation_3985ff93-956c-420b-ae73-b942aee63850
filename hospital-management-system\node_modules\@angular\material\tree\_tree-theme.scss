@use 'sass:map';
@use '../core/theming/theming';
@use '../core/theming/inspection';
@use '../core/typography/typography';
@use '../core/style/sass-utils';
@use '../core/tokens/token-utils';
@use './m2-tree';
@use './m3-tree';

@mixin base($theme) {
  @if inspection.get-theme-version($theme) == 1 {
    @include token-utils.create-token-values(map.get(m3-tree.get-tokens($theme), base));
  } @else {
  }
}

@mixin color($theme) {
  @if inspection.get-theme-version($theme) == 1 {
    @include token-utils.create-token-values(map.get(m3-tree.get-tokens($theme), color));
  } @else {
    @include sass-utils.current-selector-or-root() {
      @include token-utils.create-token-values-mixed(m2-tree.get-color-tokens($theme));
    }
  }
}

@mixin typography($theme) {
  @if inspection.get-theme-version($theme) == 1 {
    @include token-utils.create-token-values(map.get(m3-tree.get-tokens($theme), typography));
  } @else {
    // TODO(mmalerba): Stop calling this and resolve resulting screen diffs.
    $theme: inspection.private-get-typography-back-compat-theme($theme);

    @include sass-utils.current-selector-or-root() {
      @include token-utils.create-token-values-mixed(m2-tree.get-typography-tokens($theme));
    }
  }
}

@mixin density($theme) {
  @if inspection.get-theme-version($theme) == 1 {
    @include token-utils.create-token-values(map.get(m3-tree.get-tokens($theme), density));
  } @else {
    $density-scale: inspection.get-theme-density($theme);

    @include sass-utils.current-selector-or-root() {
      @include token-utils.create-token-values-mixed(m2-tree.get-density-tokens($theme));
    }
  }
}

/// Defines the tokens that will be available in the `overrides` mixin and for docs extraction.
@function _define-overrides() {
  @return (
    (
      namespace: tree,
      tokens: token-utils.get-overrides(m3-tree.get-tokens(), tree)
    ),
  );
}

@mixin overrides($tokens: ()) {
    @include token-utils.batch-create-token-values($tokens, _define-overrides());
}

@mixin theme($theme) {
  @include theming.private-check-duplicate-theme-styles($theme, 'mat-tree') {
    @if inspection.get-theme-version($theme) == 1 {
      @include base($theme);
      @include color($theme);
      @include density($theme);
      @include typography($theme);
    } @else {
      @include base($theme);
      @if inspection.theme-has($theme, color) {
        @include color($theme);
      }
      @if inspection.theme-has($theme, density) {
        @include density($theme);
      }
      @if inspection.theme-has($theme, typography) {
        @include typography($theme);
      }
    }
  }
}
