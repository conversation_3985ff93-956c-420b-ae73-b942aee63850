//
// Design system display name: Material 3
// Design system version: v0.161
//

@use 'sass:map';

// Indicates whether alternative tokens should be used
$_alternate-tokens: false;

@function md-sys-color-values-dark($palettes) {
  $values: (
    background: map.get($palettes, neutral, 6),
    error: map.get($palettes, error, 80),
    error-container: map.get($palettes, error, 30),
    inverse-on-surface: map.get($palettes, neutral, 20),
    inverse-primary: map.get($palettes, primary, 40),
    inverse-surface: map.get($palettes, neutral, 90),
    on-background: map.get($palettes, neutral, 90),
    on-error: map.get($palettes, error, 20),
    on-error-container: map.get($palettes, error, 90),
    on-primary: map.get($palettes, primary, 20),
    on-primary-container: map.get($palettes, primary, 90),
    on-primary-fixed: map.get($palettes, primary, 10),
    on-primary-fixed-variant: map.get($palettes, primary, 30),
    on-secondary: map.get($palettes, secondary, 20),
    on-secondary-container: map.get($palettes, secondary, 90),
    on-secondary-fixed: map.get($palettes, secondary, 10),
    on-secondary-fixed-variant: map.get($palettes, secondary, 30),
    on-surface: map.get($palettes, neutral, 90),
    on-surface-variant: map.get($palettes, neutral-variant, 90),
    on-tertiary: map.get($palettes, tertiary, 20),
    on-tertiary-container: map.get($palettes, tertiary, 90),
    on-tertiary-fixed: map.get($palettes, tertiary, 10),
    on-tertiary-fixed-variant: map.get($palettes, tertiary, 30),
    outline: map.get($palettes, neutral-variant, 60),
    outline-variant: map.get($palettes, neutral-variant, 30),
    primary: map.get($palettes, primary, 80),
    primary-container: map.get($palettes, primary, 30),
    primary-fixed: map.get($palettes, primary, 90),
    primary-fixed-dim: map.get($palettes, primary, 80),
    scrim: map.get($palettes, neutral, 0),
    secondary: map.get($palettes, secondary, 80),
    secondary-container: map.get($palettes, secondary, 30),
    secondary-fixed: map.get($palettes, secondary, 90),
    secondary-fixed-dim: map.get($palettes, secondary, 80),
    shadow: map.get($palettes, neutral, 0),
    surface: map.get($palettes, neutral, 6),
    surface-bright: map.get($palettes, neutral, 24),
    surface-container: map.get($palettes, neutral, 12),
    surface-container-high: map.get($palettes, neutral, 17),
    surface-container-highest: map.get($palettes, neutral, 22),
    surface-container-low: map.get($palettes, neutral, 10),
    surface-container-lowest: map.get($palettes, neutral, 4),
    surface-dim: map.get($palettes, neutral, 6),
    surface-tint: map.get($palettes, primary, 80),
    surface-variant: map.get($palettes, neutral-variant, 30),
    tertiary: map.get($palettes, tertiary, 80),
    tertiary-container: map.get($palettes, tertiary, 30),
    tertiary-fixed: map.get($palettes, tertiary, 90),
    tertiary-fixed-dim: map.get($palettes, tertiary, 80)
  );

  @if ($_alternate-tokens) {
    $values: map.merge($values, (
      background: map.get($palettes, neutral, 10),
      on-surface-variant: map.get($palettes, neutral-variant, 80),
      surface: map.get($palettes, neutral, 10),
      surface-bright: #37393b,
      surface-container: #1e1f20,
      surface-container-high: #282a2c,
      surface-container-highest: #333537,
      surface-container-low: #1b1b1b,
      surface-container-lowest: #0e0e0e,
      surface-dim: #131313,
      surface-tint: #d1e1ff,
    ));
  }

  @return $values;
}

@function md-sys-color-values-light($palettes) {
  $values: (
    background: map.get($palettes, neutral, 98),
    error: map.get($palettes, error, 40),
    error-container: map.get($palettes, error, 90),
    inverse-on-surface: map.get($palettes, neutral, 95),
    inverse-primary: map.get($palettes, primary, 80),
    inverse-surface: map.get($palettes, neutral, 20),
    on-background: map.get($palettes, neutral, 10),
    on-error: map.get($palettes, error, 100),
    on-error-container: map.get($palettes, error, 30),
    on-primary: map.get($palettes, primary, 100),
    on-primary-container: map.get($palettes, primary, 30),
    on-primary-fixed: map.get($palettes, primary, 10),
    on-primary-fixed-variant: map.get($palettes, primary, 30),
    on-secondary: map.get($palettes, secondary, 100),
    on-secondary-container: map.get($palettes, secondary, 30),
    on-secondary-fixed: map.get($palettes, secondary, 10),
    on-secondary-fixed-variant: map.get($palettes, secondary, 30),
    on-surface: map.get($palettes, neutral, 10),
    on-surface-variant: map.get($palettes, neutral-variant, 30),
    on-tertiary: map.get($palettes, tertiary, 100),
    on-tertiary-container: map.get($palettes, tertiary, 30),
    on-tertiary-fixed: map.get($palettes, tertiary, 10),
    on-tertiary-fixed-variant: map.get($palettes, tertiary, 30),
    outline: map.get($palettes, neutral-variant, 50),
    outline-variant: map.get($palettes, neutral-variant, 80),
    primary: map.get($palettes, primary, 40),
    primary-container: map.get($palettes, primary, 90),
    primary-fixed: map.get($palettes, primary, 90),
    primary-fixed-dim: map.get($palettes, primary, 80),
    scrim: map.get($palettes, neutral, 0),
    secondary: map.get($palettes, secondary, 40),
    secondary-container: map.get($palettes, secondary, 90),
    secondary-fixed: map.get($palettes, secondary, 90),
    secondary-fixed-dim: map.get($palettes, secondary, 80),
    shadow: map.get($palettes, neutral, 0),
    surface: map.get($palettes, neutral, 98),
    surface-bright: map.get($palettes, neutral, 98),
    surface-container: map.get($palettes, neutral, 94),
    surface-container-high: map.get($palettes, neutral, 92),
    surface-container-highest: map.get($palettes, neutral, 90),
    surface-container-low: map.get($palettes, neutral, 96),
    surface-container-lowest: map.get($palettes, neutral, 100),
    surface-dim: map.get($palettes, neutral, 87),
    surface-tint: map.get($palettes, primary, 40),
    surface-variant: map.get($palettes, neutral-variant, 90),
    tertiary: map.get($palettes, tertiary, 40),
    tertiary-container: map.get($palettes, tertiary, 90),
    tertiary-fixed: map.get($palettes, tertiary, 90),
    tertiary-fixed-dim: map.get($palettes, tertiary, 80)
  );

  @if ($_alternate-tokens) {
    $values: map.merge($values, (
      background: map.get($palettes, neutral, 100),
      on-error-container: map.get($palettes, error, 10),
      on-primary-container: map.get($palettes, primary, 10),
      on-secondary-container: map.get($palettes, secondary, 10),
      on-tertiary-container: map.get($palettes, tertiary, 10),
      surface: map.get($palettes, neutral, 100),
      surface-bright: map.get($palettes, neutral, 100),
      surface-container: #f0f4f9,
      surface-container-high: #e9eef6,
      surface-container-highest: #dde3ea,
      surface-container-low: #f8fafd,
      surface-container-lowest: map.get($palettes, primary, 100),
      surface-dim: #d3dbe5,
      surface-tint: #6991d6,
    ));
  }

  @return $values;
}
