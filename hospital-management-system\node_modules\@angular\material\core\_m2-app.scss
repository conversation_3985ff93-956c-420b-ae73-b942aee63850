@use 'sass:map';
@use './tokens/m2-utils';
@use './theming/inspection';
@use './style/sass-utils';
@use './style/elevation';

// Tokens that can't be configured through Angular Material's current theming API,
// but may be in a future version of the theming API.
@function get-unthemable-tokens() {
  @return ();
}

// Tokens that can be configured through Angular Material's color theming API.
@function get-color-tokens($theme) {
  $tokens: (
    app-background-color: inspection.get-theme-color($theme, background, background),
    app-text-color: inspection.get-theme-color($theme, foreground, text),
  );

  @for $zValue from 0 through 24 {
    $elevation-color: inspection.get-theme-color($theme, foreground, elevation);
    $shadow: elevation.get-box-shadow($zValue,
      if($elevation-color == null, elevation.$color, $elevation-color));
    $tokens: map.set($tokens, 'app-elevation-shadow-level-#{$zValue}', $shadow);
  }

  @return $tokens;
}

// Tokens that can be configured through Angular Material's typography theming API.
@function get-typography-tokens($theme) {
  @return ();
}

// Tokens that can be configured through Angular Material's density theming API.
@function get-density-tokens($theme) {
  @return ();
}

// Combines the tokens generated by the above functions into a single map with placeholder values.
// This is used to create token slots.
@function get-token-slots() {
  @return sass-utils.deep-merge-all(
      get-unthemable-tokens(),
      get-color-tokens(m2-utils.$placeholder-color-config),
      get-typography-tokens(m2-utils.$placeholder-typography-config),
      get-density-tokens(m2-utils.$placeholder-density-config)
  );
}
