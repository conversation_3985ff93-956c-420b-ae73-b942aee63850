import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Routes } from '@angular/router';
import { ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { MatTableModule } from '@angular/material/table';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { SharedModule } from '../shared/shared-module';
import { PatientList } from './patient-list/patient-list';
import { PatientDetail } from './patient-detail/patient-detail';
import { PatientForm } from './patient-form/patient-form';

const routes: Routes = [
  { path: '', component: PatientList },
  { path: 'new', component: PatientForm },
  { path: 'edit/:id', component: PatientForm },
  { path: ':id', component: PatientDetail }
];

@NgModule({
  declarations: [
    PatientList,
    PatientDetail,
    PatientForm
  ],
  imports: [
    CommonModule,
    ReactiveFormsModule,
    RouterModule.forChild(routes),
    MatButtonModule,
    MatIconModule,
    MatCardModule,
    MatTableModule,
    MatProgressSpinnerModule,
    SharedModule
  ]
})
export class PatientsModule { }
