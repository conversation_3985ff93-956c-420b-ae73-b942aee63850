import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Routes } from '@angular/router';
import { ReactiveFormsModule } from '@angular/forms';
import { PatientList } from './patient-list/patient-list';
import { PatientDetail } from './patient-detail/patient-detail';
import { PatientForm } from './patient-form/patient-form';

const routes: Routes = [
  { path: '', component: PatientList },
  { path: 'new', component: PatientForm },
  { path: 'edit/:id', component: PatientForm },
  { path: ':id', component: PatientDetail }
];

@NgModule({
  declarations: [
    PatientList,
    PatientDetail,
    PatientForm
  ],
  imports: [
    CommonModule,
    ReactiveFormsModule,
    RouterModule.forChild(routes)
  ]
})
export class PatientsModule { }
