import { Component, OnInit } from '@angular/core';
import { Auth } from '../../core/services/auth';
import { PatientService } from '../../core/services/patient';
import { DoctorService } from '../../core/services/doctor';
import { AppointmentService } from '../../core/services/appointment';
import { PrescriptionService } from '../../core/services/prescription.service';

interface DashboardStats {
  totalPatients?: number;
  myAppointments?: number;
  todayAppointments?: number;
  activeDoctors?: number;
  activePrescriptions?: number;
  monthlyRevenue?: string;
}

@Component({
  selector: 'app-dashboard',
  standalone: false,
  templateUrl: './dashboard.html',
  styleUrl: './dashboard.css'
})
export class Dashboard implements OnInit {
  stats: DashboardStats = {};
  currentUser: any;

  constructor(
    private authService: Auth,
    private patientService: PatientService,
    private doctorService: DoctorService,
    private appointmentService: AppointmentService,
    private prescriptionService: PrescriptionService
  ) {}

  ngOnInit(): void {
    this.currentUser = this.authService.getCurrentUser();
    this.loadDashboardStats();
  }

  private loadDashboardStats(): void {
    if (!this.currentUser) return;

    switch (this.currentUser.role) {
      case 'Admin':
        this.loadAdminStats();
        break;
      case 'Doctor':
        this.loadDoctorStats();
        break;
      case 'Patient':
        this.loadPatientStats();
        break;
    }
  }

  private loadAdminStats(): void {
    // Load total patients
    this.patientService.getPatients(1, 1).subscribe({
      next: (response) => {
        this.stats.totalPatients = response.total;
      },
      error: (error) => console.error('Error loading patients:', error)
    });

    // Load total doctors
    this.doctorService.getDoctors(1, 1).subscribe({
      next: (response) => {
        this.stats.activeDoctors = response.total;
      },
      error: (error) => console.error('Error loading doctors:', error)
    });

    // Load today's appointments
    const today = new Date().toISOString().split('T')[0];
    this.appointmentService.getAppointments(1, 100, '', today).subscribe({
      next: (response) => {
        this.stats.todayAppointments = response.total;
      },
      error: (error) => console.error('Error loading appointments:', error)
    });

    // Mock revenue data
    this.stats.monthlyRevenue = '2.4L';
  }

  private loadDoctorStats(): void {
    // Load today's appointments for this doctor
    const today = new Date().toISOString().split('T')[0];
    this.appointmentService.getAppointments(1, 100, '', today).subscribe({
      next: (response) => {
        this.stats.todayAppointments = response.total;
      },
      error: (error) => console.error('Error loading appointments:', error)
    });

    // Load total patients (doctors can see all patients)
    this.patientService.getPatients(1, 1).subscribe({
      next: (response) => {
        this.stats.totalPatients = response.total;
      },
      error: (error) => console.error('Error loading patients:', error)
    });
  }

  private loadPatientStats(): void {
    // Load patient's appointments
    this.appointmentService.getAppointments(1, 100).subscribe({
      next: (response) => {
        this.stats.myAppointments = response.total;
      },
      error: (error) => console.error('Error loading appointments:', error)
    });

    // Load patient's active prescriptions
    this.prescriptionService.getPrescriptions(1, 100, 'Active').subscribe({
      next: (response) => {
        this.stats.activePrescriptions = response.total;
      },
      error: (error) => console.error('Error loading prescriptions:', error)
    });
  }
}
