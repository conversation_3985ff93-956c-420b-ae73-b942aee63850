import { Directive, Input, TemplateRef, ViewContainerRef, OnInit, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { Subject, takeUntil } from 'rxjs';
import { Auth } from '../../core/services/auth';

@Directive({
  selector: '[hasPermission]',
  standalone: false
})
export class HasPermissionDirective implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();
  private permissions: string[] = [];
  private roles: string[] = [];

  @Input() set hasPermission(permissions: string | string[]) {
    this.permissions = Array.isArray(permissions) ? permissions : [permissions];
    this.updateView();
  }

  @Input() set hasRole(roles: string | string[]) {
    this.roles = Array.isArray(roles) ? roles : [roles];
    this.updateView();
  }

  constructor(
    private templateRef: TemplateRef<any>,
    private viewContainer: ViewContainerRef,
    private authService: Auth
  ) {}

  ngOnInit() {
    // Listen to user changes
    this.authService.currentUser$
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        this.updateView();
      });
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private updateView() {
    const user = this.authService.getCurrentUser();
    let hasAccess = false;

    if (user) {
      // Check roles if specified
      if (this.roles.length > 0) {
        hasAccess = this.roles.includes(user.role);
      }

      // Check permissions if specified (AND logic with roles)
      if (this.permissions.length > 0) {
        const hasPermission = this.authService.hasAnyPermission(this.permissions);
        hasAccess = this.roles.length > 0 ? (hasAccess && hasPermission) : hasPermission;
      }

      // If no roles or permissions specified, just check if user is logged in
      if (this.roles.length === 0 && this.permissions.length === 0) {
        hasAccess = true;
      }
    }

    if (hasAccess) {
      this.viewContainer.createEmbeddedView(this.templateRef);
    } else {
      this.viewContainer.clear();
    }
  }
}
