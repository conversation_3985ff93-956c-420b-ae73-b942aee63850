const express = require('express');
const Appointment = require('../models/Appointment');
const Patient = require('../models/Patient');
const Doctor = require('../models/Doctor');
const { authenticateToken, requirePermissions, authorizeRoles } = require('../middlewares/auth');
const router = express.Router();

// Apply authentication to all routes
router.use(authenticateToken);

// Get all appointments (Role-based access)
router.get('/', async (req, res) => {
  try {
    const { page = 1, limit = 10, status = '', date = '', doctorId = '', patientId = '' } = req.query;

    let query = {};

    // Role-based filtering
    if (req.user.role === 'Patient') {
      query.patient = req.user._id;
    } else if (req.user.role === 'Doctor') {
      query.doctor = req.user._id;
    }
    // Admin can see all appointments

    if (status) {
      query.status = status;
    }

    if (date) {
      const startDate = new Date(date);
      const endDate = new Date(date);
      endDate.setDate(endDate.getDate() + 1);
      query.appointmentDate = { $gte: startDate, $lt: endDate };
    }

    if (doctorId && req.user.role !== 'Patient') {
      query.doctor = doctorId;
    }

    if (patientId && req.user.role !== 'Patient') {
      query.patient = patientId;
    }

    const appointments = await Appointment.find(query)
      .populate('patient', 'firstName lastName email phone patientId')
      .populate('doctor', 'firstName lastName specialization department doctorId')
      .limit(limit * 1)
      .skip((page - 1) * limit)
      .sort({ appointmentDate: 1, appointmentTime: 1 });

    const total = await Appointment.countDocuments(query);

    res.json({
      appointments,
      totalPages: Math.ceil(total / limit),
      currentPage: page,
      total
    });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

// Get appointment by ID (Role-based access)
router.get('/:id', async (req, res) => {
  try {
    let query = { _id: req.params.id };

    // Role-based access control
    if (req.user.role === 'Patient') {
      query.patient = req.user._id;
    } else if (req.user.role === 'Doctor') {
      query.doctor = req.user._id;
    }

    const appointment = await Appointment.findOne(query)
      .populate('patient')
      .populate('doctor');

    if (!appointment) {
      return res.status(404).json({ message: 'Appointment not found or access denied' });
    }
    res.json(appointment);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

// Create new appointment (Admin and Doctors only)
router.post('/', requirePermissions('manage_appointments', 'view_appointments'), async (req, res) => {
  try {
    const { patient, doctor, appointmentDate, appointmentTime } = req.body;

    // Verify patient exists
    const patientExists = await Patient.findById(patient);
    if (!patientExists) {
      return res.status(404).json({ message: 'Patient not found' });
    }

    // Verify doctor exists
    const doctorExists = await Doctor.findById(doctor);
    if (!doctorExists) {
      return res.status(404).json({ message: 'Doctor not found' });
    }

    // Check for conflicting appointments
    const conflictingAppointment = await Appointment.findOne({
      doctor,
      appointmentDate: new Date(appointmentDate),
      appointmentTime,
      status: { $nin: ['Cancelled', 'Completed'] }
    });

    if (conflictingAppointment) {
      return res.status(400).json({ message: 'Doctor is not available at this time slot' });
    }

    // Set consultation fee from doctor's profile
    req.body.consultationFee = doctorExists.consultationFee;

    const appointment = new Appointment(req.body);
    await appointment.save();

    // Populate the created appointment
    await appointment.populate('patient', 'firstName lastName email phone patientId');
    await appointment.populate('doctor', 'firstName lastName specialization department doctorId');

    res.status(201).json({
      message: 'Appointment created successfully',
      appointment
    });
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
});

// Update appointment
router.put('/:id', async (req, res) => {
  try {
    const appointment = await Appointment.findByIdAndUpdate(
      req.params.id,
      req.body,
      { new: true, runValidators: true }
    ).populate('patient', 'firstName lastName email phone patientId')
     .populate('doctor', 'firstName lastName specialization department doctorId');

    if (!appointment) {
      return res.status(404).json({ message: 'Appointment not found' });
    }

    res.json({
      message: 'Appointment updated successfully',
      appointment
    });
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
});

// Cancel appointment
router.patch('/:id/cancel', async (req, res) => {
  try {
    const appointment = await Appointment.findByIdAndUpdate(
      req.params.id,
      { status: 'Cancelled' },
      { new: true }
    ).populate('patient', 'firstName lastName email phone patientId')
     .populate('doctor', 'firstName lastName specialization department doctorId');

    if (!appointment) {
      return res.status(404).json({ message: 'Appointment not found' });
    }

    res.json({
      message: 'Appointment cancelled successfully',
      appointment
    });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

// Complete appointment with prescription
router.patch('/:id/complete', async (req, res) => {
  try {
    const { prescription, notes, followUpDate } = req.body;

    const appointment = await Appointment.findByIdAndUpdate(
      req.params.id,
      {
        status: 'Completed',
        prescription,
        notes,
        followUpDate
      },
      { new: true }
    ).populate('patient', 'firstName lastName email phone patientId')
     .populate('doctor', 'firstName lastName specialization department doctorId');

    if (!appointment) {
      return res.status(404).json({ message: 'Appointment not found' });
    }

    res.json({
      message: 'Appointment completed successfully',
      appointment
    });
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
});

// Get appointments by date range
router.get('/date-range/:startDate/:endDate', async (req, res) => {
  try {
    const { startDate, endDate } = req.params;

    const appointments = await Appointment.find({
      appointmentDate: {
        $gte: new Date(startDate),
        $lte: new Date(endDate)
      }
    }).populate('patient', 'firstName lastName patientId')
      .populate('doctor', 'firstName lastName doctorId')
      .sort({ appointmentDate: 1, appointmentTime: 1 });

    res.json(appointments);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

module.exports = router;
