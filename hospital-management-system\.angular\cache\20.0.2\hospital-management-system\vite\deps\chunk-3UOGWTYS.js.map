{"version": 3, "sources": ["../../../../../../node_modules/@angular/material/fesm2022/structural-styles-CObeNzjn.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, ChangeDetectionStrategy } from '@angular/core';\n\n/**\n * Component used to load structural styles for focus indicators.\n * @docs-private\n */\nclass _StructuralStylesLoader {\n  static ɵfac = function _StructuralStylesLoader_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || _StructuralStylesLoader)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: _StructuralStylesLoader,\n    selectors: [[\"structural-styles\"]],\n    decls: 0,\n    vars: 0,\n    template: function _StructuralStylesLoader_Template(rf, ctx) {},\n    styles: [\".mat-focus-indicator{position:relative}.mat-focus-indicator::before{top:0;left:0;right:0;bottom:0;position:absolute;box-sizing:border-box;pointer-events:none;display:var(--mat-focus-indicator-display, none);border-width:var(--mat-focus-indicator-border-width, 3px);border-style:var(--mat-focus-indicator-border-style, solid);border-color:var(--mat-focus-indicator-border-color, transparent);border-radius:var(--mat-focus-indicator-border-radius, 4px)}.mat-focus-indicator:focus::before{content:\\\"\\\"}@media(forced-colors: active){html{--mat-focus-indicator-display: block}}\\n\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_StructuralStylesLoader, [{\n    type: Component,\n    args: [{\n      selector: 'structural-styles',\n      encapsulation: ViewEncapsulation.None,\n      template: '',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      styles: [\".mat-focus-indicator{position:relative}.mat-focus-indicator::before{top:0;left:0;right:0;bottom:0;position:absolute;box-sizing:border-box;pointer-events:none;display:var(--mat-focus-indicator-display, none);border-width:var(--mat-focus-indicator-border-width, 3px);border-style:var(--mat-focus-indicator-border-style, solid);border-color:var(--mat-focus-indicator-border-color, transparent);border-radius:var(--mat-focus-indicator-border-radius, 4px)}.mat-focus-indicator:focus::before{content:\\\"\\\"}@media(forced-colors: active){html{--mat-focus-indicator-display: block}}\\n\"]\n    }]\n  }], null, null);\n})();\nexport { _StructuralStylesLoader as _ };\n"], "mappings": ";;;;;;;;;AAOA,IAAM,0BAAN,MAAM,yBAAwB;AAAA,EAC5B,OAAO,OAAO,SAAS,gCAAgC,mBAAmB;AACxE,WAAO,KAAK,qBAAqB,0BAAyB;AAAA,EAC5D;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,mBAAmB,CAAC;AAAA,IACjC,OAAO;AAAA,IACP,MAAM;AAAA,IACN,UAAU,SAAS,iCAAiC,IAAI,KAAK;AAAA,IAAC;AAAA,IAC9D,QAAQ,CAAC,8jBAAgkB;AAAA,IACzkB,eAAe;AAAA,IACf,iBAAiB;AAAA,EACnB,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,yBAAyB,CAAC;AAAA,IAChG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,eAAe,kBAAkB;AAAA,MACjC,UAAU;AAAA,MACV,iBAAiB,wBAAwB;AAAA,MACzC,QAAQ,CAAC,8jBAAgkB;AAAA,IAC3kB,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": []}