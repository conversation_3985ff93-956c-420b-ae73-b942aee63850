import {
  MAT_SELECT_CONFIG,
  MAT_SELECT_SCROLL_STRATEGY,
  MAT_SELECT_SCROLL_STRATEGY_PROVIDER,
  MAT_SELECT_SCROLL_STRATEGY_PROVIDER_FACTORY,
  MAT_SELECT_TRIGGER,
  MatSelect,
  MatSelectChange,
  MatSelectModule,
  MatSelectTrigger
} from "./chunk-5I63D5YL.js";
import "./chunk-5MTCGFJI.js";
import "./chunk-DTFGSOA6.js";
import "./chunk-JKRSZR5T.js";
import "./chunk-JZB4QUID.js";
import {
  MatOptgroup,
  MatOption
} from "./chunk-BKXNVHHK.js";
import "./chunk-B5TW7NVN.js";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>ield,
  <PERSON>Hint,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>refix,
  MatSuffix
} from "./chunk-THQVE6PH.js";
import "./chunk-Y5UE37C3.js";
import "./chunk-RROAL5FS.js";
import "./chunk-R6NQQL3W.js";
import "./chunk-WF7PEYZY.js";
import "./chunk-3UOGWTYS.js";
import "./chunk-ZS3UZZRT.js";
import "./chunk-TIGMP6SS.js";
import "./chunk-JVGIZRHP.js";
import "./chunk-DQ7OVFPD.js";
import "./chunk-QCETVJKM.js";
import "./chunk-PFNGKGXN.js";
import "./chunk-EMNQZNBI.js";
import "./chunk-EOFW2REK.js";
import "./chunk-IBHZL2GR.js";
import "./chunk-FSTFWO55.js";
import "./chunk-GDTG4QJU.js";
import "./chunk-FHT3VUCU.js";
import "./chunk-JU6HWCPU.js";
import "./chunk-RQTSQUJ5.js";
import "./chunk-KQRM5WX6.js";
import "./chunk-J7HUVZB5.js";
import "./chunk-UXMLNMB3.js";
import "./chunk-LNIFJL4T.js";
import "./chunk-KVPM5A4E.js";

// node_modules/@angular/material/fesm2022/select.mjs
var matSelectAnimations = {
  // Represents
  // trigger('transformPanel', [
  //   state(
  //     'void',
  //     style({
  //       opacity: 0,
  //       transform: 'scale(1, 0.8)',
  //     }),
  //   ),
  //   transition(
  //     'void => showing',
  //     animate(
  //       '120ms cubic-bezier(0, 0, 0.2, 1)',
  //       style({
  //         opacity: 1,
  //         transform: 'scale(1, 1)',
  //       }),
  //     ),
  //   ),
  //   transition('* => void', animate('100ms linear', style({opacity: 0}))),
  // ])
  /** This animation transforms the select's overlay panel on and off the page. */
  transformPanel: {
    type: 7,
    name: "transformPanel",
    definitions: [
      {
        type: 0,
        name: "void",
        styles: {
          type: 6,
          styles: { opacity: 0, transform: "scale(1, 0.8)" },
          offset: null
        }
      },
      {
        type: 1,
        expr: "void => showing",
        animation: {
          type: 4,
          styles: {
            type: 6,
            styles: { opacity: 1, transform: "scale(1, 1)" },
            offset: null
          },
          timings: "120ms cubic-bezier(0, 0, 0.2, 1)"
        },
        options: null
      },
      {
        type: 1,
        expr: "* => void",
        animation: {
          type: 4,
          styles: { type: 6, styles: { opacity: 0 }, offset: null },
          timings: "100ms linear"
        },
        options: null
      }
    ],
    options: {}
  }
};
export {
  MAT_SELECT_CONFIG,
  MAT_SELECT_SCROLL_STRATEGY,
  MAT_SELECT_SCROLL_STRATEGY_PROVIDER,
  MAT_SELECT_SCROLL_STRATEGY_PROVIDER_FACTORY,
  MAT_SELECT_TRIGGER,
  MatError,
  MatFormField,
  MatHint,
  MatLabel,
  MatOptgroup,
  MatOption,
  MatPrefix,
  MatSelect,
  MatSelectChange,
  MatSelectModule,
  MatSelectTrigger,
  MatSuffix,
  matSelectAnimations
};
//# sourceMappingURL=@angular_material_select.js.map
