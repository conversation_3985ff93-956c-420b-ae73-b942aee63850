@use '../core/theming/inspection';
@use '../core/style/sass-utils';
@use '../core/tokens/m2-utils';

// Tokens that can't be configured through Angular Material's current theming API,
// but may be in a future version of the theming API.
//
// Tokens that are available in MDC, but not used in Angular Material should be mapped to `null`.
// `null` indicates that we are intentionally choosing not to emit a slot or value for the token in
// our CSS.
@function get-unthemable-tokens() {
  @return (
    tooltip-container-shape: 4px,
    tooltip-supporting-text-line-height: 16px,
  );
}

// Tokens that can be configured through Angular Material's color theming API.
@function get-color-tokens($theme) {

  @return (
    tooltip-container-color: inspection.get-theme-color($theme, background, tooltip),
    tooltip-supporting-text-color: #fff,
  );
}

// Tokens that can be configured through Angular Material's typography theming API.
@function get-typography-tokens($theme) {
  @return (
    tooltip-supporting-text-font: inspection.get-theme-typography($theme, caption, font-family),
    tooltip-supporting-text-size: inspection.get-theme-typography($theme, caption, font-size),
    tooltip-supporting-text-weight: inspection.get-theme-typography($theme, caption, font-weight),
    tooltip-supporting-text-tracking:
        inspection.get-theme-typography($theme, caption, letter-spacing),
  );
}

// Tokens that can be configured through Angular Material's density theming API.
@function get-density-tokens($theme) {
  @return ();
}

// Combines the tokens generated by the above functions into a single map with placeholder values.
// This is used to create token slots.
@function get-token-slots() {
  @return sass-utils.deep-merge-all(
      get-unthemable-tokens(),
      get-color-tokens(m2-utils.$placeholder-color-config),
      get-typography-tokens(m2-utils.$placeholder-typography-config),
      get-density-tokens(m2-utils.$placeholder-density-config)
  );
}
