{"version": 3, "file": "module-Ce6F7TNm.mjs", "sources": ["../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/material/dialog/dialog-config.ts", "../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/material/dialog/dialog-container.ts", "../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/material/dialog/dialog-container.html", "../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/material/dialog/dialog-ref.ts", "../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/material/dialog/dialog.ts", "../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/material/dialog/dialog-content-directives.ts", "../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/material/dialog/module.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {ViewContainerRef, Injector} from '@angular/core';\nimport {Direction} from '@angular/cdk/bidi';\nimport {ScrollStrategy} from '@angular/cdk/overlay';\nimport {DialogConfig} from '@angular/cdk/dialog';\nimport {_defaultParams} from './dialog-animations';\n\n/** Options for where to set focus to automatically on dialog open */\nexport type AutoFocusTarget = 'dialog' | 'first-tabbable' | 'first-heading';\n\n/** Valid ARIA roles for a dialog element. */\nexport type DialogRole = 'dialog' | 'alertdialog';\n\n/** Possible overrides for a dialog's position. */\nexport interface DialogPosition {\n  /** Override for the dialog's top position. */\n  top?: string;\n\n  /** Override for the dialog's bottom position. */\n  bottom?: string;\n\n  /** Override for the dialog's left position. */\n  left?: string;\n\n  /** Override for the dialog's right position. */\n  right?: string;\n}\n\n/**\n * Configuration for opening a modal dialog with the MatDialog service.\n */\nexport class MatDialogConfig<D = any> {\n  /**\n   * Where the attached component should live in Angular's *logical* component tree.\n   * This affects what is available for injection and the change detection order for the\n   * component instantiated inside of the dialog. This does not affect where the dialog\n   * content will be rendered.\n   */\n  viewContainerRef?: ViewContainerRef;\n\n  /**\n   * Injector used for the instantiation of the component to be attached. If provided,\n   * takes precedence over the injector indirectly provided by `ViewContainerRef`.\n   */\n  injector?: Injector;\n\n  /** ID for the dialog. If omitted, a unique one will be generated. */\n  id?: string;\n\n  /** The ARIA role of the dialog element. */\n  role?: DialogRole = 'dialog';\n\n  /** Custom class for the overlay pane. */\n  panelClass?: string | string[] = '';\n\n  /** Whether the dialog has a backdrop. */\n  hasBackdrop?: boolean = true;\n\n  /** Custom class for the backdrop. */\n  backdropClass?: string | string[] = '';\n\n  /** Whether the user can use escape or clicking on the backdrop to close the modal. */\n  disableClose?: boolean = false;\n\n  /** Function used to determine whether the dialog is allowed to close. */\n  closePredicate?: <\n    Result = unknown,\n    Component = unknown,\n    Config extends DialogConfig = MatDialogConfig,\n  >(\n    result: Result | undefined,\n    config: Config,\n    componentInstance: Component | null,\n  ) => boolean;\n\n  /** Width of the dialog. */\n  width?: string = '';\n\n  /** Height of the dialog. */\n  height?: string = '';\n\n  /** Min-width of the dialog. If a number is provided, assumes pixel units. */\n  minWidth?: number | string;\n\n  /** Min-height of the dialog. If a number is provided, assumes pixel units. */\n  minHeight?: number | string;\n\n  /** Max-width of the dialog. If a number is provided, assumes pixel units. */\n  maxWidth?: number | string;\n\n  /** Max-height of the dialog. If a number is provided, assumes pixel units. */\n  maxHeight?: number | string;\n\n  /** Position overrides. */\n  position?: DialogPosition;\n\n  /** Data being injected into the child component. */\n  data?: D | null = null;\n\n  /** Layout direction for the dialog's content. */\n  direction?: Direction;\n\n  /** ID of the element that describes the dialog. */\n  ariaDescribedBy?: string | null = null;\n\n  /** ID of the element that labels the dialog. */\n  ariaLabelledBy?: string | null = null;\n\n  /** Aria label to assign to the dialog element. */\n  ariaLabel?: string | null = null;\n\n  /**\n   * Whether this is a modal dialog. Used to set the `aria-modal` attribute. Off by default,\n   * because it can interfere with other overlay-based components (e.g. `mat-select`) and because\n   * it is redundant since the dialog marks all outside content as `aria-hidden` anyway.\n   */\n  ariaModal?: boolean = false;\n\n  /**\n   * Where the dialog should focus on open.\n   * @breaking-change 14.0.0 Remove boolean option from autoFocus. Use string or\n   * AutoFocusTarget instead.\n   */\n  autoFocus?: AutoFocusTarget | string | boolean = 'first-tabbable';\n\n  /**\n   * Whether the dialog should restore focus to the\n   * previously-focused element, after it's closed.\n   */\n  restoreFocus?: boolean = true;\n\n  /** Whether to wait for the opening animation to finish before trapping focus. */\n  delayFocusTrap?: boolean = true;\n\n  /** Scroll strategy to be used for the dialog. */\n  scrollStrategy?: ScrollStrategy;\n\n  /**\n   * Whether the dialog should close when the user goes backwards/forwards in history.\n   * Note that this usually doesn't include clicking on links (unless the user is using\n   * the `HashLocationStrategy`).\n   */\n  closeOnNavigation?: boolean = true;\n\n  /**\n   * Duration of the enter animation in ms.\n   * Should be a number, string type is deprecated.\n   * @breaking-change 17.0.0 Remove string signature.\n   */\n  enterAnimationDuration?: string | number;\n\n  /**\n   * Duration of the exit animation in ms.\n   * Should be a number, string type is deprecated.\n   * @breaking-change 17.0.0 Remove string signature.\n   */\n  exitAnimationDuration?: string | number;\n\n  // TODO(jelbourn): add configuration for lifecycle hooks, ARIA labelling.\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  ChangeDetectionStrategy,\n  Component,\n  ComponentRef,\n  EventEmitter,\n  OnDestroy,\n  ViewEncapsulation,\n} from '@angular/core';\nimport {MatDialogConfig} from './dialog-config';\nimport {CdkDialogContainer} from '@angular/cdk/dialog';\nimport {coerceNumberProperty} from '@angular/cdk/coercion';\nimport {CdkPortalOutlet, ComponentPortal} from '@angular/cdk/portal';\nimport {_animationsDisabled} from '../core/animation/animation';\n\n/** Event that captures the state of dialog container animations. */\ninterface LegacyDialogAnimationEvent {\n  state: 'opened' | 'opening' | 'closing' | 'closed';\n  totalTime: number;\n}\n\n/** Class added when the dialog is open. */\nconst OPEN_CLASS = 'mdc-dialog--open';\n\n/** Class added while the dialog is opening. */\nconst OPENING_CLASS = 'mdc-dialog--opening';\n\n/** Class added while the dialog is closing. */\nconst CLOSING_CLASS = 'mdc-dialog--closing';\n\n/** Duration of the opening animation in milliseconds. */\nexport const OPEN_ANIMATION_DURATION = 150;\n\n/** Duration of the closing animation in milliseconds. */\nexport const CLOSE_ANIMATION_DURATION = 75;\n\n@Component({\n  selector: 'mat-dialog-container',\n  templateUrl: 'dialog-container.html',\n  styleUrl: 'dialog.css',\n  encapsulation: ViewEncapsulation.None,\n  // Disabled for consistency with the non-MDC dialog container.\n  // tslint:disable-next-line:validate-decorators\n  changeDetection: ChangeDetectionStrategy.Default,\n  imports: [CdkPortalOutlet],\n  host: {\n    'class': 'mat-mdc-dialog-container mdc-dialog',\n    'tabindex': '-1',\n    '[attr.aria-modal]': '_config.ariaModal',\n    '[id]': '_config.id',\n    '[attr.role]': '_config.role',\n    '[attr.aria-labelledby]': '_config.ariaLabel ? null : _ariaLabelledByQueue[0]',\n    '[attr.aria-label]': '_config.ariaLabel',\n    '[attr.aria-describedby]': '_config.ariaDescribedBy || null',\n    '[class._mat-animation-noopable]': '!_animationsEnabled',\n    '[class.mat-mdc-dialog-container-with-actions]': '_actionSectionCount > 0',\n  },\n})\nexport class MatDialogContainer extends CdkDialogContainer<MatDialogConfig> implements OnDestroy {\n  /** Emits when an animation state changes. */\n  _animationStateChanged = new EventEmitter<LegacyDialogAnimationEvent>();\n\n  /** Whether animations are enabled. */\n  _animationsEnabled = !_animationsDisabled();\n\n  /** Number of actions projected in the dialog. */\n  protected _actionSectionCount = 0;\n\n  /** Host element of the dialog container component. */\n  private _hostElement: HTMLElement = this._elementRef.nativeElement;\n  /** Duration of the dialog open animation. */\n  private _enterAnimationDuration = this._animationsEnabled\n    ? (parseCssTime(this._config.enterAnimationDuration) ?? OPEN_ANIMATION_DURATION)\n    : 0;\n  /** Duration of the dialog close animation. */\n  private _exitAnimationDuration = this._animationsEnabled\n    ? (parseCssTime(this._config.exitAnimationDuration) ?? CLOSE_ANIMATION_DURATION)\n    : 0;\n  /** Current timer for dialog animations. */\n  private _animationTimer: ReturnType<typeof setTimeout> | null = null;\n\n  protected override _contentAttached(): void {\n    // Delegate to the original dialog-container initialization (i.e. saving the\n    // previous element, setting up the focus trap and moving focus to the container).\n    super._contentAttached();\n\n    // Note: Usually we would be able to use the MDC dialog foundation here to handle\n    // the dialog animation for us, but there are a few reasons why we just leverage\n    // their styles and not use the runtime foundation code:\n    //   1. Foundation does not allow us to disable animations.\n    //   2. Foundation contains unnecessary features we don't need and aren't\n    //      tree-shakeable. e.g. background scrim, keyboard event handlers for ESC button.\n    this._startOpenAnimation();\n  }\n\n  /** Starts the dialog open animation if enabled. */\n  private _startOpenAnimation() {\n    this._animationStateChanged.emit({state: 'opening', totalTime: this._enterAnimationDuration});\n\n    if (this._animationsEnabled) {\n      this._hostElement.style.setProperty(\n        TRANSITION_DURATION_PROPERTY,\n        `${this._enterAnimationDuration}ms`,\n      );\n\n      // We need to give the `setProperty` call from above some time to be applied.\n      // One would expect that the open class is added once the animation finished, but MDC\n      // uses the open class in combination with the opening class to start the animation.\n      this._requestAnimationFrame(() => this._hostElement.classList.add(OPENING_CLASS, OPEN_CLASS));\n      this._waitForAnimationToComplete(this._enterAnimationDuration, this._finishDialogOpen);\n    } else {\n      this._hostElement.classList.add(OPEN_CLASS);\n      // Note: We could immediately finish the dialog opening here with noop animations,\n      // but we defer until next tick so that consumers can subscribe to `afterOpened`.\n      // Executing this immediately would mean that `afterOpened` emits synchronously\n      // on `dialog.open` before the consumer had a change to subscribe to `afterOpened`.\n      Promise.resolve().then(() => this._finishDialogOpen());\n    }\n  }\n\n  /**\n   * Starts the exit animation of the dialog if enabled. This method is\n   * called by the dialog ref.\n   */\n  _startExitAnimation(): void {\n    this._animationStateChanged.emit({state: 'closing', totalTime: this._exitAnimationDuration});\n    this._hostElement.classList.remove(OPEN_CLASS);\n\n    if (this._animationsEnabled) {\n      this._hostElement.style.setProperty(\n        TRANSITION_DURATION_PROPERTY,\n        `${this._exitAnimationDuration}ms`,\n      );\n\n      // We need to give the `setProperty` call from above some time to be applied.\n      this._requestAnimationFrame(() => this._hostElement.classList.add(CLOSING_CLASS));\n      this._waitForAnimationToComplete(this._exitAnimationDuration, this._finishDialogClose);\n    } else {\n      // This subscription to the `OverlayRef#backdropClick` observable in the `DialogRef` is\n      // set up before any user can subscribe to the backdrop click. The subscription triggers\n      // the dialog close and this method synchronously. If we'd synchronously emit the `CLOSED`\n      // animation state event if animations are disabled, the overlay would be disposed\n      // immediately and all other subscriptions to `DialogRef#backdropClick` would be silently\n      // skipped. We work around this by waiting with the dialog close until the next tick when\n      // all subscriptions have been fired as expected. This is not an ideal solution, but\n      // there doesn't seem to be any other good way. Alternatives that have been considered:\n      //   1. Deferring `DialogRef.close`. This could be a breaking change due to a new microtask.\n      //      Also this issue is specific to the MDC implementation where the dialog could\n      //      technically be closed synchronously. In the non-MDC one, Angular animations are used\n      //      and closing always takes at least a tick.\n      //   2. Ensuring that user subscriptions to `backdropClick`, `keydownEvents` in the dialog\n      //      ref are first. This would solve the issue, but has the risk of memory leaks and also\n      //      doesn't solve the case where consumers call `DialogRef.close` in their subscriptions.\n      // Based on the fact that this is specific to the MDC-based implementation of the dialog\n      // animations, the defer is applied here.\n      Promise.resolve().then(() => this._finishDialogClose());\n    }\n  }\n\n  /**\n   * Updates the number action sections.\n   * @param delta Increase/decrease in the number of sections.\n   */\n  _updateActionSectionCount(delta: number) {\n    this._actionSectionCount += delta;\n    this._changeDetectorRef.markForCheck();\n  }\n\n  /**\n   * Completes the dialog open by clearing potential animation classes, trapping\n   * focus and emitting an opened event.\n   */\n  private _finishDialogOpen = () => {\n    this._clearAnimationClasses();\n    this._openAnimationDone(this._enterAnimationDuration);\n  };\n\n  /**\n   * Completes the dialog close by clearing potential animation classes, restoring\n   * focus and emitting a closed event.\n   */\n  private _finishDialogClose = () => {\n    this._clearAnimationClasses();\n    this._animationStateChanged.emit({state: 'closed', totalTime: this._exitAnimationDuration});\n  };\n\n  /** Clears all dialog animation classes. */\n  private _clearAnimationClasses() {\n    this._hostElement.classList.remove(OPENING_CLASS, CLOSING_CLASS);\n  }\n\n  private _waitForAnimationToComplete(duration: number, callback: () => void) {\n    if (this._animationTimer !== null) {\n      clearTimeout(this._animationTimer);\n    }\n\n    // Note that we want this timer to run inside the NgZone, because we want\n    // the related events like `afterClosed` to be inside the zone as well.\n    this._animationTimer = setTimeout(callback, duration);\n  }\n\n  /** Runs a callback in `requestAnimationFrame`, if available. */\n  private _requestAnimationFrame(callback: () => void) {\n    this._ngZone.runOutsideAngular(() => {\n      if (typeof requestAnimationFrame === 'function') {\n        requestAnimationFrame(callback);\n      } else {\n        callback();\n      }\n    });\n  }\n\n  protected override _captureInitialFocus(): void {\n    if (!this._config.delayFocusTrap) {\n      this._trapFocus();\n    }\n  }\n\n  /**\n   * Callback for when the open dialog animation has finished. Intended to\n   * be called by sub-classes that use different animation implementations.\n   */\n  protected _openAnimationDone(totalTime: number) {\n    if (this._config.delayFocusTrap) {\n      this._trapFocus();\n    }\n\n    this._animationStateChanged.next({state: 'opened', totalTime});\n  }\n\n  override ngOnDestroy() {\n    super.ngOnDestroy();\n\n    if (this._animationTimer !== null) {\n      clearTimeout(this._animationTimer);\n    }\n  }\n\n  override attachComponentPortal<T>(portal: ComponentPortal<T>): ComponentRef<T> {\n    // When a component is passed into the dialog, the host element interrupts\n    // the `display:flex` from affecting the dialog title, content, and\n    // actions. To fix this, we make the component host `display: contents` by\n    // marking its host with the `mat-mdc-dialog-component-host` class.\n    //\n    // Note that this problem does not exist when a template ref is used since\n    // the title, contents, and actions are then nested directly under the\n    // dialog surface.\n    const ref = super.attachComponentPortal(portal);\n    ref.location.nativeElement.classList.add('mat-mdc-dialog-component-host');\n    return ref;\n  }\n}\n\nconst TRANSITION_DURATION_PROPERTY = '--mat-dialog-transition-duration';\n\n// TODO(mmalerba): Remove this function after animation durations are required\n//  to be numbers.\n/**\n * Converts a CSS time string to a number in ms. If the given time is already a\n * number, it is assumed to be in ms.\n */\nfunction parseCssTime(time: string | number | undefined): number | null {\n  if (time == null) {\n    return null;\n  }\n  if (typeof time === 'number') {\n    return time;\n  }\n  if (time.endsWith('ms')) {\n    return coerceNumberProperty(time.substring(0, time.length - 2));\n  }\n  if (time.endsWith('s')) {\n    return coerceNumberProperty(time.substring(0, time.length - 1)) * 1000;\n  }\n  if (time === '0') {\n    return 0;\n  }\n  return null; // anything else is invalid.\n}\n", "<div class=\"mat-mdc-dialog-inner-container mdc-dialog__container\">\n  <div class=\"mat-mdc-dialog-surface mdc-dialog__surface\">\n    <ng-template cdkPortalOutlet />\n  </div>\n</div>\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n/** Possible states of the lifecycle of a dialog. */\nimport {FocusOrigin} from '@angular/cdk/a11y';\nimport {merge, Observable, Subject} from 'rxjs';\nimport {DialogRef} from '@angular/cdk/dialog';\nimport {DialogPosition, MatDialogConfig} from './dialog-config';\nimport {MatDialogContainer} from './dialog-container';\nimport {filter, take} from 'rxjs/operators';\nimport {ESCAPE, hasModifierKey} from '@angular/cdk/keycodes';\nimport {GlobalPositionStrategy} from '@angular/cdk/overlay';\nimport {ComponentRef} from '@angular/core';\n\nexport enum MatDialogState {\n  OPEN,\n  CLOSING,\n  CLOSED,\n}\n\n/**\n * Reference to a dialog opened via the MatDialog service.\n */\nexport class MatDialogRef<T, R = any> {\n  /** The instance of component opened into the dialog. */\n  componentInstance: T;\n\n  /**\n   * `ComponentRef` of the component opened into the dialog. Will be\n   * null when the dialog is opened using a `TemplateRef`.\n   */\n  readonly componentRef: ComponentRef<T> | null;\n\n  /** Whether the user is allowed to close the dialog. */\n  disableClose: boolean | undefined;\n\n  /** Unique ID for the dialog. */\n  id: string;\n\n  /** Subject for notifying the user that the dialog has finished opening. */\n  private readonly _afterOpened = new Subject<void>();\n\n  /** Subject for notifying the user that the dialog has started closing. */\n  private readonly _beforeClosed = new Subject<R | undefined>();\n\n  /** Result to be passed to afterClosed. */\n  private _result: R | undefined;\n\n  /** Handle to the timeout that's running as a fallback in case the exit animation doesn't fire. */\n  private _closeFallbackTimeout: ReturnType<typeof setTimeout>;\n\n  /** Current state of the dialog. */\n  private _state = MatDialogState.OPEN;\n\n  // TODO(crisbeto): we shouldn't have to declare this property, because `DialogRef.close`\n  // already has a second `options` parameter that we can use. The problem is that internal tests\n  // have assertions like `expect(MatDialogRef.close).toHaveBeenCalledWith(foo)` which will break,\n  // because it'll be called with two arguments by things like `MatDialogClose`.\n  /** Interaction that caused the dialog to close. */\n  private _closeInteractionType: FocusOrigin | undefined;\n\n  constructor(\n    private _ref: DialogRef<R, T>,\n    private _config: MatDialogConfig,\n    public _containerInstance: MatDialogContainer,\n  ) {\n    this.disableClose = _config.disableClose;\n    this.id = _ref.id;\n\n    // Used to target panels specifically tied to dialogs.\n    _ref.addPanelClass('mat-mdc-dialog-panel');\n\n    // Emit when opening animation completes\n    _containerInstance._animationStateChanged\n      .pipe(\n        filter(event => event.state === 'opened'),\n        take(1),\n      )\n      .subscribe(() => {\n        this._afterOpened.next();\n        this._afterOpened.complete();\n      });\n\n    // Dispose overlay when closing animation is complete\n    _containerInstance._animationStateChanged\n      .pipe(\n        filter(event => event.state === 'closed'),\n        take(1),\n      )\n      .subscribe(() => {\n        clearTimeout(this._closeFallbackTimeout);\n        this._finishDialogClose();\n      });\n\n    _ref.overlayRef.detachments().subscribe(() => {\n      this._beforeClosed.next(this._result);\n      this._beforeClosed.complete();\n      this._finishDialogClose();\n    });\n\n    merge(\n      this.backdropClick(),\n      this.keydownEvents().pipe(\n        filter(event => event.keyCode === ESCAPE && !this.disableClose && !hasModifierKey(event)),\n      ),\n    ).subscribe(event => {\n      if (!this.disableClose) {\n        event.preventDefault();\n        _closeDialogVia(this, event.type === 'keydown' ? 'keyboard' : 'mouse');\n      }\n    });\n  }\n\n  /**\n   * Close the dialog.\n   * @param dialogResult Optional result to return to the dialog opener.\n   */\n  close(dialogResult?: R): void {\n    const closePredicate = this._config.closePredicate;\n\n    if (closePredicate && !closePredicate(dialogResult, this._config, this.componentInstance)) {\n      return;\n    }\n\n    this._result = dialogResult;\n\n    // Transition the backdrop in parallel to the dialog.\n    this._containerInstance._animationStateChanged\n      .pipe(\n        filter(event => event.state === 'closing'),\n        take(1),\n      )\n      .subscribe(event => {\n        this._beforeClosed.next(dialogResult);\n        this._beforeClosed.complete();\n        this._ref.overlayRef.detachBackdrop();\n\n        // The logic that disposes of the overlay depends on the exit animation completing, however\n        // it isn't guaranteed if the parent view is destroyed while it's running. Add a fallback\n        // timeout which will clean everything up if the animation hasn't fired within the specified\n        // amount of time plus 100ms. We don't need to run this outside the NgZone, because for the\n        // vast majority of cases the timeout will have been cleared before it has the chance to fire.\n        this._closeFallbackTimeout = setTimeout(\n          () => this._finishDialogClose(),\n          event.totalTime + 100,\n        );\n      });\n\n    this._state = MatDialogState.CLOSING;\n    this._containerInstance._startExitAnimation();\n  }\n\n  /**\n   * Gets an observable that is notified when the dialog is finished opening.\n   */\n  afterOpened(): Observable<void> {\n    return this._afterOpened;\n  }\n\n  /**\n   * Gets an observable that is notified when the dialog is finished closing.\n   */\n  afterClosed(): Observable<R | undefined> {\n    return this._ref.closed;\n  }\n\n  /**\n   * Gets an observable that is notified when the dialog has started closing.\n   */\n  beforeClosed(): Observable<R | undefined> {\n    return this._beforeClosed;\n  }\n\n  /**\n   * Gets an observable that emits when the overlay's backdrop has been clicked.\n   */\n  backdropClick(): Observable<MouseEvent> {\n    return this._ref.backdropClick;\n  }\n\n  /**\n   * Gets an observable that emits when keydown events are targeted on the overlay.\n   */\n  keydownEvents(): Observable<KeyboardEvent> {\n    return this._ref.keydownEvents;\n  }\n\n  /**\n   * Updates the dialog's position.\n   * @param position New dialog position.\n   */\n  updatePosition(position?: DialogPosition): this {\n    let strategy = this._ref.config.positionStrategy as GlobalPositionStrategy;\n\n    if (position && (position.left || position.right)) {\n      position.left ? strategy.left(position.left) : strategy.right(position.right);\n    } else {\n      strategy.centerHorizontally();\n    }\n\n    if (position && (position.top || position.bottom)) {\n      position.top ? strategy.top(position.top) : strategy.bottom(position.bottom);\n    } else {\n      strategy.centerVertically();\n    }\n\n    this._ref.updatePosition();\n\n    return this;\n  }\n\n  /**\n   * Updates the dialog's width and height.\n   * @param width New width of the dialog.\n   * @param height New height of the dialog.\n   */\n  updateSize(width: string = '', height: string = ''): this {\n    this._ref.updateSize(width, height);\n    return this;\n  }\n\n  /** Add a CSS class or an array of classes to the overlay pane. */\n  addPanelClass(classes: string | string[]): this {\n    this._ref.addPanelClass(classes);\n    return this;\n  }\n\n  /** Remove a CSS class or an array of classes from the overlay pane. */\n  removePanelClass(classes: string | string[]): this {\n    this._ref.removePanelClass(classes);\n    return this;\n  }\n\n  /** Gets the current state of the dialog's lifecycle. */\n  getState(): MatDialogState {\n    return this._state;\n  }\n\n  /**\n   * Finishes the dialog close by updating the state of the dialog\n   * and disposing the overlay.\n   */\n  private _finishDialogClose() {\n    this._state = MatDialogState.CLOSED;\n    this._ref.close(this._result, {focusOrigin: this._closeInteractionType});\n    this.componentInstance = null!;\n  }\n}\n\n/**\n * Closes the dialog with the specified interaction type. This is currently not part of\n * `MatDialogRef` as that would conflict with custom dialog ref mocks provided in tests.\n * More details. See: https://github.com/angular/components/pull/9257#issuecomment-651342226.\n */\n// TODO: Move this back into `MatDialogRef` when we provide an official mock dialog ref.\nexport function _closeDialogVia<R>(ref: MatDialogRef<R>, interactionType: FocusOrigin, result?: R) {\n  (ref as unknown as {_closeInteractionType: FocusOrigin})._closeInteractionType = interactionType;\n  return ref.close(result);\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  ComponentType,\n  createBlockScrollStrategy,\n  createGlobalPositionStrategy,\n  ScrollStrategy,\n} from '@angular/cdk/overlay';\nimport {\n  ComponentRef,\n  Injectable,\n  InjectionToken,\n  Injector,\n  OnDestroy,\n  TemplateRef,\n  Type,\n  inject,\n} from '@angular/core';\nimport {MatDialogConfig} from './dialog-config';\nimport {MatDialogContainer} from './dialog-container';\nimport {MatDialogRef} from './dialog-ref';\nimport {defer, Observable, Subject} from 'rxjs';\nimport {Dialog, DialogConfig} from '@angular/cdk/dialog';\nimport {startWith} from 'rxjs/operators';\nimport {_IdGenerator} from '@angular/cdk/a11y';\nimport {_animationsDisabled} from '../core';\n\n/** Injection token that can be used to access the data that was passed in to a dialog. */\nexport const MAT_DIALOG_DATA = new InjectionToken<any>('MatMdcDialogData');\n\n/** Injection token that can be used to specify default dialog options. */\nexport const MAT_DIALOG_DEFAULT_OPTIONS = new InjectionToken<MatDialogConfig>(\n  'mat-mdc-dialog-default-options',\n);\n\n/** Injection token that determines the scroll handling while the dialog is open. */\nexport const MAT_DIALOG_SCROLL_STRATEGY = new InjectionToken<() => ScrollStrategy>(\n  'mat-mdc-dialog-scroll-strategy',\n  {\n    providedIn: 'root',\n    factory: () => {\n      const injector = inject(Injector);\n      return () => createBlockScrollStrategy(injector);\n    },\n  },\n);\n\n/**\n * Service to open Material Design modal dialogs.\n */\n@Injectable({providedIn: 'root'})\nexport class MatDialog implements OnDestroy {\n  private _defaultOptions = inject<MatDialogConfig>(MAT_DIALOG_DEFAULT_OPTIONS, {optional: true});\n  private _scrollStrategy = inject(MAT_DIALOG_SCROLL_STRATEGY);\n  private _parentDialog = inject(MatDialog, {optional: true, skipSelf: true});\n  private _idGenerator = inject(_IdGenerator);\n  private _injector = inject(Injector);\n  protected _dialog = inject(Dialog);\n  private _animationsDisabled = _animationsDisabled();\n\n  private readonly _openDialogsAtThisLevel: MatDialogRef<any>[] = [];\n  private readonly _afterAllClosedAtThisLevel = new Subject<void>();\n  private readonly _afterOpenedAtThisLevel = new Subject<MatDialogRef<any>>();\n  protected dialogConfigClass = MatDialogConfig;\n\n  private readonly _dialogRefConstructor: Type<MatDialogRef<any>>;\n  private readonly _dialogContainerType: Type<MatDialogContainer>;\n  private readonly _dialogDataToken: InjectionToken<any>;\n\n  /** Keeps track of the currently-open dialogs. */\n  get openDialogs(): MatDialogRef<any>[] {\n    return this._parentDialog ? this._parentDialog.openDialogs : this._openDialogsAtThisLevel;\n  }\n\n  /** Stream that emits when a dialog has been opened. */\n  get afterOpened(): Subject<MatDialogRef<any>> {\n    return this._parentDialog ? this._parentDialog.afterOpened : this._afterOpenedAtThisLevel;\n  }\n\n  private _getAfterAllClosed(): Subject<void> {\n    const parent = this._parentDialog;\n    return parent ? parent._getAfterAllClosed() : this._afterAllClosedAtThisLevel;\n  }\n\n  /**\n   * Stream that emits when all open dialog have finished closing.\n   * Will emit on subscribe if there are no open dialogs to begin with.\n   */\n  readonly afterAllClosed: Observable<void> = defer(() =>\n    this.openDialogs.length\n      ? this._getAfterAllClosed()\n      : this._getAfterAllClosed().pipe(startWith(undefined)),\n  ) as Observable<any>;\n\n  constructor(...args: unknown[]);\n\n  constructor() {\n    this._dialogRefConstructor = MatDialogRef;\n    this._dialogContainerType = MatDialogContainer;\n    this._dialogDataToken = MAT_DIALOG_DATA;\n  }\n\n  /**\n   * Opens a modal dialog containing the given component.\n   * @param component Type of the component to load into the dialog.\n   * @param config Extra configuration options.\n   * @returns Reference to the newly-opened dialog.\n   */\n  open<T, D = any, R = any>(\n    component: ComponentType<T>,\n    config?: MatDialogConfig<D>,\n  ): MatDialogRef<T, R>;\n\n  /**\n   * Opens a modal dialog containing the given template.\n   * @param template TemplateRef to instantiate as the dialog content.\n   * @param config Extra configuration options.\n   * @returns Reference to the newly-opened dialog.\n   */\n  open<T, D = any, R = any>(\n    template: TemplateRef<T>,\n    config?: MatDialogConfig<D>,\n  ): MatDialogRef<T, R>;\n\n  open<T, D = any, R = any>(\n    template: ComponentType<T> | TemplateRef<T>,\n    config?: MatDialogConfig<D>,\n  ): MatDialogRef<T, R>;\n\n  open<T, D = any, R = any>(\n    componentOrTemplateRef: ComponentType<T> | TemplateRef<T>,\n    config?: MatDialogConfig<D>,\n  ): MatDialogRef<T, R> {\n    let dialogRef: MatDialogRef<T, R>;\n    config = {...(this._defaultOptions || new MatDialogConfig()), ...config};\n    config.id = config.id || this._idGenerator.getId('mat-mdc-dialog-');\n    config.scrollStrategy = config.scrollStrategy || this._scrollStrategy();\n\n    const cdkRef = this._dialog.open<R, D, T>(componentOrTemplateRef, {\n      ...config,\n      positionStrategy: createGlobalPositionStrategy(this._injector)\n        .centerHorizontally()\n        .centerVertically(),\n      // Disable closing since we need to sync it up to the animation ourselves.\n      disableClose: true,\n      // Closing is tied to our animation so the close predicate has to be implemented separately.\n      closePredicate: undefined,\n      // Disable closing on destroy, because this service cleans up its open dialogs as well.\n      // We want to do the cleanup here, rather than the CDK service, because the CDK destroys\n      // the dialogs immediately whereas we want it to wait for the animations to finish.\n      closeOnDestroy: false,\n      // Disable closing on detachments so that we can sync up the animation.\n      // The Material dialog ref handles this manually.\n      closeOnOverlayDetachments: false,\n      disableAnimations:\n        this._animationsDisabled ||\n        config.enterAnimationDuration?.toLocaleString() === '0' ||\n        config.exitAnimationDuration?.toString() === '0',\n      container: {\n        type: this._dialogContainerType,\n        providers: () => [\n          // Provide our config as the CDK config as well since it has the same interface as the\n          // CDK one, but it contains the actual values passed in by the user for things like\n          // `disableClose` which we disable for the CDK dialog since we handle it ourselves.\n          {provide: this.dialogConfigClass, useValue: config},\n          {provide: DialogConfig, useValue: config},\n        ],\n      },\n      templateContext: () => ({dialogRef}),\n      providers: (ref, cdkConfig, dialogContainer) => {\n        dialogRef = new this._dialogRefConstructor(ref, config, dialogContainer);\n        dialogRef.updatePosition(config?.position);\n        return [\n          {provide: this._dialogContainerType, useValue: dialogContainer},\n          {provide: this._dialogDataToken, useValue: cdkConfig.data},\n          {provide: this._dialogRefConstructor, useValue: dialogRef},\n        ];\n      },\n    });\n\n    // This can't be assigned in the `providers` callback, because\n    // the instance hasn't been assigned to the CDK ref yet.\n    (dialogRef! as {componentRef: ComponentRef<T>}).componentRef = cdkRef.componentRef!;\n    dialogRef!.componentInstance = cdkRef.componentInstance!;\n\n    this.openDialogs.push(dialogRef!);\n    this.afterOpened.next(dialogRef!);\n\n    dialogRef!.afterClosed().subscribe(() => {\n      const index = this.openDialogs.indexOf(dialogRef);\n\n      if (index > -1) {\n        this.openDialogs.splice(index, 1);\n\n        if (!this.openDialogs.length) {\n          this._getAfterAllClosed().next();\n        }\n      }\n    });\n\n    return dialogRef!;\n  }\n\n  /**\n   * Closes all of the currently-open dialogs.\n   */\n  closeAll(): void {\n    this._closeDialogs(this.openDialogs);\n  }\n\n  /**\n   * Finds an open dialog by its id.\n   * @param id ID to use when looking up the dialog.\n   */\n  getDialogById(id: string): MatDialogRef<any> | undefined {\n    return this.openDialogs.find(dialog => dialog.id === id);\n  }\n\n  ngOnDestroy() {\n    // Only close the dialogs at this level on destroy\n    // since the parent service may still be active.\n    this._closeDialogs(this._openDialogsAtThisLevel);\n    this._afterAllClosedAtThisLevel.complete();\n    this._afterOpenedAtThisLevel.complete();\n  }\n\n  private _closeDialogs(dialogs: MatDialogRef<any>[]) {\n    let i = dialogs.length;\n\n    while (i--) {\n      dialogs[i].close();\n    }\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {\n  Directive,\n  ElementRef,\n  Input,\n  OnChanges,\n  OnDestroy,\n  OnInit,\n  SimpleChanges,\n  inject,\n} from '@angular/core';\nimport {_IdGenerator} from '@angular/cdk/a11y';\nimport {CdkScrollable} from '@angular/cdk/scrolling';\n\nimport {MatDialog} from './dialog';\nimport {_closeDialogVia, MatDialogRef} from './dialog-ref';\n\n/**\n * Button that will close the current dialog.\n */\n@Directive({\n  selector: '[mat-dialog-close], [matDialogClose]',\n  exportAs: 'matDialogClose',\n  host: {\n    '(click)': '_onButtonClick($event)',\n    '[attr.aria-label]': 'ariaLabel || null',\n    '[attr.type]': 'type',\n  },\n})\nexport class MatDialogClose implements OnInit, OnChanges {\n  dialogRef = inject<MatDialogRef<any>>(MatDialogRef, {optional: true})!;\n  private _elementRef = inject<ElementRef<HTMLElement>>(ElementRef);\n  private _dialog = inject(MatDialog);\n\n  /** Screen-reader label for the button. */\n  @Input('aria-label') ariaLabel: string;\n\n  /** Default to \"button\" to prevents accidental form submits. */\n  @Input() type: 'submit' | 'button' | 'reset' = 'button';\n\n  /** Dialog close input. */\n  @Input('mat-dialog-close') dialogResult: any;\n\n  @Input('matDialogClose') _matDialogClose: any;\n\n  constructor(...args: unknown[]);\n  constructor() {}\n\n  ngOnInit() {\n    if (!this.dialogRef) {\n      // When this directive is included in a dialog via TemplateRef (rather than being\n      // in a Component), the DialogRef isn't available via injection because embedded\n      // views cannot be given a custom injector. Instead, we look up the DialogRef by\n      // ID. This must occur in `onInit`, as the ID binding for the dialog container won't\n      // be resolved at constructor time.\n      this.dialogRef = getClosestDialog(this._elementRef, this._dialog.openDialogs)!;\n    }\n  }\n\n  ngOnChanges(changes: SimpleChanges) {\n    const proxiedChange = changes['_matDialogClose'] || changes['_matDialogCloseResult'];\n\n    if (proxiedChange) {\n      this.dialogResult = proxiedChange.currentValue;\n    }\n  }\n\n  _onButtonClick(event: MouseEvent) {\n    // Determinate the focus origin using the click event, because using the FocusMonitor will\n    // result in incorrect origins. Most of the time, close buttons will be auto focused in the\n    // dialog, and therefore clicking the button won't result in a focus change. This means that\n    // the FocusMonitor won't detect any origin change, and will always output `program`.\n    _closeDialogVia(\n      this.dialogRef,\n      event.screenX === 0 && event.screenY === 0 ? 'keyboard' : 'mouse',\n      this.dialogResult,\n    );\n  }\n}\n\n@Directive()\nexport abstract class MatDialogLayoutSection implements OnInit, OnDestroy {\n  protected _dialogRef = inject<MatDialogRef<any>>(MatDialogRef, {optional: true})!;\n  private _elementRef = inject<ElementRef<HTMLElement>>(ElementRef);\n  private _dialog = inject(MatDialog);\n\n  constructor(...args: unknown[]);\n\n  constructor() {}\n\n  protected abstract _onAdd(): void;\n  protected abstract _onRemove(): void;\n\n  ngOnInit() {\n    if (!this._dialogRef) {\n      this._dialogRef = getClosestDialog(this._elementRef, this._dialog.openDialogs)!;\n    }\n\n    if (this._dialogRef) {\n      Promise.resolve().then(() => {\n        this._onAdd();\n      });\n    }\n  }\n\n  ngOnDestroy() {\n    // Note: we null check because there are some internal\n    // tests that are mocking out `MatDialogRef` incorrectly.\n    const instance = this._dialogRef?._containerInstance;\n\n    if (instance) {\n      Promise.resolve().then(() => {\n        this._onRemove();\n      });\n    }\n  }\n}\n\n/**\n * Title of a dialog element. Stays fixed to the top of the dialog when scrolling.\n */\n@Directive({\n  selector: '[mat-dialog-title], [matDialogTitle]',\n  exportAs: 'matDialogTitle',\n  host: {\n    'class': 'mat-mdc-dialog-title mdc-dialog__title',\n    '[id]': 'id',\n  },\n})\nexport class MatDialogTitle extends MatDialogLayoutSection {\n  @Input() id: string = inject(_IdGenerator).getId('mat-mdc-dialog-title-');\n\n  protected _onAdd() {\n    // Note: we null check the queue, because there are some internal\n    // tests that are mocking out `MatDialogRef` incorrectly.\n    this._dialogRef._containerInstance?._addAriaLabelledBy?.(this.id);\n  }\n\n  protected override _onRemove(): void {\n    this._dialogRef?._containerInstance?._removeAriaLabelledBy?.(this.id);\n  }\n}\n\n/**\n * Scrollable content container of a dialog.\n */\n@Directive({\n  selector: `[mat-dialog-content], mat-dialog-content, [matDialogContent]`,\n  host: {'class': 'mat-mdc-dialog-content mdc-dialog__content'},\n  hostDirectives: [CdkScrollable],\n})\nexport class MatDialogContent {}\n\n/**\n * Container for the bottom action buttons in a dialog.\n * Stays fixed to the bottom when scrolling.\n */\n@Directive({\n  selector: `[mat-dialog-actions], mat-dialog-actions, [matDialogActions]`,\n  host: {\n    'class': 'mat-mdc-dialog-actions mdc-dialog__actions',\n    '[class.mat-mdc-dialog-actions-align-start]': 'align === \"start\"',\n    '[class.mat-mdc-dialog-actions-align-center]': 'align === \"center\"',\n    '[class.mat-mdc-dialog-actions-align-end]': 'align === \"end\"',\n  },\n})\nexport class MatDialogActions extends MatDialogLayoutSection {\n  /**\n   * Horizontal alignment of action buttons.\n   */\n  @Input() align?: 'start' | 'center' | 'end';\n\n  protected _onAdd() {\n    this._dialogRef._containerInstance?._updateActionSectionCount?.(1);\n  }\n\n  protected override _onRemove(): void {\n    this._dialogRef._containerInstance?._updateActionSectionCount?.(-1);\n  }\n}\n\n/**\n * Finds the closest MatDialogRef to an element by looking at the DOM.\n * @param element Element relative to which to look for a dialog.\n * @param openDialogs References to the currently-open dialogs.\n */\nfunction getClosestDialog(element: ElementRef<HTMLElement>, openDialogs: MatDialogRef<any>[]) {\n  let parent: HTMLElement | null = element.nativeElement.parentElement;\n\n  while (parent && !parent.classList.contains('mat-mdc-dialog-container')) {\n    parent = parent.parentElement;\n  }\n\n  return parent ? openDialogs.find(dialog => dialog.id === parent!.id) : null;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {DialogModule} from '@angular/cdk/dialog';\nimport {OverlayModule} from '@angular/cdk/overlay';\nimport {PortalModule} from '@angular/cdk/portal';\nimport {NgModule} from '@angular/core';\nimport {MatCommonModule} from '../core';\nimport {MatDialog} from './dialog';\nimport {MatDialogContainer} from './dialog-container';\nimport {\n  MatDialogActions,\n  MatDialogClose,\n  MatDialogContent,\n  MatDialogTitle,\n} from './dialog-content-directives';\n\nconst DIRECTIVES = [\n  MatDialogContainer,\n  MatDialogClose,\n  MatDialogTitle,\n  MatDialogActions,\n  MatDialogContent,\n];\n\n@NgModule({\n  imports: [DialogModule, OverlayModule, PortalModule, MatCommonModule, ...DIRECTIVES],\n  exports: [MatCommonModule, ...DIRECTIVES],\n  providers: [MatDialog],\n})\nexport class MatDialogModule {}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAmCA;;AAEG;MACU,eAAe,CAAA;AAC1B;;;;;AAKG;AACH,IAAA,gBAAgB;AAEhB;;;AAGG;AACH,IAAA,QAAQ;;AAGR,IAAA,EAAE;;IAGF,IAAI,GAAgB,QAAQ;;IAG5B,UAAU,GAAuB,EAAE;;IAGnC,WAAW,GAAa,IAAI;;IAG5B,aAAa,GAAuB,EAAE;;IAGtC,YAAY,GAAa,KAAK;;AAG9B,IAAA,cAAc;;IAWd,KAAK,GAAY,EAAE;;IAGnB,MAAM,GAAY,EAAE;;AAGpB,IAAA,QAAQ;;AAGR,IAAA,SAAS;;AAGT,IAAA,QAAQ;;AAGR,IAAA,SAAS;;AAGT,IAAA,QAAQ;;IAGR,IAAI,GAAc,IAAI;;AAGtB,IAAA,SAAS;;IAGT,eAAe,GAAmB,IAAI;;IAGtC,cAAc,GAAmB,IAAI;;IAGrC,SAAS,GAAmB,IAAI;AAEhC;;;;AAIG;IACH,SAAS,GAAa,KAAK;AAE3B;;;;AAIG;IACH,SAAS,GAAwC,gBAAgB;AAEjE;;;AAGG;IACH,YAAY,GAAa,IAAI;;IAG7B,cAAc,GAAa,IAAI;;AAG/B,IAAA,cAAc;AAEd;;;;AAIG;IACH,iBAAiB,GAAa,IAAI;AAElC;;;;AAIG;AACH,IAAA,sBAAsB;AAEtB;;;;AAIG;AACH,IAAA,qBAAqB;AAGtB;;AC1ID;AACA,MAAM,UAAU,GAAG,kBAAkB;AAErC;AACA,MAAM,aAAa,GAAG,qBAAqB;AAE3C;AACA,MAAM,aAAa,GAAG,qBAAqB;AAE3C;AACO,MAAM,uBAAuB,GAAG,GAAG;AAE1C;AACO,MAAM,wBAAwB,GAAG,EAAE;AAwBpC,MAAO,kBAAmB,SAAQ,kBAAmC,CAAA;;AAEzE,IAAA,sBAAsB,GAAG,IAAI,YAAY,EAA8B;;AAGvE,IAAA,kBAAkB,GAAG,CAAC,mBAAmB,EAAE;;IAGjC,mBAAmB,GAAG,CAAC;;AAGzB,IAAA,YAAY,GAAgB,IAAI,CAAC,WAAW,CAAC,aAAa;;IAE1D,uBAAuB,GAAG,IAAI,CAAC;AACrC,WAAG,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,sBAAsB,CAAC,IAAI,uBAAuB;UAC7E,CAAC;;IAEG,sBAAsB,GAAG,IAAI,CAAC;AACpC,WAAG,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,qBAAqB,CAAC,IAAI,wBAAwB;UAC7E,CAAC;;IAEG,eAAe,GAAyC,IAAI;IAEjD,gBAAgB,GAAA;;;QAGjC,KAAK,CAAC,gBAAgB,EAAE;;;;;;;QAQxB,IAAI,CAAC,mBAAmB,EAAE;;;IAIpB,mBAAmB,GAAA;AACzB,QAAA,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,EAAC,KAAK,EAAE,SAAS,EAAE,SAAS,EAAE,IAAI,CAAC,uBAAuB,EAAC,CAAC;AAE7F,QAAA,IAAI,IAAI,CAAC,kBAAkB,EAAE;AAC3B,YAAA,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,WAAW,CACjC,4BAA4B,EAC5B,GAAG,IAAI,CAAC,uBAAuB,CAAA,EAAA,CAAI,CACpC;;;;AAKD,YAAA,IAAI,CAAC,sBAAsB,CAAC,MAAM,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,GAAG,CAAC,aAAa,EAAE,UAAU,CAAC,CAAC;YAC7F,IAAI,CAAC,2BAA2B,CAAC,IAAI,CAAC,uBAAuB,EAAE,IAAI,CAAC,iBAAiB,CAAC;;aACjF;YACL,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,GAAG,CAAC,UAAU,CAAC;;;;;AAK3C,YAAA,OAAO,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAC;;;AAI1D;;;AAGG;IACH,mBAAmB,GAAA;AACjB,QAAA,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,EAAC,KAAK,EAAE,SAAS,EAAE,SAAS,EAAE,IAAI,CAAC,sBAAsB,EAAC,CAAC;QAC5F,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,MAAM,CAAC,UAAU,CAAC;AAE9C,QAAA,IAAI,IAAI,CAAC,kBAAkB,EAAE;AAC3B,YAAA,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,WAAW,CACjC,4BAA4B,EAC5B,GAAG,IAAI,CAAC,sBAAsB,CAAA,EAAA,CAAI,CACnC;;AAGD,YAAA,IAAI,CAAC,sBAAsB,CAAC,MAAM,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;YACjF,IAAI,CAAC,2BAA2B,CAAC,IAAI,CAAC,sBAAsB,EAAE,IAAI,CAAC,kBAAkB,CAAC;;aACjF;;;;;;;;;;;;;;;;;;AAkBL,YAAA,OAAO,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,kBAAkB,EAAE,CAAC;;;AAI3D;;;AAGG;AACH,IAAA,yBAAyB,CAAC,KAAa,EAAA;AACrC,QAAA,IAAI,CAAC,mBAAmB,IAAI,KAAK;AACjC,QAAA,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE;;AAGxC;;;AAGG;IACK,iBAAiB,GAAG,MAAK;QAC/B,IAAI,CAAC,sBAAsB,EAAE;AAC7B,QAAA,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,uBAAuB,CAAC;AACvD,KAAC;AAED;;;AAGG;IACK,kBAAkB,GAAG,MAAK;QAChC,IAAI,CAAC,sBAAsB,EAAE;AAC7B,QAAA,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,EAAC,KAAK,EAAE,QAAQ,EAAE,SAAS,EAAE,IAAI,CAAC,sBAAsB,EAAC,CAAC;AAC7F,KAAC;;IAGO,sBAAsB,GAAA;QAC5B,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,MAAM,CAAC,aAAa,EAAE,aAAa,CAAC;;IAG1D,2BAA2B,CAAC,QAAgB,EAAE,QAAoB,EAAA;AACxE,QAAA,IAAI,IAAI,CAAC,eAAe,KAAK,IAAI,EAAE;AACjC,YAAA,YAAY,CAAC,IAAI,CAAC,eAAe,CAAC;;;;QAKpC,IAAI,CAAC,eAAe,GAAG,UAAU,CAAC,QAAQ,EAAE,QAAQ,CAAC;;;AAI/C,IAAA,sBAAsB,CAAC,QAAoB,EAAA;AACjD,QAAA,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,MAAK;AAClC,YAAA,IAAI,OAAO,qBAAqB,KAAK,UAAU,EAAE;gBAC/C,qBAAqB,CAAC,QAAQ,CAAC;;iBAC1B;AACL,gBAAA,QAAQ,EAAE;;AAEd,SAAC,CAAC;;IAGe,oBAAoB,GAAA;AACrC,QAAA,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE;YAChC,IAAI,CAAC,UAAU,EAAE;;;AAIrB;;;AAGG;AACO,IAAA,kBAAkB,CAAC,SAAiB,EAAA;AAC5C,QAAA,IAAI,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE;YAC/B,IAAI,CAAC,UAAU,EAAE;;AAGnB,QAAA,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,EAAC,KAAK,EAAE,QAAQ,EAAE,SAAS,EAAC,CAAC;;IAGvD,WAAW,GAAA;QAClB,KAAK,CAAC,WAAW,EAAE;AAEnB,QAAA,IAAI,IAAI,CAAC,eAAe,KAAK,IAAI,EAAE;AACjC,YAAA,YAAY,CAAC,IAAI,CAAC,eAAe,CAAC;;;AAI7B,IAAA,qBAAqB,CAAI,MAA0B,EAAA;;;;;;;;;QAS1D,MAAM,GAAG,GAAG,KAAK,CAAC,qBAAqB,CAAC,MAAM,CAAC;QAC/C,GAAG,CAAC,QAAQ,CAAC,aAAa,CAAC,SAAS,CAAC,GAAG,CAAC,+BAA+B,CAAC;AACzE,QAAA,OAAO,GAAG;;uGA/LD,kBAAkB,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAlB,kBAAkB,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,sBAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,UAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,iBAAA,EAAA,mBAAA,EAAA,IAAA,EAAA,YAAA,EAAA,WAAA,EAAA,cAAA,EAAA,sBAAA,EAAA,oDAAA,EAAA,iBAAA,EAAA,mBAAA,EAAA,uBAAA,EAAA,iCAAA,EAAA,+BAAA,EAAA,qBAAA,EAAA,6CAAA,EAAA,yBAAA,EAAA,EAAA,cAAA,EAAA,qCAAA,EAAA,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,QAAA,ECjE/B,6LAKA,EAAA,MAAA,EAAA,CAAA,wjKAAA,CAAA,EAAA,YAAA,EAAA,CAAA,EAAA,IAAA,EAAA,WAAA,EAAA,IAAA,ED8CY,eAAe,EAAA,QAAA,EAAA,mBAAA,EAAA,MAAA,EAAA,CAAA,iBAAA,CAAA,EAAA,OAAA,EAAA,CAAA,UAAA,CAAA,EAAA,QAAA,EAAA,CAAA,iBAAA,CAAA,EAAA,CAAA,EAAA,eAAA,EAAA,EAAA,CAAA,uBAAA,CAAA,OAAA,EAAA,aAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,IAAA,EAAA,CAAA;;2FAcd,kBAAkB,EAAA,UAAA,EAAA,CAAA;kBAtB9B,SAAS;AACE,YAAA,IAAA,EAAA,CAAA,EAAA,QAAA,EAAA,sBAAsB,EAGjB,aAAA,EAAA,iBAAiB,CAAC,IAAI,EAGpB,eAAA,EAAA,uBAAuB,CAAC,OAAO,EACvC,OAAA,EAAA,CAAC,eAAe,CAAC,EACpB,IAAA,EAAA;AACJ,wBAAA,OAAO,EAAE,qCAAqC;AAC9C,wBAAA,UAAU,EAAE,IAAI;AAChB,wBAAA,mBAAmB,EAAE,mBAAmB;AACxC,wBAAA,MAAM,EAAE,YAAY;AACpB,wBAAA,aAAa,EAAE,cAAc;AAC7B,wBAAA,wBAAwB,EAAE,oDAAoD;AAC9E,wBAAA,mBAAmB,EAAE,mBAAmB;AACxC,wBAAA,yBAAyB,EAAE,iCAAiC;AAC5D,wBAAA,iCAAiC,EAAE,qBAAqB;AACxD,wBAAA,+CAA+C,EAAE,yBAAyB;AAC3E,qBAAA,EAAA,QAAA,EAAA,6LAAA,EAAA,MAAA,EAAA,CAAA,wjKAAA,CAAA,EAAA;;AAqMH,MAAM,4BAA4B,GAAG,kCAAkC;AAEvE;AACA;AACA;;;AAGG;AACH,SAAS,YAAY,CAAC,IAAiC,EAAA;AACrD,IAAA,IAAI,IAAI,IAAI,IAAI,EAAE;AAChB,QAAA,OAAO,IAAI;;AAEb,IAAA,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;AAC5B,QAAA,OAAO,IAAI;;AAEb,IAAA,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;AACvB,QAAA,OAAO,oBAAoB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;;AAEjE,IAAA,IAAI,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;AACtB,QAAA,OAAO,oBAAoB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI;;AAExE,IAAA,IAAI,IAAI,KAAK,GAAG,EAAE;AAChB,QAAA,OAAO,CAAC;;IAEV,OAAO,IAAI,CAAC;AACd;;IE1QY;AAAZ,CAAA,UAAY,cAAc,EAAA;AACxB,IAAA,cAAA,CAAA,cAAA,CAAA,MAAA,CAAA,GAAA,CAAA,CAAA,GAAA,MAAI;AACJ,IAAA,cAAA,CAAA,cAAA,CAAA,SAAA,CAAA,GAAA,CAAA,CAAA,GAAA,SAAO;AACP,IAAA,cAAA,CAAA,cAAA,CAAA,QAAA,CAAA,GAAA,CAAA,CAAA,GAAA,QAAM;AACR,CAAC,EAJW,cAAc,KAAd,cAAc,GAIzB,EAAA,CAAA,CAAA;AAED;;AAEG;MACU,YAAY,CAAA;AAuCb,IAAA,IAAA;AACA,IAAA,OAAA;AACD,IAAA,kBAAA;;AAvCT,IAAA,iBAAiB;AAEjB;;;AAGG;AACM,IAAA,YAAY;;AAGrB,IAAA,YAAY;;AAGZ,IAAA,EAAE;;AAGe,IAAA,YAAY,GAAG,IAAI,OAAO,EAAQ;;AAGlC,IAAA,aAAa,GAAG,IAAI,OAAO,EAAiB;;AAGrD,IAAA,OAAO;;AAGP,IAAA,qBAAqB;;AAGrB,IAAA,MAAM,GAAG,cAAc,CAAC,IAAI;;;;;;AAO5B,IAAA,qBAAqB;AAE7B,IAAA,WAAA,CACU,IAAqB,EACrB,OAAwB,EACzB,kBAAsC,EAAA;QAFrC,IAAI,CAAA,IAAA,GAAJ,IAAI;QACJ,IAAO,CAAA,OAAA,GAAP,OAAO;QACR,IAAkB,CAAA,kBAAA,GAAlB,kBAAkB;AAEzB,QAAA,IAAI,CAAC,YAAY,GAAG,OAAO,CAAC,YAAY;AACxC,QAAA,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE;;AAGjB,QAAA,IAAI,CAAC,aAAa,CAAC,sBAAsB,CAAC;;AAG1C,QAAA,kBAAkB,CAAC;AAChB,aAAA,IAAI,CACH,MAAM,CAAC,KAAK,IAAI,KAAK,CAAC,KAAK,KAAK,QAAQ,CAAC,EACzC,IAAI,CAAC,CAAC,CAAC;aAER,SAAS,CAAC,MAAK;AACd,YAAA,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE;AACxB,YAAA,IAAI,CAAC,YAAY,CAAC,QAAQ,EAAE;AAC9B,SAAC,CAAC;;AAGJ,QAAA,kBAAkB,CAAC;AAChB,aAAA,IAAI,CACH,MAAM,CAAC,KAAK,IAAI,KAAK,CAAC,KAAK,KAAK,QAAQ,CAAC,EACzC,IAAI,CAAC,CAAC,CAAC;aAER,SAAS,CAAC,MAAK;AACd,YAAA,YAAY,CAAC,IAAI,CAAC,qBAAqB,CAAC;YACxC,IAAI,CAAC,kBAAkB,EAAE;AAC3B,SAAC,CAAC;QAEJ,IAAI,CAAC,UAAU,CAAC,WAAW,EAAE,CAAC,SAAS,CAAC,MAAK;YAC3C,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC;AACrC,YAAA,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE;YAC7B,IAAI,CAAC,kBAAkB,EAAE;AAC3B,SAAC,CAAC;AAEF,QAAA,KAAK,CACH,IAAI,CAAC,aAAa,EAAE,EACpB,IAAI,CAAC,aAAa,EAAE,CAAC,IAAI,CACvB,MAAM,CAAC,KAAK,IAAI,KAAK,CAAC,OAAO,KAAK,MAAM,IAAI,CAAC,IAAI,CAAC,YAAY,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,CAC1F,CACF,CAAC,SAAS,CAAC,KAAK,IAAG;AAClB,YAAA,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;gBACtB,KAAK,CAAC,cAAc,EAAE;AACtB,gBAAA,eAAe,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,KAAK,SAAS,GAAG,UAAU,GAAG,OAAO,CAAC;;AAE1E,SAAC,CAAC;;AAGJ;;;AAGG;AACH,IAAA,KAAK,CAAC,YAAgB,EAAA;AACpB,QAAA,MAAM,cAAc,GAAG,IAAI,CAAC,OAAO,CAAC,cAAc;AAElD,QAAA,IAAI,cAAc,IAAI,CAAC,cAAc,CAAC,YAAY,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,iBAAiB,CAAC,EAAE;YACzF;;AAGF,QAAA,IAAI,CAAC,OAAO,GAAG,YAAY;;QAG3B,IAAI,CAAC,kBAAkB,CAAC;AACrB,aAAA,IAAI,CACH,MAAM,CAAC,KAAK,IAAI,KAAK,CAAC,KAAK,KAAK,SAAS,CAAC,EAC1C,IAAI,CAAC,CAAC,CAAC;aAER,SAAS,CAAC,KAAK,IAAG;AACjB,YAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,YAAY,CAAC;AACrC,YAAA,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE;AAC7B,YAAA,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,EAAE;;;;;;AAOrC,YAAA,IAAI,CAAC,qBAAqB,GAAG,UAAU,CACrC,MAAM,IAAI,CAAC,kBAAkB,EAAE,EAC/B,KAAK,CAAC,SAAS,GAAG,GAAG,CACtB;AACH,SAAC,CAAC;AAEJ,QAAA,IAAI,CAAC,MAAM,GAAG,cAAc,CAAC,OAAO;AACpC,QAAA,IAAI,CAAC,kBAAkB,CAAC,mBAAmB,EAAE;;AAG/C;;AAEG;IACH,WAAW,GAAA;QACT,OAAO,IAAI,CAAC,YAAY;;AAG1B;;AAEG;IACH,WAAW,GAAA;AACT,QAAA,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM;;AAGzB;;AAEG;IACH,YAAY,GAAA;QACV,OAAO,IAAI,CAAC,aAAa;;AAG3B;;AAEG;IACH,aAAa,GAAA;AACX,QAAA,OAAO,IAAI,CAAC,IAAI,CAAC,aAAa;;AAGhC;;AAEG;IACH,aAAa,GAAA;AACX,QAAA,OAAO,IAAI,CAAC,IAAI,CAAC,aAAa;;AAGhC;;;AAGG;AACH,IAAA,cAAc,CAAC,QAAyB,EAAA;QACtC,IAAI,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,gBAA0C;AAE1E,QAAA,IAAI,QAAQ,KAAK,QAAQ,CAAC,IAAI,IAAI,QAAQ,CAAC,KAAK,CAAC,EAAE;YACjD,QAAQ,CAAC,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC;;aACxE;YACL,QAAQ,CAAC,kBAAkB,EAAE;;AAG/B,QAAA,IAAI,QAAQ,KAAK,QAAQ,CAAC,GAAG,IAAI,QAAQ,CAAC,MAAM,CAAC,EAAE;YACjD,QAAQ,CAAC,GAAG,GAAG,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;;aACvE;YACL,QAAQ,CAAC,gBAAgB,EAAE;;AAG7B,QAAA,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;AAE1B,QAAA,OAAO,IAAI;;AAGb;;;;AAIG;AACH,IAAA,UAAU,CAAC,KAAA,GAAgB,EAAE,EAAE,SAAiB,EAAE,EAAA;QAChD,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,MAAM,CAAC;AACnC,QAAA,OAAO,IAAI;;;AAIb,IAAA,aAAa,CAAC,OAA0B,EAAA;AACtC,QAAA,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC;AAChC,QAAA,OAAO,IAAI;;;AAIb,IAAA,gBAAgB,CAAC,OAA0B,EAAA;AACzC,QAAA,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC;AACnC,QAAA,OAAO,IAAI;;;IAIb,QAAQ,GAAA;QACN,OAAO,IAAI,CAAC,MAAM;;AAGpB;;;AAGG;IACK,kBAAkB,GAAA;AACxB,QAAA,IAAI,CAAC,MAAM,GAAG,cAAc,CAAC,MAAM;AACnC,QAAA,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE,EAAC,WAAW,EAAE,IAAI,CAAC,qBAAqB,EAAC,CAAC;AACxE,QAAA,IAAI,CAAC,iBAAiB,GAAG,IAAK;;AAEjC;AAED;;;;AAIG;AACH;SACgB,eAAe,CAAI,GAAoB,EAAE,eAA4B,EAAE,MAAU,EAAA;AAC9F,IAAA,GAAuD,CAAC,qBAAqB,GAAG,eAAe;AAChG,IAAA,OAAO,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC;AAC1B;;ACtOA;MACa,eAAe,GAAG,IAAI,cAAc,CAAM,kBAAkB;AAEzE;MACa,0BAA0B,GAAG,IAAI,cAAc,CAC1D,gCAAgC;AAGlC;MACa,0BAA0B,GAAG,IAAI,cAAc,CAC1D,gCAAgC,EAChC;AACE,IAAA,UAAU,EAAE,MAAM;IAClB,OAAO,EAAE,MAAK;AACZ,QAAA,MAAM,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC;AACjC,QAAA,OAAO,MAAM,yBAAyB,CAAC,QAAQ,CAAC;KACjD;AACF,CAAA;AAGH;;AAEG;MAEU,SAAS,CAAA;IACZ,eAAe,GAAG,MAAM,CAAkB,0BAA0B,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC;AACvF,IAAA,eAAe,GAAG,MAAM,CAAC,0BAA0B,CAAC;AACpD,IAAA,aAAa,GAAG,MAAM,CAAC,SAAS,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAC,CAAC;AACnE,IAAA,YAAY,GAAG,MAAM,CAAC,YAAY,CAAC;AACnC,IAAA,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC;AAC1B,IAAA,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC;IAC1B,mBAAmB,GAAG,mBAAmB,EAAE;IAElC,uBAAuB,GAAwB,EAAE;AACjD,IAAA,0BAA0B,GAAG,IAAI,OAAO,EAAQ;AAChD,IAAA,uBAAuB,GAAG,IAAI,OAAO,EAAqB;IACjE,iBAAiB,GAAG,eAAe;AAE5B,IAAA,qBAAqB;AACrB,IAAA,oBAAoB;AACpB,IAAA,gBAAgB;;AAGjC,IAAA,IAAI,WAAW,GAAA;AACb,QAAA,OAAO,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,WAAW,GAAG,IAAI,CAAC,uBAAuB;;;AAI3F,IAAA,IAAI,WAAW,GAAA;AACb,QAAA,OAAO,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,WAAW,GAAG,IAAI,CAAC,uBAAuB;;IAGnF,kBAAkB,GAAA;AACxB,QAAA,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa;AACjC,QAAA,OAAO,MAAM,GAAG,MAAM,CAAC,kBAAkB,EAAE,GAAG,IAAI,CAAC,0BAA0B;;AAG/E;;;AAGG;IACM,cAAc,GAAqB,KAAK,CAAC,MAChD,IAAI,CAAC,WAAW,CAAC;AACf,UAAE,IAAI,CAAC,kBAAkB;AACzB,UAAE,IAAI,CAAC,kBAAkB,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,CACtC;AAIpB,IAAA,WAAA,GAAA;AACE,QAAA,IAAI,CAAC,qBAAqB,GAAG,YAAY;AACzC,QAAA,IAAI,CAAC,oBAAoB,GAAG,kBAAkB;AAC9C,QAAA,IAAI,CAAC,gBAAgB,GAAG,eAAe;;IA8BzC,IAAI,CACF,sBAAyD,EACzD,MAA2B,EAAA;AAE3B,QAAA,IAAI,SAA6B;AACjC,QAAA,MAAM,GAAG,EAAC,IAAI,IAAI,CAAC,eAAe,IAAI,IAAI,eAAe,EAAE,CAAC,EAAE,GAAG,MAAM,EAAC;AACxE,QAAA,MAAM,CAAC,EAAE,GAAG,MAAM,CAAC,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,iBAAiB,CAAC;QACnE,MAAM,CAAC,cAAc,GAAG,MAAM,CAAC,cAAc,IAAI,IAAI,CAAC,eAAe,EAAE;QAEvE,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAU,sBAAsB,EAAE;AAChE,YAAA,GAAG,MAAM;AACT,YAAA,gBAAgB,EAAE,4BAA4B,CAAC,IAAI,CAAC,SAAS;AAC1D,iBAAA,kBAAkB;AAClB,iBAAA,gBAAgB,EAAE;;AAErB,YAAA,YAAY,EAAE,IAAI;;AAElB,YAAA,cAAc,EAAE,SAAS;;;;AAIzB,YAAA,cAAc,EAAE,KAAK;;;AAGrB,YAAA,yBAAyB,EAAE,KAAK;YAChC,iBAAiB,EACf,IAAI,CAAC,mBAAmB;AACxB,gBAAA,MAAM,CAAC,sBAAsB,EAAE,cAAc,EAAE,KAAK,GAAG;AACvD,gBAAA,MAAM,CAAC,qBAAqB,EAAE,QAAQ,EAAE,KAAK,GAAG;AAClD,YAAA,SAAS,EAAE;gBACT,IAAI,EAAE,IAAI,CAAC,oBAAoB;gBAC/B,SAAS,EAAE,MAAM;;;;oBAIf,EAAC,OAAO,EAAE,IAAI,CAAC,iBAAiB,EAAE,QAAQ,EAAE,MAAM,EAAC;AACnD,oBAAA,EAAC,OAAO,EAAE,YAAY,EAAE,QAAQ,EAAE,MAAM,EAAC;AAC1C,iBAAA;AACF,aAAA;YACD,eAAe,EAAE,OAAO,EAAC,SAAS,EAAC,CAAC;YACpC,SAAS,EAAE,CAAC,GAAG,EAAE,SAAS,EAAE,eAAe,KAAI;AAC7C,gBAAA,SAAS,GAAG,IAAI,IAAI,CAAC,qBAAqB,CAAC,GAAG,EAAE,MAAM,EAAE,eAAe,CAAC;AACxE,gBAAA,SAAS,CAAC,cAAc,CAAC,MAAM,EAAE,QAAQ,CAAC;gBAC1C,OAAO;oBACL,EAAC,OAAO,EAAE,IAAI,CAAC,oBAAoB,EAAE,QAAQ,EAAE,eAAe,EAAC;oBAC/D,EAAC,OAAO,EAAE,IAAI,CAAC,gBAAgB,EAAE,QAAQ,EAAE,SAAS,CAAC,IAAI,EAAC;oBAC1D,EAAC,OAAO,EAAE,IAAI,CAAC,qBAAqB,EAAE,QAAQ,EAAE,SAAS,EAAC;iBAC3D;aACF;AACF,SAAA,CAAC;;;AAID,QAAA,SAA8C,CAAC,YAAY,GAAG,MAAM,CAAC,YAAa;AACnF,QAAA,SAAU,CAAC,iBAAiB,GAAG,MAAM,CAAC,iBAAkB;AAExD,QAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,SAAU,CAAC;AACjC,QAAA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,SAAU,CAAC;AAEjC,QAAA,SAAU,CAAC,WAAW,EAAE,CAAC,SAAS,CAAC,MAAK;YACtC,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,SAAS,CAAC;AAEjD,YAAA,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE;gBACd,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC;AAEjC,gBAAA,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE;AAC5B,oBAAA,IAAI,CAAC,kBAAkB,EAAE,CAAC,IAAI,EAAE;;;AAGtC,SAAC,CAAC;AAEF,QAAA,OAAO,SAAU;;AAGnB;;AAEG;IACH,QAAQ,GAAA;AACN,QAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,WAAW,CAAC;;AAGtC;;;AAGG;AACH,IAAA,aAAa,CAAC,EAAU,EAAA;AACtB,QAAA,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,EAAE,KAAK,EAAE,CAAC;;IAG1D,WAAW,GAAA;;;AAGT,QAAA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,uBAAuB,CAAC;AAChD,QAAA,IAAI,CAAC,0BAA0B,CAAC,QAAQ,EAAE;AAC1C,QAAA,IAAI,CAAC,uBAAuB,CAAC,QAAQ,EAAE;;AAGjC,IAAA,aAAa,CAAC,OAA4B,EAAA;AAChD,QAAA,IAAI,CAAC,GAAG,OAAO,CAAC,MAAM;QAEtB,OAAO,CAAC,EAAE,EAAE;AACV,YAAA,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE;;;uGAnLX,SAAS,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA;AAAT,IAAA,OAAA,KAAA,GAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,SAAS,cADG,MAAM,EAAA,CAAA;;2FAClB,SAAS,EAAA,UAAA,EAAA,CAAA;kBADrB,UAAU;mBAAC,EAAC,UAAU,EAAE,MAAM,EAAC;;;AChChC;;AAEG;MAUU,cAAc,CAAA;IACzB,SAAS,GAAG,MAAM,CAAoB,YAAY,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAE;AAC9D,IAAA,WAAW,GAAG,MAAM,CAA0B,UAAU,CAAC;AACzD,IAAA,OAAO,GAAG,MAAM,CAAC,SAAS,CAAC;;AAGd,IAAA,SAAS;;IAGrB,IAAI,GAAkC,QAAQ;;AAG5B,IAAA,YAAY;AAEd,IAAA,eAAe;AAGxC,IAAA,WAAA,GAAA;IAEA,QAAQ,GAAA;AACN,QAAA,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;;;;;;AAMnB,YAAA,IAAI,CAAC,SAAS,GAAG,gBAAgB,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,OAAO,CAAC,WAAW,CAAE;;;AAIlF,IAAA,WAAW,CAAC,OAAsB,EAAA;QAChC,MAAM,aAAa,GAAG,OAAO,CAAC,iBAAiB,CAAC,IAAI,OAAO,CAAC,uBAAuB,CAAC;QAEpF,IAAI,aAAa,EAAE;AACjB,YAAA,IAAI,CAAC,YAAY,GAAG,aAAa,CAAC,YAAY;;;AAIlD,IAAA,cAAc,CAAC,KAAiB,EAAA;;;;;AAK9B,QAAA,eAAe,CACb,IAAI,CAAC,SAAS,EACd,KAAK,CAAC,OAAO,KAAK,CAAC,IAAI,KAAK,CAAC,OAAO,KAAK,CAAC,GAAG,UAAU,GAAG,OAAO,EACjE,IAAI,CAAC,YAAY,CAClB;;uGA/CQ,cAAc,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAd,cAAc,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,sCAAA,EAAA,MAAA,EAAA,EAAA,SAAA,EAAA,CAAA,YAAA,EAAA,WAAA,CAAA,EAAA,IAAA,EAAA,MAAA,EAAA,YAAA,EAAA,CAAA,kBAAA,EAAA,cAAA,CAAA,EAAA,eAAA,EAAA,CAAA,gBAAA,EAAA,iBAAA,CAAA,EAAA,EAAA,IAAA,EAAA,EAAA,SAAA,EAAA,EAAA,OAAA,EAAA,wBAAA,EAAA,EAAA,UAAA,EAAA,EAAA,iBAAA,EAAA,mBAAA,EAAA,WAAA,EAAA,MAAA,EAAA,EAAA,EAAA,QAAA,EAAA,CAAA,gBAAA,CAAA,EAAA,aAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAAd,cAAc,EAAA,UAAA,EAAA,CAAA;kBAT1B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,sCAAsC;AAChD,oBAAA,QAAQ,EAAE,gBAAgB;AAC1B,oBAAA,IAAI,EAAE;AACJ,wBAAA,SAAS,EAAE,wBAAwB;AACnC,wBAAA,mBAAmB,EAAE,mBAAmB;AACxC,wBAAA,aAAa,EAAE,MAAM;AACtB,qBAAA;AACF,iBAAA;wDAOsB,SAAS,EAAA,CAAA;sBAA7B,KAAK;uBAAC,YAAY;gBAGV,IAAI,EAAA,CAAA;sBAAZ;gBAG0B,YAAY,EAAA,CAAA;sBAAtC,KAAK;uBAAC,kBAAkB;gBAEA,eAAe,EAAA,CAAA;sBAAvC,KAAK;uBAAC,gBAAgB;;MAsCH,sBAAsB,CAAA;IAChC,UAAU,GAAG,MAAM,CAAoB,YAAY,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAE;AACzE,IAAA,WAAW,GAAG,MAAM,CAA0B,UAAU,CAAC;AACzD,IAAA,OAAO,GAAG,MAAM,CAAC,SAAS,CAAC;AAInC,IAAA,WAAA,GAAA;IAKA,QAAQ,GAAA;AACN,QAAA,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;AACpB,YAAA,IAAI,CAAC,UAAU,GAAG,gBAAgB,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,OAAO,CAAC,WAAW,CAAE;;AAGjF,QAAA,IAAI,IAAI,CAAC,UAAU,EAAE;AACnB,YAAA,OAAO,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,MAAK;gBAC1B,IAAI,CAAC,MAAM,EAAE;AACf,aAAC,CAAC;;;IAIN,WAAW,GAAA;;;AAGT,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAU,EAAE,kBAAkB;QAEpD,IAAI,QAAQ,EAAE;AACZ,YAAA,OAAO,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,MAAK;gBAC1B,IAAI,CAAC,SAAS,EAAE;AAClB,aAAC,CAAC;;;uGAhCc,sBAAsB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAtB,sBAAsB,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAAtB,sBAAsB,EAAA,UAAA,EAAA,CAAA;kBAD3C;;AAsCD;;AAEG;AASG,MAAO,cAAe,SAAQ,sBAAsB,CAAA;IAC/C,EAAE,GAAW,MAAM,CAAC,YAAY,CAAC,CAAC,KAAK,CAAC,uBAAuB,CAAC;IAE/D,MAAM,GAAA;;;AAGd,QAAA,IAAI,CAAC,UAAU,CAAC,kBAAkB,EAAE,kBAAkB,GAAG,IAAI,CAAC,EAAE,CAAC;;IAGhD,SAAS,GAAA;AAC1B,QAAA,IAAI,CAAC,UAAU,EAAE,kBAAkB,EAAE,qBAAqB,GAAG,IAAI,CAAC,EAAE,CAAC;;uGAV5D,cAAc,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAd,cAAc,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,sCAAA,EAAA,MAAA,EAAA,EAAA,EAAA,EAAA,IAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,IAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,wCAAA,EAAA,EAAA,QAAA,EAAA,CAAA,gBAAA,CAAA,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAAd,cAAc,EAAA,UAAA,EAAA,CAAA;kBAR1B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,sCAAsC;AAChD,oBAAA,QAAQ,EAAE,gBAAgB;AAC1B,oBAAA,IAAI,EAAE;AACJ,wBAAA,OAAO,EAAE,wCAAwC;AACjD,wBAAA,MAAM,EAAE,IAAI;AACb,qBAAA;AACF,iBAAA;8BAEU,EAAE,EAAA,CAAA;sBAAV;;AAaH;;AAEG;MAMU,gBAAgB,CAAA;uGAAhB,gBAAgB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAhB,gBAAgB,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,8DAAA,EAAA,IAAA,EAAA,EAAA,cAAA,EAAA,4CAAA,EAAA,EAAA,cAAA,EAAA,CAAA,EAAA,SAAA,EAAA,EAAA,CAAA,aAAA,EAAA,CAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAAhB,gBAAgB,EAAA,UAAA,EAAA,CAAA;kBAL5B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,CAA8D,4DAAA,CAAA;AACxE,oBAAA,IAAI,EAAE,EAAC,OAAO,EAAE,4CAA4C,EAAC;oBAC7D,cAAc,EAAE,CAAC,aAAa,CAAC;AAChC,iBAAA;;AAGD;;;AAGG;AAUG,MAAO,gBAAiB,SAAQ,sBAAsB,CAAA;AAC1D;;AAEG;AACM,IAAA,KAAK;IAEJ,MAAM,GAAA;QACd,IAAI,CAAC,UAAU,CAAC,kBAAkB,EAAE,yBAAyB,GAAG,CAAC,CAAC;;IAGjD,SAAS,GAAA;QAC1B,IAAI,CAAC,UAAU,CAAC,kBAAkB,EAAE,yBAAyB,GAAG,CAAC,CAAC,CAAC;;uGAX1D,gBAAgB,EAAA,IAAA,EAAA,IAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,SAAA,EAAA,CAAA;2FAAhB,gBAAgB,EAAA,YAAA,EAAA,IAAA,EAAA,QAAA,EAAA,8DAAA,EAAA,MAAA,EAAA,EAAA,KAAA,EAAA,OAAA,EAAA,EAAA,IAAA,EAAA,EAAA,UAAA,EAAA,EAAA,0CAAA,EAAA,qBAAA,EAAA,2CAAA,EAAA,sBAAA,EAAA,wCAAA,EAAA,mBAAA,EAAA,EAAA,cAAA,EAAA,4CAAA,EAAA,EAAA,eAAA,EAAA,IAAA,EAAA,QAAA,EAAA,EAAA,EAAA,CAAA;;2FAAhB,gBAAgB,EAAA,UAAA,EAAA,CAAA;kBAT5B,SAAS;AAAC,YAAA,IAAA,EAAA,CAAA;AACT,oBAAA,QAAQ,EAAE,CAA8D,4DAAA,CAAA;AACxE,oBAAA,IAAI,EAAE;AACJ,wBAAA,OAAO,EAAE,4CAA4C;AACrD,wBAAA,4CAA4C,EAAE,mBAAmB;AACjE,wBAAA,6CAA6C,EAAE,oBAAoB;AACnE,wBAAA,0CAA0C,EAAE,iBAAiB;AAC9D,qBAAA;AACF,iBAAA;8BAKU,KAAK,EAAA,CAAA;sBAAb;;AAWH;;;;AAIG;AACH,SAAS,gBAAgB,CAAC,OAAgC,EAAE,WAAgC,EAAA;AAC1F,IAAA,IAAI,MAAM,GAAuB,OAAO,CAAC,aAAa,CAAC,aAAa;AAEpE,IAAA,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,0BAA0B,CAAC,EAAE;AACvE,QAAA,MAAM,GAAG,MAAM,CAAC,aAAa;;IAG/B,OAAO,MAAM,GAAG,WAAW,CAAC,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,EAAE,KAAK,MAAO,CAAC,EAAE,CAAC,GAAG,IAAI;AAC7E;;ACnLA,MAAM,UAAU,GAAG;IACjB,kBAAkB;IAClB,cAAc;IACd,cAAc;IACd,gBAAgB;IAChB,gBAAgB;CACjB;MAOY,eAAe,CAAA;uGAAf,eAAe,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA;wGAAf,eAAe,EAAA,OAAA,EAAA,CAJhB,YAAY,EAAE,aAAa,EAAE,YAAY,EAAE,eAAe,EARpE,kBAAkB;YAClB,cAAc;YACd,cAAc;YACd,gBAAgB;YAChB,gBAAgB,CAAA,EAAA,OAAA,EAAA,CAKN,eAAe,EATzB,kBAAkB;YAClB,cAAc;YACd,cAAc;YACd,gBAAgB;YAChB,gBAAgB,CAAA,EAAA,CAAA;AAQL,IAAA,OAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,QAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,eAAe,EAFf,SAAA,EAAA,CAAC,SAAS,CAAC,EAFZ,OAAA,EAAA,CAAA,YAAY,EAAE,aAAa,EAAE,YAAY,EAAE,eAAe,EAC1D,eAAe,CAAA,EAAA,CAAA;;2FAGd,eAAe,EAAA,UAAA,EAAA,CAAA;kBAL3B,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACR,oBAAA,OAAO,EAAE,CAAC,YAAY,EAAE,aAAa,EAAE,YAAY,EAAE,eAAe,EAAE,GAAG,UAAU,CAAC;AACpF,oBAAA,OAAO,EAAE,CAAC,eAAe,EAAE,GAAG,UAAU,CAAC;oBACzC,SAAS,EAAE,CAAC,SAAS,CAAC;AACvB,iBAAA;;;;;"}