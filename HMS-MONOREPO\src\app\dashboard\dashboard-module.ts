import { NgModule } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { SharedModule } from '../shared/shared-module';
import { DashboardRoutingModule } from './dashboard-routing.module';
import { Dashboard } from './dashboard/dashboard';

@NgModule({
  declarations: [
    Dashboard
  ],
  imports: [
    SharedModule,
    DashboardRoutingModule,
    MatButtonModule,
    MatIconModule,
    MatCardModule,
    MatProgressSpinnerModule
  ]
})
export class DashboardModule { }
