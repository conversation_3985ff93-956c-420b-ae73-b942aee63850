{"version": 3, "file": "activedescendant-key-manager-CZAE5aFC.mjs", "sources": ["../../../../../darwin_arm64-fastbuild-ST-46c76129e412/bin/src/cdk/a11y/key-manager/activedescendant-key-manager.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nimport {ListKeyManager, ListKeyManagerOption} from './list-key-manager';\n\n/**\n * This is the interface for highlightable items (used by the ActiveDescendantKeyManager).\n * Each item must know how to style itself as active or inactive and whether or not it is\n * currently disabled.\n */\nexport interface Highlightable extends ListKeyManagerOption {\n  /** Applies the styles for an active item to this item. */\n  setActiveStyles(): void;\n\n  /** Applies the styles for an inactive item to this item. */\n  setInactiveStyles(): void;\n}\n\nexport class ActiveDescendantKeyManager<T> extends ListKeyManager<Highlightable & T> {\n  /**\n   * Sets the active item to the item at the specified index and adds the\n   * active styles to the newly active item. Also removes active styles\n   * from the previously active item.\n   * @param index Index of the item to be set as active.\n   */\n  override setActiveItem(index: number): void;\n\n  /**\n   * Sets the active item to the item to the specified one and adds the\n   * active styles to the it. Also removes active styles from the\n   * previously active item.\n   * @param item Item to be set as active.\n   */\n  override setActiveItem(item: T): void;\n\n  override setActiveItem(index: any): void {\n    if (this.activeItem) {\n      this.activeItem.setInactiveStyles();\n    }\n    super.setActiveItem(index);\n    if (this.activeItem) {\n      this.activeItem.setActiveStyles();\n    }\n  }\n}\n"], "names": [], "mappings": ";;AAuBM,MAAO,0BAA8B,SAAQ,cAAiC,CAAA;AAiBzE,IAAA,aAAa,CAAC,KAAU,EAAA;AAC/B,QAAA,IAAI,IAAI,CAAC,UAAU,EAAE;AACnB,YAAA,IAAI,CAAC,UAAU,CAAC,iBAAiB,EAAE;;AAErC,QAAA,KAAK,CAAC,aAAa,CAAC,KAAK,CAAC;AAC1B,QAAA,IAAI,IAAI,CAAC,UAAU,EAAE;AACnB,YAAA,IAAI,CAAC,UAAU,CAAC,eAAe,EAAE;;;AAGtC;;;;"}