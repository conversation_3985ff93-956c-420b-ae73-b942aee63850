import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { MaterialModule } from './material.module';
import { HasPermissionDirective } from './directives/has-permission.directive';
import { UnauthorizedComponent } from './components/unauthorized.component';

@NgModule({
  declarations: [
    HasPermissionDirective,
    UnauthorizedComponent
  ],
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    RouterModule,
    MaterialModule
  ],
  exports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    RouterModule,
    MaterialModule,
    HasPermissionDirective,
    UnauthorizedComponent
  ]
})
export class SharedModule { }
