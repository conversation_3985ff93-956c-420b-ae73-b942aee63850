const express = require('express');
const Doctor = require('../models/Doctor');
const { authenticateToken, requirePermissions, authorizeRoles } = require('../middlewares/auth');
const router = express.Router();

// Apply authentication to all routes
router.use(authenticateToken);

// Get all doctors
router.get('/', async (req, res) => {
  try {
    const { page = 1, limit = 10, search = '', department = '', specialization = '' } = req.query;

    let query = { isActive: true };

    if (search) {
      query.$or = [
        { firstName: { $regex: search, $options: 'i' } },
        { lastName: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } },
        { doctorId: { $regex: search, $options: 'i' } }
      ];
    }

    if (department) {
      query.department = { $regex: department, $options: 'i' };
    }

    if (specialization) {
      query.specialization = { $regex: specialization, $options: 'i' };
    }

    const doctors = await Doctor.find(query)
      .limit(limit * 1)
      .skip((page - 1) * limit)
      .sort({ createdAt: -1 });

    const total = await Doctor.countDocuments(query);

    res.json({
      doctors,
      totalPages: Math.ceil(total / limit),
      currentPage: page,
      total
    });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

// Get doctor by ID
router.get('/:id', async (req, res) => {
  try {
    const doctor = await Doctor.findById(req.params.id);
    if (!doctor) {
      return res.status(404).json({ message: 'Doctor not found' });
    }
    res.json(doctor);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

// Create new doctor (Admin only)
router.post('/', authorizeRoles('Admin'), async (req, res) => {
  try {
    const doctor = new Doctor(req.body);
    await doctor.save();
    res.status(201).json({
      message: 'Doctor created successfully',
      doctor
    });
  } catch (error) {
    if (error.code === 11000) {
      res.status(400).json({ message: 'Doctor with this email or license number already exists' });
    } else {
      res.status(400).json({ message: error.message });
    }
  }
});

// Update doctor (Admin only)
router.put('/:id', authorizeRoles('Admin'), async (req, res) => {
  try {
    const doctor = await Doctor.findByIdAndUpdate(
      req.params.id,
      req.body,
      { new: true, runValidators: true }
    );

    if (!doctor) {
      return res.status(404).json({ message: 'Doctor not found' });
    }

    res.json({
      message: 'Doctor updated successfully',
      doctor
    });
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
});

// Delete doctor (soft delete) - Admin only
router.delete('/:id', authorizeRoles('Admin'), async (req, res) => {
  try {
    const doctor = await Doctor.findByIdAndUpdate(
      req.params.id,
      { isActive: false },
      { new: true }
    );

    if (!doctor) {
      return res.status(404).json({ message: 'Doctor not found' });
    }

    res.json({ message: 'Doctor deactivated successfully' });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

// Get available time slots for a doctor
router.get('/:id/available-slots', async (req, res) => {
  try {
    const { date } = req.query;
    const doctor = await Doctor.findById(req.params.id).select('availableSlots');

    if (!doctor) {
      return res.status(404).json({ message: 'Doctor not found' });
    }

    // Here you would implement logic to check available slots for a specific date
    // considering existing appointments
    res.json(doctor.availableSlots);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

// Get doctors by department
router.get('/department/:department', async (req, res) => {
  try {
    const doctors = await Doctor.find({
      department: { $regex: req.params.department, $options: 'i' },
      isActive: true
    }).select('firstName lastName specialization consultationFee');

    res.json(doctors);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

// Get unique departments
router.get('/meta/departments', async (req, res) => {
  try {
    const departments = await Doctor.distinct('department', { isActive: true });
    res.json(departments);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

// Get unique specializations
router.get('/meta/specializations', async (req, res) => {
  try {
    const specializations = await Doctor.distinct('specialization', { isActive: true });
    res.json(specializations);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

module.exports = router;
